#include "ui_sigma.h"

#include "zeal.h"
#include "sigma.h"
#include "ui_manager.h"
#include "game_functions.h"

ui_sigma::ui_sigma(ZealService *zeal, UIManager *mgr) 
    : zeal_service(zeal), ui_manager(mgr), sigma(nullptr) {
  // Get reference to the Sigma module
  sigma = zeal_service->sigma.get();
  
  // Register for UI initialization callback
  zeal->callbacks->AddGeneric([this]() { InitUI(); }, callback_type::InitUI);
}

ui_sigma::~ui_sigma() {
  // Cleanup
}

void ui_sigma::InitUI() {
  // Initialize the Sigma tab UI components
  InitTradeskillOptions();
  
  // Sync the current state of all options
  SyncTradeskillOptions();
}

void ui_sigma::InitTradeskillOptions() {
  if (!ui_manager || !ui_manager->options || !ui_manager->options->wnd) {
    return;
  }

  // Set up the tradeskill Alt+Right-click checkbox callback
  ui_manager->AddCheckboxCallback(ui_manager->options->wnd, "Sigma_TradeskillAltRightClick",
                                  [this](Zeal::GameUI::BasicWnd *wnd) {
    if (sigma) {
      sigma->tradeskill_alt_rightclick_enabled.set(wnd->Checked);
    }
  });

  // Set up the tradeskill show messages checkbox callback
  ui_manager->AddCheckboxCallback(ui_manager->options->wnd, "Sigma_TradeskillShowMessages",
                                  [this](Zeal::GameUI::BasicWnd *wnd) {
    if (sigma) {
      sigma->tradeskill_show_messages.set(wnd->Checked);
    }
  });
}

void ui_sigma::SyncTradeskillOptions() {
  if (!ui_manager || !ui_manager->options || !ui_manager->options->wnd || !sigma) {
    return;
  }

  // Sync the tradeskill Alt+Right-click checkbox
  Zeal::GameUI::BasicWnd *checkbox = ui_manager->options->wnd->GetChildItem("Sigma_TradeskillAltRightClick");
  if (checkbox) {
    checkbox->Checked = sigma->tradeskill_alt_rightclick_enabled.get();
  }

  // Sync the tradeskill show messages checkbox
  Zeal::GameUI::BasicWnd *messages_checkbox = ui_manager->options->wnd->GetChildItem("Sigma_TradeskillShowMessages");
  if (messages_checkbox) {
    messages_checkbox->Checked = sigma->tradeskill_show_messages.get();
  }
}

int ui_sigma::WndNotification(Zeal::GameUI::SidlWnd *wnd, unsigned int message, void *data) {
  // Handle window notifications for the Sigma tab
  return 0;
}
