<?xml version="1.0" encoding="us-ascii"?>
<XML ID="EQInterfaceDefinitionLanguage">
  <Schema xmlns="EverQuestData" xmlns:dt="EverQuestDataTypes" />
  <Label item="Zeal_VersionLabel">
    <ScreenID>Zeal_VersionLabel</ScreenID>
    <RelativePosition>true</RelativePosition>
    <Location>
      <X>272</X>
      <Y>2</Y>
    </Location>
    <Size>
      <CX>154</CX>
      <CY>32</CY>
    </Size>
    <Text>Zeal Version:</Text>
    <TextColor>
      <R>255</R>
      <G>255</G>
      <B>255</B>
    </TextColor>
    <NoWrap>true</NoWrap>
    <AlignCenter>false</AlignCenter>
    <AlignRight>false</AlignRight>
  </Label>
  <Label item="Zeal_VersionValue">
    <ScreenID>Zeal_VersionValue</ScreenID>
    <RelativePosition>true</RelativePosition>
    <Location>
      <X>424</X>
      <Y>2</Y>
    </Location>
    <Size>
      <CX>330</CX>
      <CY>32</CY>
    </Size>
    <Text>Not Installed</Text>
    <TextColor>
      <R>255</R>
      <G>255</G>
      <B>0</B>
      <Alpha>255</Alpha>
    </TextColor>
    <NoWrap>true</NoWrap>
    <AlignCenter>false</AlignCenter>
    <AlignRight>false</AlignRight>
  </Label>  
    
  <Button item="Zeal_HideCorpse">
    <ScreenID>Zeal_HideCorpse</ScreenID>
    <RelativePosition>true</RelativePosition>
    <Location>
      <X>20</X>
      <Y>48</Y>
    </Location>
    <Size>
      <CX>300</CX>
      <CY>40</CY>
    </Size>
    <Style_VScroll>false</Style_VScroll>
    <Style_HScroll>false</Style_HScroll>
    <Style_Transparent>false</Style_Transparent>
    <Style_Checkbox>true</Style_Checkbox>
    <TooltipReference>Hides a corpse after you have looted it</TooltipReference>
    <Text>Hide corpse looted</Text>
    <TextColor>
      <R>255</R>
      <G>255</G>
      <B>255</B>
    </TextColor>
    <ButtonDrawTemplate>
      <Normal>A_BtnNormal</Normal>
      <Pressed>A_BtnPressed</Pressed>
      <Flyby>A_BtnFlyby</Flyby>
      <Disabled>A_BtnDisabled</Disabled>
      <PressedFlyby>A_BtnPressedFlyby</PressedFlyby>
    </ButtonDrawTemplate>
  </Button>
  
   <Button item="Zeal_BlueCon">
    <ScreenID>Zeal_BlueCon</ScreenID>
    <RelativePosition>true</RelativePosition>
    <Location>
      <X>20</X>
      <Y>92</Y>
    </Location>
    <Size>
      <CX>300</CX>
      <CY>40</CY>
    </Size>
    <Style_VScroll>false</Style_VScroll>
    <Style_HScroll>false</Style_HScroll>
    <Style_Transparent>false</Style_Transparent>
    <Style_Checkbox>true</Style_Checkbox>
    <TooltipReference>Changes the dark blue con color in chat and track to use zeal color #15.</TooltipReference>
    <Text>Blue Con</Text>
    <TextColor>
      <R>255</R>
      <G>255</G>
      <B>255</B>
    </TextColor>
    <ButtonDrawTemplate>
      <Normal>A_BtnNormal</Normal>
      <Pressed>A_BtnPressed</Pressed>
      <Flyby>A_BtnFlyby</Flyby>
      <Disabled>A_BtnDisabled</Disabled>
      <PressedFlyby>A_BtnPressedFlyby</PressedFlyby>
    </ButtonDrawTemplate>
  </Button>
  
  <Button item="Zeal_Input">
    <ScreenID>Zeal_Input</ScreenID>
    <RelativePosition>true</RelativePosition>
    <Location>
      <X>20</X>
      <Y>136</Y>
    </Location>
    <Size>
      <CX>300</CX>
      <CY>40</CY>
    </Size>
    <Style_VScroll>false</Style_VScroll>
    <Style_HScroll>false</Style_HScroll>
    <Style_Transparent>false</Style_Transparent>
    <Style_Checkbox>true</Style_Checkbox>
    <TooltipReference>Toggles the zeal input setup for any input in game, giving you a more modern input (ctrl+c, ctrl+v, left, right, shift left+right for highlighting, home, end ect)</TooltipReference>
    <Text>Advanced Input</Text>
    <TextColor>
      <R>255</R>
      <G>255</G>
      <B>255</B>
    </TextColor>
    <ButtonDrawTemplate>
      <Normal>A_BtnNormal</Normal>
      <Pressed>A_BtnPressed</Pressed>
      <Flyby>A_BtnFlyby</Flyby>
      <Disabled>A_BtnDisabled</Disabled>
      <PressedFlyby>A_BtnPressedFlyby</PressedFlyby>
    </ButtonDrawTemplate>
  </Button>
  
  <Button item="Zeal_ShowHelm">
    <ScreenID>Zeal_ShowHelm</ScreenID>
    <RelativePosition>true</RelativePosition>
    <Location>
      <X>20</X>
      <Y>180</Y>
    </Location>
    <Size>
      <CX>300</CX>
      <CY>40</CY>
    </Size>
    <Style_VScroll>false</Style_VScroll>
    <Style_HScroll>false</Style_HScroll>
    <Style_Transparent>false</Style_Transparent>
    <Style_Checkbox>true</Style_Checkbox>
    <TooltipReference>Toggles your helmet</TooltipReference>
    <Text>Show Helm</Text>
    <TextColor>
      <R>255</R>
      <G>255</G>
      <B>255</B>
    </TextColor>
    <ButtonDrawTemplate>
      <Normal>A_BtnNormal</Normal>
      <Pressed>A_BtnPressed</Pressed>
      <Flyby>A_BtnFlyby</Flyby>
      <Disabled>A_BtnDisabled</Disabled>
      <PressedFlyby>A_BtnPressedFlyby</PressedFlyby>
    </ButtonDrawTemplate>
  </Button>
  
  <Button item="Zeal_Escape">
    <ScreenID>Zeal_Escape</ScreenID>
    <RelativePosition>true</RelativePosition>
    <Location>
      <X>20</X>
      <Y>224</Y>
    </Location>
    <Size>
      <CX>300</CX>
      <CY>40</CY>
    </Size>
    <Style_VScroll>false</Style_VScroll>
    <Style_HScroll>false</Style_HScroll>
    <Style_Transparent>false</Style_Transparent>
    <Style_Checkbox>true</Style_Checkbox>
    <TooltipReference>Stops windows from closing on escape but still drops target</TooltipReference>
    <Text>Escape Logic</Text>
    <TextColor>
      <R>255</R>
      <G>255</G>
      <B>255</B>
    </TextColor>
    <ButtonDrawTemplate>
      <Normal>A_BtnNormal</Normal>
      <Pressed>A_BtnPressed</Pressed>
      <Flyby>A_BtnFlyby</Flyby>
      <Disabled>A_BtnDisabled</Disabled>
      <PressedFlyby>A_BtnPressedFlyby</PressedFlyby>
    </ButtonDrawTemplate>
  </Button>

  <Button item="Zeal_RaidEscapeLock">
    <ScreenID>Zeal_RaidEscapeLock</ScreenID>
    <RelativePosition>true</RelativePosition>
    <Location>
      <X>20</X>
      <Y>268</Y>
    </Location>
    <Size>
      <CX>300</CX>
      <CY>40</CY>
    </Size>
    <Style_VScroll>false</Style_VScroll>
    <Style_HScroll>false</Style_HScroll>
    <Style_Transparent>false</Style_Transparent>
    <Style_Checkbox>true</Style_Checkbox>
    <TooltipReference>Pressing Escape will not close the raid window when this option is enabled.</TooltipReference>
    <Text>Raid Escape Lock</Text>
    <TextColor>
      <R>255</R>
      <G>255</G>
      <B>255</B>
    </TextColor>
    <ButtonDrawTemplate>
      <Normal>A_BtnNormal</Normal>
      <Pressed>A_BtnPressed</Pressed>
      <Flyby>A_BtnFlyby</Flyby>
      <Disabled>A_BtnDisabled</Disabled>
      <PressedFlyby>A_BtnPressedFlyby</PressedFlyby>
    </ButtonDrawTemplate>
  </Button>

  <Button item="Zeal_AltContainerTooltips">
    <ScreenID>Zeal_AltContainerTooltips</ScreenID>
    <RelativePosition>true</RelativePosition>
    <Location>
      <X>20</X>
      <Y>312</Y>
    </Location>
    <Size>
      <CX>300</CX>
      <CY>40</CY>
    </Size>
    <Style_VScroll>false</Style_VScroll>
    <Style_HScroll>false</Style_HScroll>
    <Style_Transparent>false</Style_Transparent>
    <Style_Checkbox>true</Style_Checkbox>
    <TooltipReference>Alt key will show ALL item Tooltips in open bags</TooltipReference>
    <Text>Container Tooltips</Text>
    <TextColor>
      <R>255</R>
      <G>255</G>
      <B>255</B>
    </TextColor>
    <ButtonDrawTemplate>
      <Normal>A_BtnNormal</Normal>
      <Pressed>A_BtnPressed</Pressed>
      <Flyby>A_BtnFlyby</Flyby>
      <Disabled>A_BtnDisabled</Disabled>
      <PressedFlyby>A_BtnPressedFlyby</PressedFlyby>
    </ButtonDrawTemplate>
  </Button>

  <Button item="Zeal_SpellbookAutoStand">
    <ScreenID>Zeal_SpellbookAutoStand</ScreenID>
    <RelativePosition>true</RelativePosition>
    <Location>
      <X>20</X>
      <Y>356</Y>
    </Location>
    <Size>
      <CX>300</CX>
      <CY>40</CY>
    </Size>
    <Style_VScroll>false</Style_VScroll>
    <Style_HScroll>false</Style_HScroll>
    <Style_Transparent>false</Style_Transparent>
    <Style_Checkbox>true</Style_Checkbox>
    <TooltipReference>Toggles Autostand if Spellbook is open (so you can turn pages with movement keys)</TooltipReference>
    <Text>Spellbook Auto Stand</Text>
    <TextColor>
      <R>255</R>
      <G>255</G>
      <B>255</B>
    </TextColor>
    <ButtonDrawTemplate>
      <Normal>A_BtnNormal</Normal>
      <Pressed>A_BtnPressed</Pressed>
      <Flyby>A_BtnFlyby</Flyby>
      <Disabled>A_BtnDisabled</Disabled>
      <PressedFlyby>A_BtnPressedFlyby</PressedFlyby>
    </ButtonDrawTemplate>
  </Button>

  <Button item="Zeal_ClassicClasses">
    <ScreenID>Zeal_ClassicClasses</ScreenID>
    <RelativePosition>true</RelativePosition>
    <Location>
      <X>20</X>
      <Y>400</Y>
    </Location>
    <Size>
      <CX>300</CX>
      <CY>40</CY>
    </Size>
    <Style_VScroll>false</Style_VScroll>
    <Style_HScroll>false</Style_HScroll>
    <Style_Transparent>false</Style_Transparent>
    <Style_Checkbox>true</Style_Checkbox>
    <TooltipReference>Toggles on and off the 50+ class names on /who and other areas</TooltipReference>
    <Text>Classic Class Names</Text>
    <TextColor>
      <R>255</R>
      <G>255</G>
      <B>255</B>
    </TextColor>
    <ButtonDrawTemplate>
      <Normal>A_BtnNormal</Normal>
      <Pressed>A_BtnPressed</Pressed>
      <Flyby>A_BtnFlyby</Flyby>
      <Disabled>A_BtnDisabled</Disabled>
      <PressedFlyby>A_BtnPressedFlyby</PressedFlyby>
    </ButtonDrawTemplate>
  </Button>

  <Button item="Zeal_TellWindows">
    <ScreenID>Zeal_TellWindows</ScreenID>
    <RelativePosition>true</RelativePosition>
    <Location>
      <X>20</X>
      <Y>444</Y>
    </Location>
    <Size>
      <CX>300</CX>
      <CY>40</CY>
    </Size>
    <Style_VScroll>false</Style_VScroll>
    <Style_HScroll>false</Style_HScroll>
    <Style_Transparent>false</Style_Transparent>
    <Style_Checkbox>true</Style_Checkbox>
    <TooltipReference>Toggles Tell Windows</TooltipReference>
    <Text>Tell Windows</Text>
    <TextColor>
      <R>255</R>
      <G>255</G>
      <B>255</B>
    </TextColor>
    <ButtonDrawTemplate>
      <Normal>A_BtnNormal</Normal>
      <Pressed>A_BtnPressed</Pressed>
      <Flyby>A_BtnFlyby</Flyby>
      <Disabled>A_BtnDisabled</Disabled>
      <PressedFlyby>A_BtnPressedFlyby</PressedFlyby>
    </ButtonDrawTemplate>
  </Button>
  
  <Button item="Zeal_TellWindowsHist">
    <ScreenID>Zeal_TellWindowsHist</ScreenID>
    <RelativePosition>true</RelativePosition>
    <Location>
      <X>20</X>
      <Y>488</Y>
    </Location>
    <Size>
      <CX>300</CX>
      <CY>40</CY>
    </Size>
    <Style_VScroll>false</Style_VScroll>
    <Style_HScroll>false</Style_HScroll>
    <Style_Transparent>false</Style_Transparent>
    <Style_Checkbox>true</Style_Checkbox>
    <TooltipReference>Toggles Tell Window Session History</TooltipReference>
    <Text>Tell Window History</Text>
    <TextColor>
      <R>255</R>
      <G>255</G>
      <B>255</B>
    </TextColor>
    <ButtonDrawTemplate>
      <Normal>A_BtnNormal</Normal>
      <Pressed>A_BtnPressed</Pressed>
      <Flyby>A_BtnFlyby</Flyby>
      <Disabled>A_BtnDisabled</Disabled>
      <PressedFlyby>A_BtnPressedFlyby</PressedFlyby>
    </ButtonDrawTemplate>
  </Button>

  <Button item="Zeal_LinkAllAltDelimiter">
    <ScreenID>Zeal_LinkAllAltDelimiter</ScreenID>
    <RelativePosition>true</RelativePosition>
    <Location>
      <X>20</X>
      <Y>532</Y>
    </Location>
    <Size>
      <CX>300</CX>
      <CY>40</CY>
    </Size>
    <Style_VScroll>false</Style_VScroll>
    <Style_HScroll>false</Style_HScroll>
    <Style_Transparent>false</Style_Transparent>
    <Style_Checkbox>true</Style_Checkbox>
    <TooltipReference>Enables alternative link all delimiter</TooltipReference>
    <Text>Alt LinkAll Delimiter</Text>
    <TextColor>
      <R>255</R>
      <G>255</G>
      <B>255</B>
    </TextColor>
    <ButtonDrawTemplate>
      <Normal>A_BtnNormal</Normal>
      <Pressed>A_BtnPressed</Pressed>
      <Flyby>A_BtnFlyby</Flyby>
      <Disabled>A_BtnDisabled</Disabled>
      <PressedFlyby>A_BtnPressedFlyby</PressedFlyby>
    </ButtonDrawTemplate>
  </Button>
  
  <Button item="Zeal_EnableContainerLock">
    <ScreenID>Zeal_EnableContainerLock</ScreenID>
    <RelativePosition>true</RelativePosition>
    <Location>
      <X>20</X>
      <Y>576</Y>
    </Location>
    <Size>
      <CX>300</CX>
      <CY>40</CY>
    </Size>
    <Style_VScroll>false</Style_VScroll>
    <Style_HScroll>false</Style_HScroll>
    <Style_Transparent>false</Style_Transparent>
    <Style_Checkbox>true</Style_Checkbox>
    <TooltipReference>Enables lock in container context menu (requires the bag to be re-opened to take effect)</TooltipReference>
    <Text>Enable container lock</Text>
    <TextColor>
      <R>255</R>
      <G>255</G>
      <B>255</B>
    </TextColor>
    <ButtonDrawTemplate>
      <Normal>A_BtnNormal</Normal>
      <Pressed>A_BtnPressed</Pressed>
      <Flyby>A_BtnFlyby</Flyby>
      <Disabled>A_BtnDisabled</Disabled>
      <PressedFlyby>A_BtnPressedFlyby</PressedFlyby>
    </ButtonDrawTemplate>
  </Button>
  
  <Button item="Zeal_ExportOnCamp">
    <ScreenID>Zeal_ExportOnCamp</ScreenID>
    <RelativePosition>true</RelativePosition>
    <Location>
      <X>20</X>
      <Y>620</Y>
    </Location>
    <Size>
      <CX>300</CX>
      <CY>40</CY>
    </Size>
    <Style_VScroll>false</Style_VScroll>
    <Style_HScroll>false</Style_HScroll>
    <Style_Transparent>false</Style_Transparent>
    <Style_Checkbox>true</Style_Checkbox>
    <TooltipReference>Exports current inventory and spellbook to files when /camp is executed</TooltipReference>
    <Text>Export data on /camp</Text>
    <TextColor>
      <R>255</R>
      <G>255</G>
      <B>255</B>
    </TextColor>
    <ButtonDrawTemplate>
      <Normal>A_BtnNormal</Normal>
      <Pressed>A_BtnPressed</Pressed>
      <Flyby>A_BtnFlyby</Flyby>
      <Disabled>A_BtnDisabled</Disabled>
      <PressedFlyby>A_BtnPressedFlyby</PressedFlyby>
    </ButtonDrawTemplate>
  </Button>

  <Button item="Zeal_BuffTimers">
    <ScreenID>Zeal_BuffTimers</ScreenID>
    <RelativePosition>true</RelativePosition>
    <Location>
      <X>20</X>
      <Y>664</Y>
    </Location>
    <Size>
      <CX>300</CX>
      <CY>40</CY>
    </Size>
    <Style_VScroll>false</Style_VScroll>
    <Style_HScroll>false</Style_HScroll>
    <Style_Transparent>false</Style_Transparent>
    <Style_Checkbox>true</Style_Checkbox>
    <TooltipReference>Toggles Buff Timers</TooltipReference>
    <Text>Buff Timers</Text>
    <TextColor>
      <R>255</R>
      <G>255</G>
      <B>255</B>
    </TextColor>
    <ButtonDrawTemplate>
      <Normal>A_BtnNormal</Normal>
      <Pressed>A_BtnPressed</Pressed>
      <Flyby>A_BtnFlyby</Flyby>
      <Disabled>A_BtnDisabled</Disabled>
      <PressedFlyby>A_BtnPressedFlyby</PressedFlyby>
    </ButtonDrawTemplate>
  </Button>

  <Button item="Zeal_RecastTimers">
    <ScreenID>Zeal_RecastTimers</ScreenID>
    <RelativePosition>true</RelativePosition>
    <Location>
      <X>20</X>
      <Y>708</Y>
    </Location>
    <Size>
      <CX>300</CX>
      <CY>40</CY>
    </Size>
    <Style_VScroll>false</Style_VScroll>
    <Style_HScroll>false</Style_HScroll>
    <Style_Transparent>false</Style_Transparent>
    <Style_Checkbox>true</Style_Checkbox>
    <TooltipReference>Toggles Recast Timers</TooltipReference>
    <Text>Recast Timers</Text>
    <TextColor>
      <R>255</R>
      <G>255</G>
      <B>255</B>
    </TextColor>
    <ButtonDrawTemplate>
      <Normal>A_BtnNormal</Normal>
      <Pressed>A_BtnPressed</Pressed>
      <Flyby>A_BtnFlyby</Flyby>
      <Disabled>A_BtnDisabled</Disabled>
      <PressedFlyby>A_BtnPressedFlyby</PressedFlyby>
    </ButtonDrawTemplate>
  </Button>

  <Button item="Zeal_RecastTimersLeftAlign">
    <ScreenID>Zeal_RecastTimersLeftAlign</ScreenID>
    <RelativePosition>true</RelativePosition>
    <Location>
      <X>20</X>
      <Y>752</Y>
    </Location>
    <Size>
      <CX>300</CX>
      <CY>40</CY>
    </Size>
    <Style_VScroll>false</Style_VScroll>
    <Style_HScroll>false</Style_HScroll>
    <Style_Transparent>false</Style_Transparent>
    <Style_Checkbox>true</Style_Checkbox>
    <TooltipReference>Left align recast timer tooltip</TooltipReference>
    <Text>Left Align Recast</Text>
    <TextColor>
      <R>255</R>
      <G>255</G>
      <B>255</B>
    </TextColor>
    <ButtonDrawTemplate>
      <Normal>A_BtnNormal</Normal>
      <Pressed>A_BtnPressed</Pressed>
      <Flyby>A_BtnFlyby</Flyby>
      <Disabled>A_BtnDisabled</Disabled>
      <PressedFlyby>A_BtnPressedFlyby</PressedFlyby>
    </ButtonDrawTemplate>
  </Button>
 
  <Button item="Zeal_InviteDialog">
    <ScreenID>Zeal_InviteDialog</ScreenID>
    <RelativePosition>true</RelativePosition>
    <Location>
      <X>20</X>
      <Y>796</Y>
    </Location>
    <Size>
      <CX>300</CX>
      <CY>40</CY>
    </Size>
    <Style_VScroll>false</Style_VScroll>
    <Style_HScroll>false</Style_HScroll>
    <Style_Transparent>false</Style_Transparent>
    <Style_Checkbox>true</Style_Checkbox>
    <TooltipReference>Display a dialog when invited to a group</TooltipReference>
    <Text>Invite Dialog</Text>
    <TextColor>
      <R>255</R>
      <G>255</G>
      <B>255</B>
    </TextColor>
    <ButtonDrawTemplate>
      <Normal>A_BtnNormal</Normal>
      <Pressed>A_BtnPressed</Pressed>
      <Flyby>A_BtnFlyby</Flyby>
      <Disabled>A_BtnDisabled</Disabled>
      <PressedFlyby>A_BtnPressedFlyby</PressedFlyby>
    </ButtonDrawTemplate>
  </Button>

  
  <Label item="Zeal_HoverTimeout_Label">
    <ScreenID>Zeal_HoverTimeout_Label</ScreenID>
    <RelativePosition>true</RelativePosition>
    <Location>
      <X>370</X>
      <Y>52</Y>
    </Location>
    <Size>
      <CX>300</CX>
      <CY>28</CY>
    </Size>
    <Text>Tooltip Hover Delay</Text>
    <TextColor>
      <R>255</R>
      <G>255</G>
      <B>255</B>
    </TextColor>
    <NoWrap>true</NoWrap>
    <AlignCenter>false</AlignCenter>
    <AlignRight>false</AlignRight>
  </Label>
  <Slider item="Zeal_HoverTimeout_Slider">
    <ScreenID>Zeal_HoverTimeout_Slider</ScreenID>
    <RelativePosition>true</RelativePosition>
    <Location>
      <X>370</X>
      <Y>92</Y>
    </Location>
    <Size>
      <CX>200</CX>
      <CY>32</CY>
    </Size>
    <SliderArt>SDT_DefSlider</SliderArt>
  </Slider>
  <Label item="Zeal_HoverTimeout_Value">
    <ScreenID>Zeal_HoverTimeout_Value</ScreenID>
    <RelativePosition>true</RelativePosition>
    <Location>
      <X>570</X>
      <Y>92</Y>
    </Location>
    <Size>
      <CX>100</CX>
      <CY>32</CY>
    </Size>
    <Text>0</Text>
    <TextColor>
      <R>255</R>
      <G>255</G>
      <B>255</B>
    </TextColor>
    <NoWrap>true</NoWrap>
    <AlignCenter>false</AlignCenter>
    <AlignRight>true</AlignRight>
  </Label>

  <Label item="Zeal_Timestamps_Label">
    <ScreenID>Zeal_Timestamps_Label</ScreenID>
    <RelativePosition>true</RelativePosition>
    <Location>
      <X>370</X>
      <Y>150</Y>
    </Location>
    <Size>
      <CX>300</CX>
      <CY>28</CY>
    </Size>
    <Text>Timestamps</Text>
    <TextColor>
      <R>255</R>
      <G>255</G>
      <B>255</B>
    </TextColor>
    <NoWrap>true</NoWrap>
    <AlignCenter>false</AlignCenter>
    <AlignRight>false</AlignRight>
  </Label>
  
  <Combobox item="Zeal_Timestamps_Combobox">
    <ScreenID>Zeal_Timestamps_Combobox</ScreenID>
    <DrawTemplate>WDT_Inner</DrawTemplate>
    <Location>
      <X>370</X>
      <Y>190</Y>
    </Location>
    <Size>
      <CX>300</CX>
      <CY>48</CY>
    </Size>
    <TextColor>
      <R>255</R>
      <G>255</G>
      <B>255</B>
    </TextColor>
    <ListHeight>70</ListHeight>
    <Button>BDT_Combo</Button>
    <Style_Border>true</Style_Border>
    <Choices>None</Choices>
    <Choices>Long</Choices>
    <Choices>Short</Choices>
  </Combobox>

  <Label item="Zeal_InviteSound_Label">
    <ScreenID>Zeal_InviteSound_Label</ScreenID>
    <RelativePosition>true</RelativePosition>
    <Location>
      <X>370</X>
      <Y>250</Y>
    </Location>
    <Size>
      <CX>290</CX>
      <CY>28</CY>
    </Size>
    <Text>Invite sound</Text>
    <TextColor>
      <R>255</R>
      <G>255</G>
      <B>255</B>
    </TextColor>
    <NoWrap>true</NoWrap>
    <AlignCenter>false</AlignCenter>
    <AlignRight>false</AlignRight>
  </Label>

  <Combobox item="Zeal_InviteSound_Combobox">
    <ScreenID>Zeal_InviteSound_Combobox</ScreenID>
    <DrawTemplate>WDT_Inner</DrawTemplate>
    <Location>
      <X>370</X>
      <Y>290</Y>
    </Location>
    <Size>
      <CX>300</CX>
      <CY>48</CY>
    </Size>
    <TextColor>
      <R>255</R>
      <G>255</G>
      <B>255</B>
    </TextColor>
    <ListHeight>70</ListHeight>
    <Button>BDT_Combo</Button>
    <Style_Border>true</Style_Border>
    <Choices>None</Choices>
  </Combobox>

  <Label item="Zeal_TellSound_Label">
    <ScreenID>Zeal_TellSound_Label</ScreenID>
    <RelativePosition>true</RelativePosition>
    <Location>
      <X>370</X>
      <Y>350</Y>
    </Location>
    <Size>
      <CX>290</CX>
      <CY>28</CY>
    </Size>
    <Text>New tell sound</Text>
    <TextColor>
      <R>255</R>
      <G>255</G>
      <B>255</B>
    </TextColor>
    <NoWrap>true</NoWrap>
    <AlignCenter>false</AlignCenter>
    <AlignRight>false</AlignRight>
  </Label>

  <Combobox item="Zeal_TellSound_Combobox">
    <ScreenID>Zeal_TellSound_Combobox</ScreenID>
    <DrawTemplate>WDT_Inner</DrawTemplate>
    <Location>
      <X>370</X>
      <Y>390</Y>
    </Location>
    <Size>
      <CX>300</CX>
      <CY>48</CY>
    </Size>
    <TextColor>
      <R>255</R>
      <G>255</G>
      <B>255</B>
    </TextColor>
    <ListHeight>70</ListHeight>
    <Button>BDT_Combo</Button>
    <Style_Border>true</Style_Border>
    <Choices>None</Choices>
  </Combobox>
 
  <Label item="Zeal_FPS_Label">
    <ScreenID>Zeal_FPS_Label</ScreenID>
    <RelativePosition>true</RelativePosition>
    <Location>
      <X>370</X>
      <Y>450</Y>
    </Location>
    <Size>
      <CX>290</CX>
      <CY>28</CY>
    </Size>
    <Text>FPS Limit</Text>
    <TextColor>
      <R>255</R>
      <G>255</G>
      <B>255</B>
    </TextColor>
    <NoWrap>true</NoWrap>
    <AlignCenter>false</AlignCenter>
    <AlignRight>false</AlignRight>
  </Label>

  <Combobox item="Zeal_FPS_Combobox">
    <ScreenID>Zeal_FPS_Combobox</ScreenID>
    <DrawTemplate>WDT_Inner</DrawTemplate>
    <Location>
      <X>370</X>
      <Y>490</Y>
    </Location>
    <Size>
      <CX>300</CX>
      <CY>48</CY>
    </Size>
    <TextColor>
      <R>255</R>
      <G>255</G>
      <B>255</B>
    </TextColor>
    <ListHeight>70</ListHeight>
    <Button>BDT_Combo</Button>
    <Style_Border>true</Style_Border>
    <Choices>Unlimited</Choices>
    <Choices>30</Choices>
    <Choices>60</Choices>
    <Choices>120</Choices>
    <Choices>144</Choices>
    <Choices>165</Choices>
    <Choices>240</Choices>
  </Combobox>

  <Label item="Zeal_LockToggleBag_Label">
    <ScreenID>Zeal_LockToggleBag_Label</ScreenID>
    <RelativePosition>true</RelativePosition>
    <Location>
      <X>370</X>
      <Y>550</Y>
    </Location>
    <Size>
      <CX>290</CX>
      <CY>28</CY>
    </Size>
    <Text>Lock open toggle bag slot #</Text>
    <TextColor>
      <R>255</R>
      <G>255</G>
      <B>255</B>
    </TextColor>
    <NoWrap>true</NoWrap>
    <AlignCenter>false</AlignCenter>
    <AlignRight>false</AlignRight>
  </Label>

  <Combobox item="Zeal_LockToggleBag_Combobox">
    <ScreenID>Zeal_LockToggleBag_Combobox</ScreenID>
    <DrawTemplate>WDT_Inner</DrawTemplate>
    <Location>
      <X>370</X>
      <Y>590</Y>
    </Location>
    <Size>
      <CX>300</CX>
      <CY>48</CY>
    </Size>
    <TextColor>
      <R>255</R>
      <G>255</G>
      <B>255</B>
    </TextColor>
    <ListHeight>70</ListHeight>
    <Button>BDT_Combo</Button>
    <Style_Border>true</Style_Border>
    <Choices>None</Choices>
    <Choices>1</Choices>
    <Choices>2</Choices>
    <Choices>3</Choices>
    <Choices>4</Choices>
    <Choices>5</Choices>
    <Choices>6</Choices>
    <Choices>7</Choices>
    <Choices>8</Choices>
  </Combobox> 

  
  <Button item="Zeal_EnhancedAutoRun">
    <ScreenID>Zeal_EnhancedAutoRun</ScreenID>
    <RelativePosition>true</RelativePosition>
    <Location>
      <X>720</X>
      <Y>48</Y>
    </Location>
    <Size>
      <CX>300</CX>
      <CY>40</CY>
    </Size>
    <Style_VScroll>false</Style_VScroll>
    <Style_HScroll>false</Style_HScroll>
    <Style_Transparent>false</Style_Transparent>
    <Style_Checkbox>true</Style_Checkbox>
    <TooltipReference>More consistent 'lock on' behavior and adds strafe</TooltipReference>
    <Text>Enhanced AutoRun</Text>
    <TextColor>
      <R>255</R>
      <G>255</G>
      <B>255</B>
    </TextColor>
    <ButtonDrawTemplate>
      <Normal>A_BtnNormal</Normal>
      <Pressed>A_BtnPressed</Pressed>
      <Flyby>A_BtnFlyby</Flyby>
      <Disabled>A_BtnDisabled</Disabled>
      <PressedFlyby>A_BtnPressedFlyby</PressedFlyby>
    </ButtonDrawTemplate>
  </Button>
  
  <Button item="Zeal_AutoFollowEnable">
    <ScreenID>Zeal_AutoFollowEnable</ScreenID>
    <RelativePosition>true</RelativePosition>
    <Location>
      <X>720</X>
      <Y>92</Y>
    </Location>
    <Size>
      <CX>300</CX>
      <CY>40</CY>
    </Size>
    <Style_VScroll>false</Style_VScroll>
    <Style_HScroll>false</Style_HScroll>
    <Style_Transparent>false</Style_Transparent>
    <Style_Checkbox>true</Style_Checkbox>
    <TooltipReference>Enable Zeal /follow mode (reliability, distance, and pitch)</TooltipReference>
    <Text>Enhance /follow</Text>
    <TextColor>
      <R>255</R>
      <G>255</G>
      <B>255</B>
    </TextColor>
    <ButtonDrawTemplate>
      <Normal>A_BtnNormal</Normal>
      <Pressed>A_BtnPressed</Pressed>
      <Flyby>A_BtnFlyby</Flyby>
      <Disabled>A_BtnDisabled</Disabled>
      <PressedFlyby>A_BtnPressedFlyby</PressedFlyby>
    </ButtonDrawTemplate>
  </Button>
  
  <Button item="Zeal_SingleClickGiveEnable">
    <ScreenID>Zeal_SingleClickGiveEnable</ScreenID>
    <RelativePosition>true</RelativePosition>
    <Location>
      <X>720</X>
      <Y>136</Y>
    </Location>
    <Size>
      <CX>300</CX>
      <CY>40</CY>
    </Size>
    <Style_VScroll>false</Style_VScroll>
    <Style_HScroll>false</Style_HScroll>
    <Style_Transparent>false</Style_Transparent>
    <Style_Checkbox>true</Style_Checkbox>
    <TooltipReference>Enables the single click auto-transfer of stackable items to open give, trade, or crafting windows</TooltipReference>
    <Text>Single click give</Text>
    <TextColor>
      <R>255</R>
      <G>255</G>
      <B>255</B>
    </TextColor>
    <ButtonDrawTemplate>
      <Normal>A_BtnNormal</Normal>
      <Pressed>A_BtnPressed</Pressed>
      <Flyby>A_BtnFlyby</Flyby>
      <Disabled>A_BtnDisabled</Disabled>
      <PressedFlyby>A_BtnPressedFlyby</PressedFlyby>
    </ButtonDrawTemplate>
  </Button>

  <Button item="Zeal_SelfClickThru">
    <ScreenID>Zeal_SelfClickThru</ScreenID>
    <RelativePosition>true</RelativePosition>
    <Location>
      <X>720</X>
      <Y>180</Y>
    </Location>
    <Size>
      <CX>300</CX>
      <CY>40</CY>
    </Size>
    <Style_VScroll>false</Style_VScroll>
    <Style_HScroll>false</Style_HScroll>
    <Style_Transparent>false</Style_Transparent>
    <Style_Checkbox>true</Style_Checkbox>
    <TooltipReference>Disables third person click on self (/selfclickthru)</TooltipReference>
    <Text>Self-click thru</Text>
    <TextColor>
      <R>255</R>
      <G>255</G>
      <B>255</B>
    </TextColor>
    <ButtonDrawTemplate>
      <Normal>A_BtnNormal</Normal>
      <Pressed>A_BtnPressed</Pressed>
      <Flyby>A_BtnFlyby</Flyby>
      <Disabled>A_BtnDisabled</Disabled>
      <PressedFlyby>A_BtnPressedFlyby</PressedFlyby>
    </ButtonDrawTemplate>
  </Button>

  <Button item="Zeal_LeftClickCon">
    <ScreenID>Zeal_LeftClickCon</ScreenID>
    <RelativePosition>true</RelativePosition>
    <Location>
      <X>720</X>
      <Y>224</Y>
    </Location>
    <Size>
      <CX>300</CX>
      <CY>40</CY>
    </Size>
    <Style_VScroll>false</Style_VScroll>
    <Style_HScroll>false</Style_HScroll>
    <Style_Transparent>false</Style_Transparent>
    <Style_Checkbox>true</Style_Checkbox>
    <TooltipReference>Generates a consider message upon left click</TooltipReference>
    <Text>Left click consider</Text>
    <TextColor>
      <R>255</R>
      <G>255</G>
      <B>255</B>
    </TextColor>
    <ButtonDrawTemplate>
      <Normal>A_BtnNormal</Normal>
      <Pressed>A_BtnPressed</Pressed>
      <Flyby>A_BtnFlyby</Flyby>
      <Disabled>A_BtnDisabled</Disabled>
      <PressedFlyby>A_BtnPressedFlyby</PressedFlyby>
    </ButtonDrawTemplate>
  </Button>

  <Button item="Zeal_RightClickToEquip">
    <ScreenID>Zeal_RightClickToEquip</ScreenID>
    <RelativePosition>true</RelativePosition>
    <Location>
      <X>720</X>
      <Y>268</Y>
    </Location>
    <Size>
      <CX>300</CX>
      <CY>40</CY>
    </Size>
    <Style_VScroll>false</Style_VScroll>
    <Style_HScroll>false</Style_HScroll>
    <Style_Transparent>false</Style_Transparent>
    <Style_Checkbox>true</Style_Checkbox>
    <TooltipReference>Equip items from your bags by right clicking them.</TooltipReference>
    <Text>Right Click to Equip</Text>
    <TextColor>
      <R>255</R>
      <G>255</G>
      <B>255</B>
    </TextColor>
    <ButtonDrawTemplate>
      <Normal>A_BtnNormal</Normal>
      <Pressed>A_BtnPressed</Pressed>
      <Flyby>A_BtnFlyby</Flyby>
      <Disabled>A_BtnDisabled</Disabled>
      <PressedFlyby>A_BtnPressedFlyby</PressedFlyby>
    </ButtonDrawTemplate>
  </Button>
  
  <Button item="Zeal_CtrlRightClickCorpse">
    <ScreenID>Zeal_CtrlRightClickCorpse</ScreenID>
    <RelativePosition>true</RelativePosition>
    <Location>
      <X>720</X>
      <Y>312</Y>
    </Location>
    <Size>
      <CX>300</CX>
      <CY>40</CY>
    </Size>
    <Style_VScroll>false</Style_VScroll>
    <Style_HScroll>false</Style_HScroll>
    <Style_Transparent>false</Style_Transparent>
    <Style_Checkbox>true</Style_Checkbox>
    <TooltipReference>Require Ctrl with right click to loot corpse</TooltipReference>
    <Text>Ctrl Right Click Loot</Text>
    <TextColor>
      <R>255</R>
      <G>255</G>
      <B>255</B>
    </TextColor>
    <ButtonDrawTemplate>
      <Normal>A_BtnNormal</Normal>
      <Pressed>A_BtnPressed</Pressed>
      <Flyby>A_BtnFlyby</Flyby>
      <Disabled>A_BtnDisabled</Disabled>
      <PressedFlyby>A_BtnPressedFlyby</PressedFlyby>
    </ButtonDrawTemplate>
  </Button>
  
  <Button item="Zeal_CtrlContextMenus">
    <ScreenID>Zeal_CtrlContextMenus</ScreenID>
    <RelativePosition>true</RelativePosition>
    <Location>
      <X>720</X>
      <Y>356</Y>
    </Location>
    <Size>
      <CX>300</CX>
      <CY>40</CY>
    </Size>
    <Style_VScroll>false</Style_VScroll>
    <Style_HScroll>false</Style_HScroll>
    <Style_Transparent>false</Style_Transparent>
    <Style_Checkbox>true</Style_Checkbox>
    <TooltipReference>Require Ctrl with right click to pop-up context menus</TooltipReference>
    <Text>Ctrl Context Menus</Text>
    <TextColor>
      <R>255</R>
      <G>255</G>
      <B>255</B>
    </TextColor>
    <ButtonDrawTemplate>
      <Normal>A_BtnNormal</Normal>
      <Pressed>A_BtnPressed</Pressed>
      <Flyby>A_BtnFlyby</Flyby>
      <Disabled>A_BtnDisabled</Disabled>
      <PressedFlyby>A_BtnPressedFlyby</PressedFlyby>
    </ButtonDrawTemplate>
  </Button>

  <Button item="Zeal_EnhancedSpellInfo">
    <ScreenID>Zeal_EnhancedSpellInfo</ScreenID>
    <RelativePosition>true</RelativePosition>
    <Location>
      <X>720</X>
      <Y>400</Y>
    </Location>
    <Size>
      <CX>300</CX>
      <CY>40</CY>
    </Size>
    <Style_VScroll>false</Style_VScroll>
    <Style_HScroll>false</Style_HScroll>
    <Style_Transparent>false</Style_Transparent>
    <Style_Checkbox>true</Style_Checkbox>
    <TooltipReference>Replaces the spell info display content with 'enhanced' information</TooltipReference>
    <Text>Enhanced spell info</Text>
    <TextColor>
      <R>255</R>
      <G>255</G>
      <B>255</B>
    </TextColor>
    <ButtonDrawTemplate>
      <Normal>A_BtnNormal</Normal>
      <Pressed>A_BtnPressed</Pressed>
      <Flyby>A_BtnFlyby</Flyby>
      <Disabled>A_BtnDisabled</Disabled>
      <PressedFlyby>A_BtnPressedFlyby</PressedFlyby>
    </ButtonDrawTemplate>
  </Button>

  <Button item="Zeal_CastAutoStand">
    <ScreenID>Zeal_CastAutoStand</ScreenID>
    <RelativePosition>true</RelativePosition>
    <Location>
      <X>720</X>
      <Y>444</Y>
    </Location>
    <Size>
      <CX>300</CX>
      <CY>40</CY>
    </Size>
    <Style_VScroll>false</Style_VScroll>
    <Style_HScroll>false</Style_HScroll>
    <Style_Transparent>false</Style_Transparent>
    <Style_Checkbox>true</Style_Checkbox>
    <TooltipReference>Autostand when trying to cast</TooltipReference>
    <Text>Cast Auto Stand</Text>
    <TextColor>
      <R>255</R>
      <G>255</G>
      <B>255</B>
    </TextColor>
    <ButtonDrawTemplate>
      <Normal>A_BtnNormal</Normal>
      <Pressed>A_BtnPressed</Pressed>
      <Flyby>A_BtnFlyby</Flyby>
      <Disabled>A_BtnDisabled</Disabled>
      <PressedFlyby>A_BtnPressedFlyby</PressedFlyby>
    </ButtonDrawTemplate>
  </Button>

  <Button item="Zeal_BrownSkeletons">
    <ScreenID>Zeal_BrownSkeletons</ScreenID>
    <RelativePosition>true</RelativePosition>
    <Location>
      <X>720</X>
      <Y>488</Y>
    </Location>
    <Size>
      <CX>300</CX>
      <CY>40</CY>
    </Size>
    <Style_VScroll>false</Style_VScroll>
    <Style_HScroll>false</Style_HScroll>
    <Style_Transparent>false</Style_Transparent>
    <Style_Checkbox>true</Style_Checkbox>
    <TooltipReference>Toggles brown skeleton models</TooltipReference>
    <Text>Brown Skeletons</Text>
    <TextColor>
      <R>255</R>
      <G>255</G>
      <B>255</B>
    </TextColor>
    <ButtonDrawTemplate>
      <Normal>A_BtnNormal</Normal>
      <Pressed>A_BtnPressed</Pressed>
      <Flyby>A_BtnFlyby</Flyby>
      <Disabled>A_BtnDisabled</Disabled>
      <PressedFlyby>A_BtnPressedFlyby</PressedFlyby>
    </ButtonDrawTemplate>
  </Button>

  <Button item="Zeal_ClassicMusic">
    <ScreenID>Zeal_ClassicMusic</ScreenID>
    <RelativePosition>true</RelativePosition>
    <Location>
      <X>720</X>
      <Y>532</Y>
    </Location>
    <Size>
      <CX>300</CX>
      <CY>40</CY>
    </Size>
    <Style_VScroll>false</Style_VScroll>
    <Style_HScroll>false</Style_HScroll>
    <Style_Transparent>false</Style_Transparent>
    <Style_Checkbox>true</Style_Checkbox>
    <TooltipReference>Toggle Classic Music</TooltipReference>
    <Text>Classic Music</Text>
    <TextColor>
      <R>255</R>
      <G>255</G>
      <B>255</B>
    </TextColor>
    <ButtonDrawTemplate>
      <Normal>A_BtnNormal</Normal>
      <Pressed>A_BtnPressed</Pressed>
      <Flyby>A_BtnFlyby</Flyby>
      <Disabled>A_BtnDisabled</Disabled>
      <PressedFlyby>A_BtnPressedFlyby</PressedFlyby>
    </ButtonDrawTemplate>
  </Button>
  
  <Button item="Zeal_SuppressMissedNotes">
    <ScreenID>Zeal_SuppressMissedNotes</ScreenID>
    <RelativePosition>true</RelativePosition>
    <Location>
      <X>720</X>
      <Y>576</Y>
    </Location>
    <Size>
      <CX>300</CX>
      <CY>40</CY>
    </Size>
    <Style_VScroll>false</Style_VScroll>
    <Style_HScroll>false</Style_HScroll>
    <Style_Transparent>false</Style_Transparent>
    <Style_Checkbox>true</Style_Checkbox>
    <TooltipReference>Suppress missed note messages from other bards</TooltipReference>
    <Text>No missed notes</Text>
    <TextColor>
      <R>255</R>
      <G>255</G>
      <B>255</B>
    </TextColor>
    <ButtonDrawTemplate>
      <Normal>A_BtnNormal</Normal>
      <Pressed>A_BtnPressed</Pressed>
      <Flyby>A_BtnFlyby</Flyby>
      <Disabled>A_BtnDisabled</Disabled>
      <PressedFlyby>A_BtnPressedFlyby</PressedFlyby>
    </ButtonDrawTemplate>
  </Button>

  <Button item="Zeal_SuppressOtherFizzles">
    <ScreenID>Zeal_SuppressOtherFizzles</ScreenID>
    <RelativePosition>true</RelativePosition>
    <Location>
      <X>720</X>
      <Y>620</Y>
    </Location>
    <Size>
      <CX>300</CX>
      <CY>40</CY>
    </Size>
    <Style_VScroll>false</Style_VScroll>
    <Style_HScroll>false</Style_HScroll>
    <Style_Transparent>false</Style_Transparent>
    <Style_Checkbox>true</Style_Checkbox>
    <TooltipReference>Suppress fizzle messages from non-grouped casters</TooltipReference>
    <Text>Reduce fizzles</Text>
    <TextColor>
      <R>255</R>
      <G>255</G>
      <B>255</B>
    </TextColor>
    <ButtonDrawTemplate>
      <Normal>A_BtnNormal</Normal>
      <Pressed>A_BtnPressed</Pressed>
      <Flyby>A_BtnFlyby</Flyby>
      <Disabled>A_BtnDisabled</Disabled>
      <PressedFlyby>A_BtnPressedFlyby</PressedFlyby>
    </ButtonDrawTemplate>
  </Button>  

  <Button item="Zeal_UseZealAssistOn">
    <ScreenID>Zeal_UseZealAssistOn</ScreenID>
    <RelativePosition>true</RelativePosition>
    <Location>
      <X>720</X>
      <Y>664</Y>
    </Location>
    <Size>
      <CX>300</CX>
      <CY>40</CY>
    </Size>
    <Style_VScroll>false</Style_VScroll>
    <Style_HScroll>false</Style_HScroll>
    <Style_Transparent>false</Style_Transparent>
    <Style_Checkbox>true</Style_Checkbox>
    <TooltipReference>Enable per character /assist on settings</TooltipReference>
    <Text>Use Zeal /assist on</Text>
    <TextColor>
      <R>255</R>
      <G>255</G>
      <B>255</B>
    </TextColor>
    <ButtonDrawTemplate>
      <Normal>A_BtnNormal</Normal>
      <Pressed>A_BtnPressed</Pressed>
      <Flyby>A_BtnFlyby</Flyby>
      <Disabled>A_BtnDisabled</Disabled>
      <PressedFlyby>A_BtnPressedFlyby</PressedFlyby>
    </ButtonDrawTemplate>
  </Button>

  <Button item="Zeal_DetectAssistFailure">
    <ScreenID>Zeal_DetectAssistFailure</ScreenID>
    <RelativePosition>true</RelativePosition>
    <Location>
      <X>720</X>
      <Y>708</Y>
    </Location>
    <Size>
      <CX>300</CX>
      <CY>40</CY>
    </Size>
    <Style_VScroll>false</Style_VScroll>
    <Style_HScroll>false</Style_HScroll>
    <Style_Transparent>false</Style_Transparent>
    <Style_Checkbox>true</Style_Checkbox>
    <TooltipReference>Emits a message and clears target if /assist fails</TooltipReference>
    <Text>Detect assist failure</Text>
    <TextColor>
      <R>255</R>
      <G>255</G>
      <B>255</B>
    </TextColor>
    <ButtonDrawTemplate>
      <Normal>A_BtnNormal</Normal>
      <Pressed>A_BtnPressed</Pressed>
      <Flyby>A_BtnFlyby</Flyby>
      <Disabled>A_BtnDisabled</Disabled>
      <PressedFlyby>A_BtnPressedFlyby</PressedFlyby>
    </ButtonDrawTemplate>
  </Button>

  <Button item="Zeal_SlashNotPoke">
    <ScreenID>Zeal_SlashNotPoke</ScreenID>
    <RelativePosition>true</RelativePosition>
    <Location>
      <X>720</X>
      <Y>752</Y>
    </Location>
    <Size>
      <CX>300</CX>
      <CY>40</CY>
    </Size>
    <Style_VScroll>false</Style_VScroll>
    <Style_HScroll>false</Style_HScroll>
    <Style_Transparent>false</Style_Transparent>
    <Style_Checkbox>true</Style_Checkbox>
    <TooltipReference>Uses the 2HS animation for 2HB</TooltipReference>
    <Text>Slash not poke</Text>
    <TextColor>
      <R>255</R>
      <G>255</G>
      <B>255</B>
    </TextColor>
    <ButtonDrawTemplate>
      <Normal>A_BtnNormal</Normal>
      <Pressed>A_BtnPressed</Pressed>
      <Flyby>A_BtnFlyby</Flyby>
      <Disabled>A_BtnDisabled</Disabled>
      <PressedFlyby>A_BtnPressedFlyby</PressedFlyby>
    </ButtonDrawTemplate>
  </Button>

  <Button item="Zeal_AltTransportCats">
    <ScreenID>Zeal_AltTransportCats</ScreenID>
    <RelativePosition>true</RelativePosition>
    <Location>
      <X>720</X>
      <Y>796</Y>
    </Location>
    <Size>
      <CX>300</CX>
      <CY>40</CY>
    </Size>
    <Style_VScroll>false</Style_VScroll>
    <Style_HScroll>false</Style_HScroll>
    <Style_Transparent>false</Style_Transparent>
    <Style_Checkbox>true</Style_Checkbox>
    <TooltipReference>Use the alternative transport spellset categories</TooltipReference>
    <Text>Alt transport cats</Text>
    <TextColor>
      <R>255</R>
      <G>255</G>
      <B>255</B>
    </TextColor>
    <ButtonDrawTemplate>
      <Normal>A_BtnNormal</Normal>
      <Pressed>A_BtnPressed</Pressed>
      <Flyby>A_BtnFlyby</Flyby>
      <Disabled>A_BtnDisabled</Disabled>
      <PressedFlyby>A_BtnPressedFlyby</PressedFlyby>
    </ButtonDrawTemplate>
  </Button>

  <Button item="Zeal_SuppressLifetapFeeling">
    <ScreenID>Zeal_SuppressLifetapFeeling</ScreenID>
    <RelativePosition>true</RelativePosition>
    <Location>
      <X>720</X>
      <Y>840</Y>
    </Location>
    <Size>
      <CX>300</CX>
      <CY>40</CY>
    </Size>
    <Style_VScroll>false</Style_VScroll>
    <Style_HScroll>false</Style_HScroll>
    <Style_Transparent>false</Style_Transparent>
    <Style_Checkbox>true</Style_Checkbox>
    <TooltipReference>Suppress lifetap 'I feel better' message</TooltipReference>
    <Text>Suppress lifetaps</Text>
    <TextColor>
      <R>255</R>
      <G>255</G>
      <B>255</B>
    </TextColor>
    <ButtonDrawTemplate>
      <Normal>A_BtnNormal</Normal>
      <Pressed>A_BtnPressed</Pressed>
      <Flyby>A_BtnFlyby</Flyby>
      <Disabled>A_BtnDisabled</Disabled>
      <PressedFlyby>A_BtnPressedFlyby</PressedFlyby>
    </ButtonDrawTemplate>
  </Button>
  
    
  <Page item="Tab_General">
    <ScreenID>Tab_General</ScreenID>
    <RelativePosition>true</RelativePosition>
    
    <Style_VScroll>true</Style_VScroll>
    <Style_HScroll>true</Style_HScroll>
    <Style_Transparent>false</Style_Transparent>
    <DrawTemplate>WDT_Def</DrawTemplate>
    <TabText>General</TabText>
    <TabTextColor>
      <R>255</R>
      <G>255</G>
      <B>255</B>
    </TabTextColor>
    <TabTextActiveColor>
      <R>255</R>
      <G>255</G>
      <B>0</B>
    </TabTextActiveColor>
    <Style_Sizable>true</Style_Sizable>
    <Pieces>Zeal_VersionLabel</Pieces>
    <Pieces>Zeal_VersionValue</Pieces>
    
    <Pieces>Zeal_HideCorpse</Pieces>
    <Pieces>Zeal_BlueCon</Pieces>
    <Pieces>Zeal_Input</Pieces>
    <Pieces>Zeal_ShowHelm</Pieces>
    <Pieces>Zeal_Escape</Pieces>
    <Pieces>Zeal_RaidEscapeLock</Pieces>
    <Pieces>Zeal_AltContainerTooltips</Pieces>
    <Pieces>Zeal_SpellbookAutoStand</Pieces>
    <Pieces>Zeal_ClassicClasses</Pieces>
    <Pieces>Zeal_TellWindows</Pieces>
    <Pieces>Zeal_TellWindowsHist</Pieces>
    <Pieces>Zeal_LinkAllAltDelimiter</Pieces>
    <Pieces>Zeal_EnableContainerLock</Pieces>
    <Pieces>Zeal_ExportOnCamp</Pieces>
    <Pieces>Zeal_BuffTimers</Pieces>
    <Pieces>Zeal_RecastTimers</Pieces>
    <Pieces>Zeal_RecastTimersLeftAlign</Pieces>
    <Pieces>Zeal_InviteDialog</Pieces>
    
    <Pieces>Zeal_HoverTimeout_Label</Pieces>
    <Pieces>Zeal_HoverTimeout_Slider</Pieces>
    <Pieces>Zeal_HoverTimeout_Value</Pieces>
    <Pieces>Zeal_Timestamps_Label</Pieces>
    <Pieces>Zeal_InviteSound_Label</Pieces>
    <Pieces>Zeal_TellSound_Label</Pieces>
    <Pieces>Zeal_FPS_Label</Pieces>
    <Pieces>Zeal_LockToggleBag_Label</Pieces>
    
    <Pieces>Zeal_LockToggleBag_Combobox</Pieces>
    <Pieces>Zeal_FPS_Combobox</Pieces>
    <Pieces>Zeal_TellSound_Combobox</Pieces>
    <Pieces>Zeal_InviteSound_Combobox</Pieces>
    <Pieces>Zeal_Timestamps_Combobox</Pieces>
    
    <Pieces>Zeal_EnhancedAutoRun</Pieces>
    <Pieces>Zeal_AutoFollowEnable</Pieces>
    <Pieces>Zeal_SingleClickGiveEnable</Pieces>
    <Pieces>Zeal_SelfClickThru</Pieces>
    <Pieces>Zeal_LeftClickCon</Pieces>
    <Pieces>Zeal_RightClickToEquip</Pieces>
    <Pieces>Zeal_CtrlRightClickCorpse</Pieces>
    <Pieces>Zeal_CtrlContextMenus</Pieces>
    <Pieces>Zeal_EnhancedSpellInfo</Pieces>
    <Pieces>Zeal_CastAutoStand</Pieces>
    <Pieces>Zeal_BrownSkeletons</Pieces>
    <Pieces>Zeal_ClassicMusic</Pieces>
    <Pieces>Zeal_SuppressMissedNotes</Pieces>
    <Pieces>Zeal_SuppressOtherFizzles</Pieces>
    <Pieces>Zeal_UseZealAssistOn</Pieces>
    <Pieces>Zeal_DetectAssistFailure</Pieces>
    <Pieces>Zeal_SlashNotPoke</Pieces>
    <Pieces>Zeal_AltTransportCats</Pieces>
    <Pieces>Zeal_SuppressLifetapFeeling</Pieces>
    <Location>
      <X>0</X>
      <Y>44</Y>
    </Location>
    <Size>
      <CX>760</CX>
      <CY>678</CY>
    </Size>
  </Page>
  </XML>