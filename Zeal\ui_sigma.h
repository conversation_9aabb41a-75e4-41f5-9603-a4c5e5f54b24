#pragma once

#include "game_ui.h"

// Forward declarations
class ZealService;
class UIManager;
class Sigma;

/**
 * UI class for the Sigma tab in Zeal options
 * 
 * This class manages the user interface for all Sigma (custom) features
 * in the Zeal options window.
 */
class ui_sigma {
 public:
  ui_sigma(ZealService *zeal, UIManager *mgr);
  ~ui_sigma();

  void InitUI();
  static int WndNotification(Zeal::GameUI::SidlWnd *wnd, unsigned int message, void *data);

 private:
  ZealService *zeal_service;
  UIManager *ui_manager;
  Sigma *sigma;

  void InitTradeskillOptions();
  void SyncTradeskillOptions();
};
