<?xml version="1.0" encoding="us-ascii"?>
<XML ID="EQInterfaceDefinitionLanguage">
  <Schema xmlns="EverQuestData" xmlns:dt="EverQuestDataTypes" />
  
  <Label item="Section_Nameplate">
    <ScreenID>Section_Nameplate</ScreenID>
    <RelativePosition>true</RelativePosition>
    <Location>
      <X>272</X>
      <Y>2</Y>
    </Location>
    <Size>
      <CX>320</CX>
      <CY>32</CY>
    </Size>
    <Text>Nameplate Colors</Text>
    <TextColor>
      <R>255</R>
      <G>255</G>
      <B>255</B>
    </TextColor>
    <NoWrap>true</NoWrap>
    <AlignCenter>false</AlignCenter>
    <AlignRight>false</AlignRight>
  </Label>
  
  <Button item="Zeal_BtnDivider">
    <ScreenID>Zeal_BtnDivider</ScreenID>
    <RelativePosition>true</RelativePosition>
    <Location>
      <X>0</X>
      <Y>44</Y>
    </Location>
    <Size>
      <CX>760</CX>
      <CY>4</CY>
    </Size>
    <Style_VScroll>false</Style_VScroll>
    <Style_HScroll>false</Style_HScroll>
    <Style_Transparent>false</Style_Transparent>
    <Style_Checkbox>false</Style_Checkbox>
    <Text>-</Text>
    <TextColor>
      <R>255</R>
      <G>255</G>
      <B>255</B>
    </TextColor>
    <ButtonDrawTemplate>
      <Normal>A_BtnNormal</Normal>
      <Pressed>A_BtnPressed</Pressed>
      <Flyby>A_BtnFlyby</Flyby>
      <Disabled>A_BtnDisabled</Disabled>
      <PressedFlyby>A_BtnPressedFlyby</PressedFlyby>
    </ButtonDrawTemplate>
  </Button>
  
 <Button item="Zeal_Color0">
    <ScreenID>Zeal_Color0</ScreenID>
    <RelativePosition>true</RelativePosition>
    <Location>
      <X>20</X>
      <Y>64</Y>
    </Location>
    <Size>
      <CX>300</CX>
      <CY>40</CY>
    </Size>
    <Style_VScroll>false</Style_VScroll>
    <Style_HScroll>false</Style_HScroll>
    <Style_Transparent>false</Style_Transparent>
    <Style_Checkbox>false</Style_Checkbox>
    <Text>1 - AFK</Text>
    <TextColor>
      <R>255</R>
      <G>255</G>
      <B>255</B>
    </TextColor>
    <ButtonDrawTemplate>
      <Normal>A_BtnNormal</Normal>
      <Pressed>A_BtnPressed</Pressed>
      <Flyby>A_BtnFlyby</Flyby>
      <Disabled>A_BtnDisabled</Disabled>
      <PressedFlyby>A_BtnPressedFlyby</PressedFlyby>
    </ButtonDrawTemplate>
  </Button>
  <Button item="Zeal_Color1">
    <ScreenID>Zeal_Color1</ScreenID>
    <RelativePosition>true</RelativePosition>
    <Location>
      <X>400</X>
      <Y>64</Y>
    </Location>
    <Size>
      <CX>300</CX>
      <CY>40</CY>
    </Size>
    <Style_VScroll>false</Style_VScroll>
    <Style_HScroll>false</Style_HScroll>
    <Style_Transparent>false</Style_Transparent>
    <Style_Checkbox>false</Style_Checkbox>
    <Text>2 - LFG</Text>
    <TextColor>
      <R>255</R>
      <G>255</G>
      <B>255</B>
    </TextColor>
    <ButtonDrawTemplate>
      <Normal>A_BtnNormal</Normal>
      <Pressed>A_BtnPressed</Pressed>
      <Flyby>A_BtnFlyby</Flyby>
      <Disabled>A_BtnDisabled</Disabled>
      <PressedFlyby>A_BtnPressedFlyby</PressedFlyby>
    </ButtonDrawTemplate>
  </Button>
   <Button item="Zeal_Color2">
    <ScreenID>Zeal_Color2</ScreenID>
    <RelativePosition>true</RelativePosition>
    <Location>
      <X>20</X>
      <Y>108</Y>
    </Location>
    <Size>
      <CX>300</CX>
      <CY>40</CY>
    </Size>
    <Style_VScroll>false</Style_VScroll>
    <Style_HScroll>false</Style_HScroll>
    <Style_Transparent>false</Style_Transparent>
    <Style_Checkbox>false</Style_Checkbox>
    <Text>3 - LD</Text>
    <TextColor>
      <R>255</R>
      <G>255</G>
      <B>255</B>
    </TextColor>
    <ButtonDrawTemplate>
      <Normal>A_BtnNormal</Normal>
      <Pressed>A_BtnPressed</Pressed>
      <Flyby>A_BtnFlyby</Flyby>
      <Disabled>A_BtnDisabled</Disabled>
      <PressedFlyby>A_BtnPressedFlyby</PressedFlyby>
    </ButtonDrawTemplate>
  </Button>
  <Button item="Zeal_Color3">
    <ScreenID>Zeal_Color3</ScreenID>
    <RelativePosition>true</RelativePosition>
    <Location>
      <X>400</X>
      <Y>108</Y>
    </Location>
    <Size>
      <CX>300</CX>
      <CY>40</CY>
    </Size>
    <Style_VScroll>false</Style_VScroll>
    <Style_HScroll>false</Style_HScroll>
    <Style_Transparent>false</Style_Transparent>
    <Style_Checkbox>false</Style_Checkbox>
    <Text>4 - MyGuild</Text>
    <TextColor>
      <R>255</R>
      <G>255</G>
      <B>255</B>
    </TextColor>
    <ButtonDrawTemplate>
      <Normal>A_BtnNormal</Normal>
      <Pressed>A_BtnPressed</Pressed>
      <Flyby>A_BtnFlyby</Flyby>
      <Disabled>A_BtnDisabled</Disabled>
      <PressedFlyby>A_BtnPressedFlyby</PressedFlyby>
    </ButtonDrawTemplate>
  </Button>
    <Button item="Zeal_Color4">
    <ScreenID>Zeal_Color4</ScreenID>
    <RelativePosition>true</RelativePosition>
    <Location>
      <X>20</X>
      <Y>152</Y>
    </Location>
    <Size>
      <CX>300</CX>
      <CY>40</CY>
    </Size>
    <Style_VScroll>false</Style_VScroll>
    <Style_HScroll>false</Style_HScroll>
    <Style_Transparent>false</Style_Transparent>
    <Style_Checkbox>false</Style_Checkbox>
    <Text>5 - Raid</Text>
    <TextColor>
      <R>255</R>
      <G>255</G>
      <B>255</B>
    </TextColor>
    <ButtonDrawTemplate>
      <Normal>A_BtnNormal</Normal>
      <Pressed>A_BtnPressed</Pressed>
      <Flyby>A_BtnFlyby</Flyby>
      <Disabled>A_BtnDisabled</Disabled>
      <PressedFlyby>A_BtnPressedFlyby</PressedFlyby>
    </ButtonDrawTemplate>
  </Button>
      <Button item="Zeal_Color5">
    <ScreenID>Zeal_Color5</ScreenID>
    <RelativePosition>true</RelativePosition>
    <Location>
      <X>400</X>
      <Y>152</Y>
    </Location>
    <Size>
      <CX>300</CX>
      <CY>40</CY>
    </Size>
    <Style_VScroll>false</Style_VScroll>
    <Style_HScroll>false</Style_HScroll>
    <Style_Transparent>false</Style_Transparent>
    <Style_Checkbox>false</Style_Checkbox>
    <Text>6 - Group</Text>
    <TextColor>
      <R>255</R>
      <G>255</G>
      <B>255</B>
    </TextColor>
    <ButtonDrawTemplate>
      <Normal>A_BtnNormal</Normal>
      <Pressed>A_BtnPressed</Pressed>
      <Flyby>A_BtnFlyby</Flyby>
      <Disabled>A_BtnDisabled</Disabled>
      <PressedFlyby>A_BtnPressedFlyby</PressedFlyby>
    </ButtonDrawTemplate>
  </Button>
  <Button item="Zeal_Color6">
    <ScreenID>Zeal_Color6</ScreenID>
    <RelativePosition>true</RelativePosition>
    <Location>
      <X>20</X>
      <Y>196</Y>
    </Location>
    <Size>
      <CX>300</CX>
      <CY>40</CY>
    </Size>
    <Style_VScroll>false</Style_VScroll>
    <Style_HScroll>false</Style_HScroll>
    <Style_Transparent>false</Style_Transparent>
    <Style_Checkbox>false</Style_Checkbox>
    <Text>7 - PVP</Text>
    <TextColor>
      <R>255</R>
      <G>255</G>
      <B>255</B>
    </TextColor>
    <ButtonDrawTemplate>
      <Normal>A_BtnNormal</Normal>
      <Pressed>A_BtnPressed</Pressed>
      <Flyby>A_BtnFlyby</Flyby>
      <Disabled>A_BtnDisabled</Disabled>
      <PressedFlyby>A_BtnPressedFlyby</PressedFlyby>
    </ButtonDrawTemplate>
  </Button>
  <Button item="Zeal_Color7">
    <ScreenID>Zeal_Color7</ScreenID>
    <RelativePosition>true</RelativePosition>
    <Location>
      <X>400</X>
      <Y>196</Y>
    </Location>
    <Size>
      <CX>300</CX>
      <CY>40</CY>
    </Size>
    <Style_VScroll>false</Style_VScroll>
    <Style_HScroll>false</Style_HScroll>
    <Style_Transparent>false</Style_Transparent>
    <Style_Checkbox>false</Style_Checkbox>
    <Text>8 - Roleplay</Text>
    <TextColor>
      <R>255</R>
      <G>255</G>
      <B>255</B>
    </TextColor>
    <ButtonDrawTemplate>
      <Normal>A_BtnNormal</Normal>
      <Pressed>A_BtnPressed</Pressed>
      <Flyby>A_BtnFlyby</Flyby>
      <Disabled>A_BtnDisabled</Disabled>
      <PressedFlyby>A_BtnPressedFlyby</PressedFlyby>
    </ButtonDrawTemplate>
  </Button>
   <Button item="Zeal_Color8">
    <ScreenID>Zeal_Color8</ScreenID>
    <RelativePosition>true</RelativePosition>
    <Location>
      <X>20</X>
      <Y>240</Y>
    </Location>
    <Size>
      <CX>300</CX>
      <CY>40</CY>
    </Size>
    <Style_VScroll>false</Style_VScroll>
    <Style_HScroll>false</Style_HScroll>
    <Style_Transparent>false</Style_Transparent>
    <Style_Checkbox>false</Style_Checkbox>
    <Text>9 - OtherGuilds</Text>
    <TextColor>
      <R>255</R>
      <G>255</G>
      <B>255</B>
    </TextColor>
    <ButtonDrawTemplate>
      <Normal>A_BtnNormal</Normal>
      <Pressed>A_BtnPressed</Pressed>
      <Flyby>A_BtnFlyby</Flyby>
      <Disabled>A_BtnDisabled</Disabled>
      <PressedFlyby>A_BtnPressedFlyby</PressedFlyby>
    </ButtonDrawTemplate>
  </Button>
  <Button item="Zeal_Color9">
    <ScreenID>Zeal_Color9</ScreenID>
    <RelativePosition>true</RelativePosition>
    <Location>
      <X>400</X>
      <Y>240</Y>
    </Location>
    <Size>
      <CX>300</CX>
      <CY>40</CY>
    </Size>
    <Style_VScroll>false</Style_VScroll>
    <Style_HScroll>false</Style_HScroll>
    <Style_Transparent>false</Style_Transparent>
    <Style_Checkbox>false</Style_Checkbox>
    <Text>10 - DefaultAdventurer</Text>
    <TextColor>
      <R>255</R>
      <G>255</G>
      <B>255</B>
    </TextColor>
    <ButtonDrawTemplate>
      <Normal>A_BtnNormal</Normal>
      <Pressed>A_BtnPressed</Pressed>
      <Flyby>A_BtnFlyby</Flyby>
      <Disabled>A_BtnDisabled</Disabled>
      <PressedFlyby>A_BtnPressedFlyby</PressedFlyby>
    </ButtonDrawTemplate>
  </Button>
    <Button item="Zeal_Color10">
    <ScreenID>Zeal_Color10</ScreenID>
    <RelativePosition>true</RelativePosition>
    <Location>
      <X>20</X>
      <Y>284</Y>
    </Location>
    <Size>
      <CX>300</CX>
      <CY>40</CY>
    </Size>
    <Style_VScroll>false</Style_VScroll>
    <Style_HScroll>false</Style_HScroll>
    <Style_Transparent>false</Style_Transparent>
    <Style_Checkbox>false</Style_Checkbox>
    <Text>11 - NpcCorpse</Text>
    <TextColor>
      <R>255</R>
      <G>255</G>
      <B>255</B>
    </TextColor>
    <ButtonDrawTemplate>
      <Normal>A_BtnNormal</Normal>
      <Pressed>A_BtnPressed</Pressed>
      <Flyby>A_BtnFlyby</Flyby>
      <Disabled>A_BtnDisabled</Disabled>
      <PressedFlyby>A_BtnPressedFlyby</PressedFlyby>
    </ButtonDrawTemplate>
  </Button>
      <Button item="Zeal_Color11">
    <ScreenID>Zeal_Color11</ScreenID>
    <RelativePosition>true</RelativePosition>
    <Location>
      <X>400</X>
      <Y>284</Y>
    </Location>
    <Size>
      <CX>300</CX>
      <CY>40</CY>
    </Size>
    <Style_VScroll>false</Style_VScroll>
    <Style_HScroll>false</Style_HScroll>
    <Style_Transparent>false</Style_Transparent>
    <Style_Checkbox>false</Style_Checkbox>
    <Text>12 - PlayersCorpse</Text>
    <TextColor>
      <R>255</R>
      <G>255</G>
      <B>255</B>
    </TextColor>
    <ButtonDrawTemplate>
      <Normal>A_BtnNormal</Normal>
      <Pressed>A_BtnPressed</Pressed>
      <Flyby>A_BtnFlyby</Flyby>
      <Disabled>A_BtnDisabled</Disabled>
      <PressedFlyby>A_BtnPressedFlyby</PressedFlyby>
    </ButtonDrawTemplate>
  </Button>
  <Button item="Zeal_Color30">
    <ScreenID>Zeal_Color30</ScreenID>
    <RelativePosition>true</RelativePosition>
    <Location>
      <X>20</X>
      <Y>328</Y>
    </Location>
    <Size>
      <CX>300</CX>
      <CY>40</CY>
    </Size>
    <Style_VScroll>false</Style_VScroll>
    <Style_HScroll>false</Style_HScroll>
    <Style_Transparent>false</Style_Transparent>
    <Style_Checkbox>false</Style_Checkbox>
    <Text>31 - Guild LFG</Text>
    <TextColor>
      <R>255</R>
      <G>255</G>
      <B>255</B>
    </TextColor>
    <ButtonDrawTemplate>
      <Normal>A_BtnNormal</Normal>
      <Pressed>A_BtnPressed</Pressed>
      <Flyby>A_BtnFlyby</Flyby>
      <Disabled>A_BtnDisabled</Disabled>
      <PressedFlyby>A_BtnPressedFlyby</PressedFlyby>
    </ButtonDrawTemplate>
  </Button>
  <Button item="Zeal_Color31">
    <ScreenID>Zeal_Color31</ScreenID>
    <RelativePosition>true</RelativePosition>
    <Location>
      <X>400</X>
      <Y>328</Y>
    </Location>
    <Size>
      <CX>300</CX>
      <CY>40</CY>
    </Size>
    <Style_VScroll>false</Style_VScroll>
    <Style_HScroll>false</Style_HScroll>
    <Style_Transparent>false</Style_Transparent>
    <Style_Checkbox>false</Style_Checkbox>
    <Text>32 - PVP Ally</Text>
    <TextColor>
      <R>255</R>
      <G>255</G>
      <B>255</B>
    </TextColor>
    <ButtonDrawTemplate>
      <Normal>A_BtnNormal</Normal>
      <Pressed>A_BtnPressed</Pressed>
      <Flyby>A_BtnFlyby</Flyby>
      <Disabled>A_BtnDisabled</Disabled>
      <PressedFlyby>A_BtnPressedFlyby</PressedFlyby>
    </ButtonDrawTemplate>
  </Button>
  <Label item="Section_NameplateNPC">
    <ScreenID>Section_NameplateNPC</ScreenID>
    <RelativePosition>true</RelativePosition>
    <Location>
      <X>264</X>
      <Y>372</Y>
    </Location>
    <Size>
      <CX>320</CX>
      <CY>32</CY>
    </Size>
    <Text>Con Colors</Text>
    <TextColor>
      <R>255</R>
      <G>255</G>
      <B>255</B>
    </TextColor>
    <NoWrap>true</NoWrap>
    <AlignCenter>false</AlignCenter>
    <AlignRight>false</AlignRight>
  </Label>
  <Button item="Zeal_BtnDividerNPC">
    <ScreenID>Zeal_BtnDividerNPC</ScreenID>
    <RelativePosition>true</RelativePosition>
    <Location>
      <X>0</X>
      <Y>412</Y>
    </Location>
    <Size>
      <CX>760</CX>
      <CY>4</CY>
    </Size>
    <Style_VScroll>false</Style_VScroll>
    <Style_HScroll>false</Style_HScroll>
    <Style_Transparent>false</Style_Transparent>
    <Style_Checkbox>false</Style_Checkbox>
    <Text>-</Text>
    <TextColor>
      <R>255</R>
      <G>255</G>
      <B>255</B>
    </TextColor>
    <ButtonDrawTemplate>
      <Normal>A_BtnNormal</Normal>
      <Pressed>A_BtnPressed</Pressed>
      <Flyby>A_BtnFlyby</Flyby>
      <Disabled>A_BtnDisabled</Disabled>
      <PressedFlyby>A_BtnPressedFlyby</PressedFlyby>
    </ButtonDrawTemplate>
  </Button>
<Button item="Zeal_Color12">
    <ScreenID>Zeal_Color12</ScreenID>
    <RelativePosition>true</RelativePosition>
    <Location>
      <X>20</X>
      <Y>432</Y>
    </Location>
    <Size>
      <CX>300</CX>
      <CY>40</CY>
    </Size>
    <Style_VScroll>false</Style_VScroll>
    <Style_HScroll>false</Style_HScroll>
    <Style_Transparent>false</Style_Transparent>
    <Style_Checkbox>false</Style_Checkbox>
    <Text>13 - Green Con</Text>
    <TextColor>
      <R>255</R>
      <G>255</G>
      <B>255</B>
    </TextColor>
    <ButtonDrawTemplate>
      <Normal>A_BtnNormal</Normal>
      <Pressed>A_BtnPressed</Pressed>
      <Flyby>A_BtnFlyby</Flyby>
      <Disabled>A_BtnDisabled</Disabled>
      <PressedFlyby>A_BtnPressedFlyby</PressedFlyby>
    </ButtonDrawTemplate>
  </Button>
<Button item="Zeal_Color13">
    <ScreenID>Zeal_Color13</ScreenID>
    <RelativePosition>true</RelativePosition>
    <Location>
      <X>400</X>
      <Y>432</Y>
    </Location>
    <Size>
      <CX>300</CX>
      <CY>40</CY>
    </Size>
    <Style_VScroll>false</Style_VScroll>
    <Style_HScroll>false</Style_HScroll>
    <Style_Transparent>false</Style_Transparent>
    <Style_Checkbox>false</Style_Checkbox>
    <Text>14 - LightBlue Con</Text>
    <TextColor>
      <R>255</R>
      <G>255</G>
      <B>255</B>
    </TextColor>
    <ButtonDrawTemplate>
      <Normal>A_BtnNormal</Normal>
      <Pressed>A_BtnPressed</Pressed>
      <Flyby>A_BtnFlyby</Flyby>
      <Disabled>A_BtnDisabled</Disabled>
      <PressedFlyby>A_BtnPressedFlyby</PressedFlyby>
    </ButtonDrawTemplate>
  </Button>
<Button item="Zeal_Color14">
    <ScreenID>Zeal_Color14</ScreenID>
    <RelativePosition>true</RelativePosition>
    <Location>
      <X>20</X>
      <Y>476</Y>
    </Location>
    <Size>
      <CX>300</CX>
      <CY>40</CY>
    </Size>
    <Style_VScroll>false</Style_VScroll>
    <Style_HScroll>false</Style_HScroll>
    <Style_Transparent>false</Style_Transparent>
    <Style_Checkbox>false</Style_Checkbox>
    <Text>15 - Blue Con</Text>
    <TextColor>
      <R>255</R>
      <G>255</G>
      <B>255</B>
    </TextColor>
    <ButtonDrawTemplate>
      <Normal>A_BtnNormal</Normal>
      <Pressed>A_BtnPressed</Pressed>
      <Flyby>A_BtnFlyby</Flyby>
      <Disabled>A_BtnDisabled</Disabled>
      <PressedFlyby>A_BtnPressedFlyby</PressedFlyby>
    </ButtonDrawTemplate>
  </Button>
<Button item="Zeal_Color15">
    <ScreenID>Zeal_Color15</ScreenID>
    <RelativePosition>true</RelativePosition>
    <Location>
      <X>400</X>
      <Y>476</Y>
    </Location>
    <Size>
      <CX>300</CX>
      <CY>40</CY>
    </Size>
    <Style_VScroll>false</Style_VScroll>
    <Style_HScroll>false</Style_HScroll>
    <Style_Transparent>false</Style_Transparent>
    <Style_Checkbox>false</Style_Checkbox>
    <Text>16 - White Con</Text>
    <TextColor>
      <R>255</R>
      <G>255</G>
      <B>255</B>
    </TextColor>
    <ButtonDrawTemplate>
      <Normal>A_BtnNormal</Normal>
      <Pressed>A_BtnPressed</Pressed>
      <Flyby>A_BtnFlyby</Flyby>
      <Disabled>A_BtnDisabled</Disabled>
      <PressedFlyby>A_BtnPressedFlyby</PressedFlyby>
    </ButtonDrawTemplate>
  </Button>
<Button item="Zeal_Color16">
    <ScreenID>Zeal_Color16</ScreenID>
    <RelativePosition>true</RelativePosition>
    <Location>
      <X>20</X>
      <Y>520</Y>
    </Location>
    <Size>
      <CX>300</CX>
      <CY>40</CY>
    </Size>
    <Style_VScroll>false</Style_VScroll>
    <Style_HScroll>false</Style_HScroll>
    <Style_Transparent>false</Style_Transparent>
    <Style_Checkbox>false</Style_Checkbox>
    <Text>17 - Yellow Con</Text>
    <TextColor>
      <R>255</R>
      <G>255</G>
      <B>255</B>
    </TextColor>
    <ButtonDrawTemplate>
      <Normal>A_BtnNormal</Normal>
      <Pressed>A_BtnPressed</Pressed>
      <Flyby>A_BtnFlyby</Flyby>
      <Disabled>A_BtnDisabled</Disabled>
      <PressedFlyby>A_BtnPressedFlyby</PressedFlyby>
    </ButtonDrawTemplate>
  </Button>
<Button item="Zeal_Color17">
    <ScreenID>Zeal_Color17</ScreenID>
    <RelativePosition>true</RelativePosition>
    <Location>
      <X>400</X>
      <Y>520</Y>
    </Location>
    <Size>
      <CX>300</CX>
      <CY>40</CY>
    </Size>
    <Style_VScroll>false</Style_VScroll>
    <Style_HScroll>false</Style_HScroll>
    <Style_Transparent>false</Style_Transparent>
    <Style_Checkbox>false</Style_Checkbox>
    <Text>18 - Red Con</Text>
    <TextColor>
      <R>255</R>
      <G>255</G>
      <B>255</B>
    </TextColor>
    <ButtonDrawTemplate>
      <Normal>A_BtnNormal</Normal>
      <Pressed>A_BtnPressed</Pressed>
      <Flyby>A_BtnFlyby</Flyby>
      <Disabled>A_BtnDisabled</Disabled>
      <PressedFlyby>A_BtnPressedFlyby</PressedFlyby>
    </ButtonDrawTemplate>
  </Button>
<Button item="Zeal_Color18">
    <ScreenID>Zeal_Color18</ScreenID>
    <RelativePosition>true</RelativePosition>
    <Location>
      <X>20</X>
      <Y>608</Y>
    </Location>
    <Size>
      <CX>300</CX>
      <CY>40</CY>
    </Size>
    <Style_VScroll>false</Style_VScroll>
    <Style_HScroll>false</Style_HScroll>
    <Style_Transparent>false</Style_Transparent>
    <Style_Checkbox>false</Style_Checkbox>
    <Text>19 - Target Color</Text>
    <TextColor>
      <R>255</R>
      <G>255</G>
      <B>255</B>
    </TextColor>
    <ButtonDrawTemplate>
      <Normal>A_BtnNormal</Normal>
      <Pressed>A_BtnPressed</Pressed>
      <Flyby>A_BtnFlyby</Flyby>
      <Disabled>A_BtnDisabled</Disabled>
      <PressedFlyby>A_BtnPressedFlyby</PressedFlyby>
    </ButtonDrawTemplate>
  </Button>

	<Label item="Section_ChatColors">
		<ScreenID>Section_ChatColors</ScreenID>
		<RelativePosition>true</RelativePosition>
		<Location>
			<X>272</X>
			<Y>694</Y>
		</Location>
		<Size>
			<CX>320</CX>
			<CY>32</CY>
		</Size>
		<Text>Extended Chat Colors</Text>
		<TextColor>
			<R>255</R>
			<G>255</G>
			<B>255</B>
		</TextColor>
		<NoWrap>true</NoWrap>
		<AlignCenter>false</AlignCenter>
		<AlignRight>false</AlignRight>
	</Label>

	<Button item="Zeal_BtnDividerCC">
		<ScreenID>Zeal_BtnDividerCC</ScreenID>
		<RelativePosition>true</RelativePosition>
		<Location>
			<X>0</X>
			<Y>720</Y>
		</Location>
		<Size>
			<CX>760</CX>
			<CY>4</CY>
		</Size>
		<Style_VScroll>false</Style_VScroll>
		<Style_HScroll>false</Style_HScroll>
		<Style_Transparent>false</Style_Transparent>
		<Style_Checkbox>false</Style_Checkbox>
		<Text>-</Text>
		<TextColor>
			<R>255</R>
			<G>255</G>
			<B>255</B>
		</TextColor>
		<ButtonDrawTemplate>
			<Normal>A_BtnNormal</Normal>
			<Pressed>A_BtnPressed</Pressed>
			<Flyby>A_BtnFlyby</Flyby>
			<Disabled>A_BtnDisabled</Disabled>
			<PressedFlyby>A_BtnPressedFlyby</PressedFlyby>
		</ButtonDrawTemplate>
	</Button>
  <Button item="Zeal_Color19">
    <ScreenID>Zeal_Color19</ScreenID>
    <RelativePosition>true</RelativePosition>
    <Location>
      <X>20</X>
      <Y>740</Y>
    </Location>
    <Size>
      <CX>300</CX>
      <CY>40</CY>
    </Size>
    <Style_VScroll>false</Style_VScroll>
    <Style_HScroll>false</Style_HScroll>
    <Style_Transparent>false</Style_Transparent>
    <Style_Checkbox>false</Style_Checkbox>
    <Text>20 - My Pet Damage</Text>
    <TextColor>
      <R>255</R>
      <G>255</G>
      <B>255</B>
    </TextColor>
    <ButtonDrawTemplate>
      <Normal>A_BtnNormal</Normal>
      <Pressed>A_BtnPressed</Pressed>
      <Flyby>A_BtnFlyby</Flyby>
      <Disabled>A_BtnDisabled</Disabled>
      <PressedFlyby>A_BtnPressedFlyby</PressedFlyby>
    </ButtonDrawTemplate>
  </Button>
  <Button item="Zeal_Color20">
    <ScreenID>Zeal_Color20</ScreenID>
    <RelativePosition>true</RelativePosition>
    <Location>
      <X>400</X>
      <Y>740</Y>
    </Location>
    <Size>
      <CX>300</CX>
      <CY>40</CY>
    </Size>
    <Style_VScroll>false</Style_VScroll>
    <Style_HScroll>false</Style_HScroll>
    <Style_Transparent>false</Style_Transparent>
    <Style_Checkbox>false</Style_Checkbox>
    <Text>21 - Other Pet Damage</Text>
    <TextColor>
      <R>255</R>
      <G>255</G>
      <B>255</B>
    </TextColor>
    <ButtonDrawTemplate>
      <Normal>A_BtnNormal</Normal>
      <Pressed>A_BtnPressed</Pressed>
      <Flyby>A_BtnFlyby</Flyby>
      <Disabled>A_BtnDisabled</Disabled>
      <PressedFlyby>A_BtnPressedFlyby</PressedFlyby>
    </ButtonDrawTemplate>
  </Button>
	<Button item="Zeal_Color21">
		<ScreenID>Zeal_Color21</ScreenID>
		<RelativePosition>true</RelativePosition>
		<Location>
			<X>20</X>
			<Y>784</Y>
		</Location>
		<Size>
			<CX>300</CX>
			<CY>40</CY>
		</Size>
		<Style_VScroll>false</Style_VScroll>
		<Style_HScroll>false</Style_HScroll>
		<Style_Transparent>false</Style_Transparent>
		<Style_Checkbox>false</Style_Checkbox>
		<Text>22 - My Pet Say</Text>
		<TextColor>
			<R>255</R>
			<G>255</G>
			<B>255</B>
		</TextColor>
		<ButtonDrawTemplate>
			<Normal>A_BtnNormal</Normal>
			<Pressed>A_BtnPressed</Pressed>
			<Flyby>A_BtnFlyby</Flyby>
			<Disabled>A_BtnDisabled</Disabled>
			<PressedFlyby>A_BtnPressedFlyby</PressedFlyby>
		</ButtonDrawTemplate>
	</Button>
	<Button item="Zeal_Color22">
		<ScreenID>Zeal_Color22</ScreenID>
		<RelativePosition>true</RelativePosition>
		<Location>
			<X>400</X>
			<Y>784</Y>
		</Location>
		<Size>
			<CX>300</CX>
			<CY>40</CY>
		</Size>
		<Style_VScroll>false</Style_VScroll>
		<Style_HScroll>false</Style_HScroll>
		<Style_Transparent>false</Style_Transparent>
		<Style_Checkbox>false</Style_Checkbox>
		<Text>23 - Other Pet Say</Text>
		<TextColor>
			<R>255</R>
			<G>255</G>
			<B>255</B>
		</TextColor>
		<ButtonDrawTemplate>
			<Normal>A_BtnNormal</Normal>
			<Pressed>A_BtnPressed</Pressed>
			<Flyby>A_BtnFlyby</Flyby>
			<Disabled>A_BtnDisabled</Disabled>
			<PressedFlyby>A_BtnPressedFlyby</PressedFlyby>
		</ButtonDrawTemplate>
	</Button>
    <Button item="Zeal_Color23">
		<ScreenID>Zeal_Color23</ScreenID>
		<RelativePosition>true</RelativePosition>
		<Location>
			<X>20</X>
			<Y>828</Y>
		</Location>
		<Size>
			<CX>300</CX>
			<CY>40</CY>
		</Size>
		<Style_VScroll>false</Style_VScroll>
		<Style_HScroll>false</Style_HScroll>
		<Style_Transparent>false</Style_Transparent>
		<Style_Checkbox>false</Style_Checkbox>
		<Text>24 - My Melee Special</Text>
		<TextColor>
			<R>255</R>
			<G>255</G>
			<B>255</B>
		</TextColor>
		<ButtonDrawTemplate>
			<Normal>A_BtnNormal</Normal>
			<Pressed>A_BtnPressed</Pressed>
			<Flyby>A_BtnFlyby</Flyby>
			<Disabled>A_BtnDisabled</Disabled>
			<PressedFlyby>A_BtnPressedFlyby</PressedFlyby>
		</ButtonDrawTemplate>
	</Button>
	<Button item="Zeal_Color24">
		<ScreenID>Zeal_Color24</ScreenID>
		<RelativePosition>true</RelativePosition>
		<Location>
			<X>400</X>
			<Y>828</Y>
		</Location>
		<Size>
			<CX>300</CX>
			<CY>40</CY>
		</Size>
		<Style_VScroll>false</Style_VScroll>
		<Style_HScroll>false</Style_HScroll>
		<Style_Transparent>false</Style_Transparent>
		<Style_Checkbox>false</Style_Checkbox>
		<Text>25 - Other Melee Special</Text>
		<TextColor>
			<R>255</R>
			<G>255</G>
			<B>255</B>
		</TextColor>
		<ButtonDrawTemplate>
			<Normal>A_BtnNormal</Normal>
			<Pressed>A_BtnPressed</Pressed>
			<Flyby>A_BtnFlyby</Flyby>
			<Disabled>A_BtnDisabled</Disabled>
			<PressedFlyby>A_BtnPressedFlyby</PressedFlyby>
		</ButtonDrawTemplate>
	</Button>
  <Button item="Zeal_Color25">
    <ScreenID>Zeal_Color25</ScreenID>
    <RelativePosition>true</RelativePosition>
    <Location>
      <X>20</X>
      <Y>872</Y>
    </Location>
    <Size>
      <CX>300</CX>
      <CY>40</CY>
    </Size>
    <Style_VScroll>false</Style_VScroll>
    <Style_HScroll>false</Style_HScroll>
    <Style_Transparent>false</Style_Transparent>
    <Style_Checkbox>false</Style_Checkbox>
    <Text>26 - Other Melee Critical</Text>
    <TextColor>
      <R>255</R>
      <G>255</G>
      <B>255</B>
    </TextColor>
    <ButtonDrawTemplate>
      <Normal>A_BtnNormal</Normal>
      <Pressed>A_BtnPressed</Pressed>
      <Flyby>A_BtnFlyby</Flyby>
      <Disabled>A_BtnDisabled</Disabled>
      <PressedFlyby>A_BtnPressedFlyby</PressedFlyby>
    </ButtonDrawTemplate>
  </Button>
  <Button item="Zeal_Color26">
    <ScreenID>Zeal_Color26</ScreenID>
    <RelativePosition>true</RelativePosition>
    <Location>
      <X>400</X>
      <Y>872</Y>
    </Location>
    <Size>
      <CX>300</CX>
      <CY>40</CY>
    </Size>
    <Style_VScroll>false</Style_VScroll>
    <Style_HScroll>false</Style_HScroll>
    <Style_Transparent>false</Style_Transparent>
    <Style_Checkbox>false</Style_Checkbox>
    <Text>27 - Other Damage Shield</Text>
    <TextColor>
      <R>240</R>
      <G>240</G>
      <B>0</B>
    </TextColor>
    <ButtonDrawTemplate>
      <Normal>A_BtnNormal</Normal>
      <Pressed>A_BtnPressed</Pressed>
      <Flyby>A_BtnFlyby</Flyby>
      <Disabled>A_BtnDisabled</Disabled>
      <PressedFlyby>A_BtnPressedFlyby</PressedFlyby>
    </ButtonDrawTemplate>
  </Button>
  
  <Label item="Section_FloatingCombat">
    <ScreenID>Section_FloatingCombat</ScreenID>
    <RelativePosition>true</RelativePosition>
    <Location>
      <X>272</X>
      <Y>938</Y>
    </Location>
    <Size>
      <CX>320</CX>
      <CY>32</CY>
    </Size>
    <Text>Floating combat damage</Text>
    <TextColor>
      <R>255</R>
      <G>255</G>
      <B>255</B>
    </TextColor>
    <NoWrap>true</NoWrap>
    <AlignCenter>false</AlignCenter>
    <AlignRight>false</AlignRight>
  </Label>
  <Button item="Zeal_BtnDividerFloating">
    <ScreenID>Zeal_BtnDividerFloating</ScreenID>
    <RelativePosition>true</RelativePosition>
    <Location>
      <X>0</X>
      <Y>964</Y>
    </Location>
    <Size>
      <CX>760</CX>
      <CY>4</CY>
    </Size>
    <Style_VScroll>false</Style_VScroll>
    <Style_HScroll>false</Style_HScroll>
    <Style_Transparent>false</Style_Transparent>
    <Style_Checkbox>false</Style_Checkbox>
    <Text>-</Text>
    <TextColor>
      <R>255</R>
      <G>255</G>
      <B>255</B>
    </TextColor>
    <ButtonDrawTemplate>
      <Normal>A_BtnNormal</Normal>
      <Pressed>A_BtnPressed</Pressed>
      <Flyby>A_BtnFlyby</Flyby>
      <Disabled>A_BtnDisabled</Disabled>
      <PressedFlyby>A_BtnPressedFlyby</PressedFlyby>
    </ButtonDrawTemplate>
  </Button>
  <Button item="Zeal_Color32">
    <ScreenID>Zeal_Color32</ScreenID>
    <RelativePosition>true</RelativePosition>
    <Location>
      <X>20</X>
      <Y>984</Y>
    </Location>
    <Size>
      <CX>300</CX>
      <CY>40</CY>
    </Size>
    <Style_VScroll>false</Style_VScroll>
    <Style_HScroll>false</Style_HScroll>
    <Style_Transparent>false</Style_Transparent>
    <Style_Checkbox>false</Style_Checkbox>
    <Text>33 - My melee</Text>
    <TextColor>
      <R>255</R>
      <G>255</G>
      <B>255</B>
    </TextColor>
    <ButtonDrawTemplate>
      <Normal>A_BtnNormal</Normal>
      <Pressed>A_BtnPressed</Pressed>
      <Flyby>A_BtnFlyby</Flyby>
      <Disabled>A_BtnDisabled</Disabled>
      <PressedFlyby>A_BtnPressedFlyby</PressedFlyby>
    </ButtonDrawTemplate>
  </Button>
  <Button item="Zeal_Color33">
    <ScreenID>Zeal_Color33</ScreenID>
    <RelativePosition>true</RelativePosition>
    <Location>
      <X>400</X>
      <Y>984</Y>
    </Location>
    <Size>
      <CX>300</CX>
      <CY>40</CY>
    </Size>
    <Style_VScroll>false</Style_VScroll>
    <Style_HScroll>false</Style_HScroll>
    <Style_Transparent>false</Style_Transparent>
    <Style_Checkbox>false</Style_Checkbox>
    <Text>34 - My spells</Text>
    <TextColor>
      <R>255</R>
      <G>255</G>
      <B>255</B>
    </TextColor>
    <ButtonDrawTemplate>
      <Normal>A_BtnNormal</Normal>
      <Pressed>A_BtnPressed</Pressed>
      <Flyby>A_BtnFlyby</Flyby>
      <Disabled>A_BtnDisabled</Disabled>
      <PressedFlyby>A_BtnPressedFlyby</PressedFlyby>
    </ButtonDrawTemplate>
  </Button>
  <Button item="Zeal_Color34">
    <ScreenID>Zeal_Color34</ScreenID>
    <RelativePosition>true</RelativePosition>
    <Location>
      <X>20</X>
      <Y>1028</Y>
    </Location>
    <Size>
      <CX>300</CX>
      <CY>40</CY>
    </Size>
    <Style_VScroll>false</Style_VScroll>
    <Style_HScroll>false</Style_HScroll>
    <Style_Transparent>false</Style_Transparent>
    <Style_Checkbox>false</Style_Checkbox>
    <Text>35 - Me hit by melee</Text>
    <TextColor>
      <R>255</R>
      <G>255</G>
      <B>255</B>
    </TextColor>
    <ButtonDrawTemplate>
      <Normal>A_BtnNormal</Normal>
      <Pressed>A_BtnPressed</Pressed>
      <Flyby>A_BtnFlyby</Flyby>
      <Disabled>A_BtnDisabled</Disabled>
      <PressedFlyby>A_BtnPressedFlyby</PressedFlyby>
    </ButtonDrawTemplate>
  </Button>
  <Button item="Zeal_Color35">
    <ScreenID>Zeal_Color35</ScreenID>
    <RelativePosition>true</RelativePosition>
    <Location>
      <X>400</X>
      <Y>1028</Y>
    </Location>
    <Size>
      <CX>300</CX>
      <CY>40</CY>
    </Size>
    <Style_VScroll>false</Style_VScroll>
    <Style_HScroll>false</Style_HScroll>
    <Style_Transparent>false</Style_Transparent>
    <Style_Checkbox>false</Style_Checkbox>
    <Text>36 - Me hit by spells</Text>
    <TextColor>
      <R>255</R>
      <G>255</G>
      <B>255</B>
    </TextColor>
    <ButtonDrawTemplate>
      <Normal>A_BtnNormal</Normal>
      <Pressed>A_BtnPressed</Pressed>
      <Flyby>A_BtnFlyby</Flyby>
      <Disabled>A_BtnDisabled</Disabled>
      <PressedFlyby>A_BtnPressedFlyby</PressedFlyby>
    </ButtonDrawTemplate>
  </Button>
  <Button item="Zeal_Color36">
    <ScreenID>Zeal_Color36</ScreenID>
    <RelativePosition>true</RelativePosition>
    <Location>
      <X>20</X>
      <Y>1072</Y>
    </Location>
    <Size>
      <CX>300</CX>
      <CY>40</CY>
    </Size>
    <Style_VScroll>false</Style_VScroll>
    <Style_HScroll>false</Style_HScroll>
    <Style_Transparent>false</Style_Transparent>
    <Style_Checkbox>false</Style_Checkbox>
    <Text>37 - Player hit by melee</Text>
    <TextColor>
      <R>255</R>
      <G>255</G>
      <B>255</B>
    </TextColor>
    <ButtonDrawTemplate>
      <Normal>A_BtnNormal</Normal>
      <Pressed>A_BtnPressed</Pressed>
      <Flyby>A_BtnFlyby</Flyby>
      <Disabled>A_BtnDisabled</Disabled>
      <PressedFlyby>A_BtnPressedFlyby</PressedFlyby>
    </ButtonDrawTemplate>
  </Button>
  <Button item="Zeal_Color37">
    <ScreenID>Zeal_Color37</ScreenID>
    <RelativePosition>true</RelativePosition>
    <Location>
      <X>400</X>
      <Y>1072</Y>
    </Location>
    <Size>
      <CX>300</CX>
      <CY>40</CY>
    </Size>
    <Style_VScroll>false</Style_VScroll>
    <Style_HScroll>false</Style_HScroll>
    <Style_Transparent>false</Style_Transparent>
    <Style_Checkbox>false</Style_Checkbox>
    <Text>38 - Player hit by spells</Text>
    <TextColor>
      <R>255</R>
      <G>255</G>
      <B>255</B>
    </TextColor>
    <ButtonDrawTemplate>
      <Normal>A_BtnNormal</Normal>
      <Pressed>A_BtnPressed</Pressed>
      <Flyby>A_BtnFlyby</Flyby>
      <Disabled>A_BtnDisabled</Disabled>
      <PressedFlyby>A_BtnPressedFlyby</PressedFlyby>
    </ButtonDrawTemplate>
  </Button>
  <Button item="Zeal_Color38">
    <ScreenID>Zeal_Color38</ScreenID>
    <RelativePosition>true</RelativePosition>
    <Location>
      <X>20</X>
      <Y>1116</Y>
    </Location>
    <Size>
      <CX>300</CX>
      <CY>40</CY>
    </Size>
    <Style_VScroll>false</Style_VScroll>
    <Style_HScroll>false</Style_HScroll>
    <Style_Transparent>false</Style_Transparent>
    <Style_Checkbox>false</Style_Checkbox>
    <Text>39 - NPC hit by meleee</Text>
    <TextColor>
      <R>255</R>
      <G>255</G>
      <B>255</B>
    </TextColor>
    <ButtonDrawTemplate>
      <Normal>A_BtnNormal</Normal>
      <Pressed>A_BtnPressed</Pressed>
      <Flyby>A_BtnFlyby</Flyby>
      <Disabled>A_BtnDisabled</Disabled>
      <PressedFlyby>A_BtnPressedFlyby</PressedFlyby>
    </ButtonDrawTemplate>
  </Button>
  <Button item="Zeal_Color39">
    <ScreenID>Zeal_Color39</ScreenID>
    <RelativePosition>true</RelativePosition>
    <Location>
      <X>400</X>
      <Y>1116</Y>
    </Location>
    <Size>
      <CX>300</CX>
      <CY>40</CY>
    </Size>
    <Style_VScroll>false</Style_VScroll>
    <Style_HScroll>false</Style_HScroll>
    <Style_Transparent>false</Style_Transparent>
    <Style_Checkbox>false</Style_Checkbox>
    <Text>40 - NPC hit by spells</Text>
    <TextColor>
      <R>255</R>
      <G>255</G>
      <B>255</B>
    </TextColor>
    <ButtonDrawTemplate>
      <Normal>A_BtnNormal</Normal>
      <Pressed>A_BtnPressed</Pressed>
      <Flyby>A_BtnFlyby</Flyby>
      <Disabled>A_BtnDisabled</Disabled>
      <PressedFlyby>A_BtnPressedFlyby</PressedFlyby>
    </ButtonDrawTemplate>
  </Button>
  <Page item="Tab_Colors">
    <ScreenID>Tab_Colors</ScreenID>
    <RelativePosition>true</RelativePosition>
    
    <Style_VScroll>true</Style_VScroll>
    <Style_HScroll>false</Style_HScroll>
    <Style_Transparent>false</Style_Transparent>
    <DrawTemplate>WDT_Def</DrawTemplate>
    <TabText>Colors</TabText>
    <TabTextColor>
      <R>255</R>
      <G>255</G>
      <B>255</B>
    </TabTextColor>
    <TabTextActiveColor>
      <R>255</R>
      <G>255</G>
      <B>0</B>
    </TabTextActiveColor>
    <Style_Sizable>true</Style_Sizable>
    <Pieces>Zeal_Color0</Pieces>
    <Pieces>Zeal_Color1</Pieces>
    <Pieces>Zeal_Color2</Pieces>
    <Pieces>Zeal_Color3</Pieces>
    <Pieces>Zeal_Color4</Pieces>
    <Pieces>Zeal_Color5</Pieces>
    <Pieces>Zeal_Color6</Pieces>
    <Pieces>Zeal_Color7</Pieces>
    <Pieces>Zeal_Color8</Pieces>
    <Pieces>Zeal_Color9</Pieces>
    <Pieces>Zeal_Color10</Pieces>
    <Pieces>Zeal_Color11</Pieces>
    <Pieces>Zeal_Color12</Pieces>
    <Pieces>Zeal_Color13</Pieces>
    <Pieces>Zeal_Color14</Pieces>
    <Pieces>Zeal_Color15</Pieces>
    <Pieces>Zeal_Color16</Pieces>
    <Pieces>Zeal_Color17</Pieces>
    <Pieces>Section_Nameplate</Pieces>
    <Pieces>Section_NameplateNPC</Pieces>
    <Pieces>Section_ChatColors</Pieces>
    <Pieces>Zeal_BtnDivider</Pieces>
    <Pieces>Zeal_BtnDividerNPC</Pieces>
    <Pieces>Zeal_BtnDividerCC</Pieces>
    <Pieces>Zeal_Color18</Pieces>
    <Pieces>Zeal_Color19</Pieces>
    <Pieces>Zeal_Color20</Pieces>
    <Pieces>Zeal_Color21</Pieces>
    <Pieces>Zeal_Color22</Pieces>
    <Pieces>Zeal_Color23</Pieces>
    <Pieces>Zeal_Color24</Pieces>
    <Pieces>Zeal_Color25</Pieces>
    <Pieces>Zeal_Color26</Pieces>
    <Pieces>Section_FloatingCombat</Pieces>
    <Pieces>Zeal_BtnDividerFloating</Pieces>
    <Pieces>Zeal_Color30</Pieces>
    <Pieces>Zeal_Color31</Pieces>
    <Pieces>Zeal_Color32</Pieces>
    <Pieces>Zeal_Color33</Pieces>
    <Pieces>Zeal_Color34</Pieces>
    <Pieces>Zeal_Color35</Pieces>
    <Pieces>Zeal_Color36</Pieces>
    <Pieces>Zeal_Color37</Pieces>
    <Pieces>Zeal_Color38</Pieces>
    <Pieces>Zeal_Color39</Pieces>

    <Location>
      <X>0</X>
      <Y>44</Y>
    </Location>
    <Size>
      <CX>760</CX>
      <CY>678</CY>
    </Size>
  </Page>
  </XML>