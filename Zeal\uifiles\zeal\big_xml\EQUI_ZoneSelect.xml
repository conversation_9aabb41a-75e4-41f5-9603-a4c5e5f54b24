<?xml version="1.0" encoding="us-ascii"?>
<XML ID="EQInterfaceDefinitionLanguage">
  <Schema xmlns="EverQuestData" xmlns:dt="EverQuestDataTypes" />
  <Label item="Zeal_ZoneSelect_CurrentZone">
    <ScreenID>Zeal_ZoneSelect_CurrentZone</ScreenID>
    <Font>3</Font>
    <Text />
    <TextColor>
      <R>255</R>
      <G>255</G>
      <B>255</B>
    </TextColor>
    <Size>
      <CX>440</CX>
      <CY>48</CY>
    </Size>
    <AlignCenter>true</AlignCenter>
    <Location>
      <X>8</X>
      <Y>8</Y>
    </Location>
    <AutoStretch>true</AutoStretch>
    <RelativePosition>true</RelativePosition>
    <RightAnchorToLeft>false</RightAnchorToLeft>
    <TopAnchorToTop>true</TopAnchorToTop>
    <LeftAnchorToLeft>true</LeftAnchorToLeft>
    <BottomAnchorToTop>false</BottomAnchorToTop>
	<TopAnchorOffset>8</TopAnchorOffset>
    <BottomAnchorOffset>8</BottomAnchorOffset>
  </Label>
  <Listbox item="Zeal_ZoneSelect_ListBox">
    <ScreenID>Zeal_ZoneSelect_ListBox</ScreenID>
    <DrawTemplate>WDT_Inner</DrawTemplate>
    <TooltipReference>List of Zones.</TooltipReference>
    <AutoStretch>true</AutoStretch>
    <LeftAnchorOffset>8</LeftAnchorOffset>
    <TopAnchorOffset>48</TopAnchorOffset>
    <RightAnchorOffset>8</RightAnchorOffset>
    <BottomAnchorOffset>88</BottomAnchorOffset>
    <TopAnchorToTop>true</TopAnchorToTop>
    <BottomAnchorToTop>false</BottomAnchorToTop>
    <LeftAnchorToLeft>true</LeftAnchorToLeft>
    <RightAnchorToLeft>false</RightAnchorToLeft>
    <TextColor>
      <R>255</R>
      <G>255</G>
      <B>255</B>
    </TextColor>
    <Style_Border>true</Style_Border>
    <Style_VScroll>true</Style_VScroll>
    <Columns>
      <Width>90</Width>
      <Heading>ID</Heading>
    </Columns>
    <Columns>
      <Width>200</Width>
      <Heading>Short Name</Heading>
    </Columns>
    <Columns>
      <Width>320</Width>
      <Heading>Long Name</Heading>
    </Columns>
   </Listbox>
   <Button item="Zeal_ZoneSelect_Apply">
    <ScreenID>Zeal_ZoneSelect_Apply</ScreenID>
    <RelativePosition>true</RelativePosition>
    <AutoStretch>true</AutoStretch>
    <LeftAnchorOffset>8</LeftAnchorOffset>
    <TopAnchorOffset>80</TopAnchorOffset>
    <RightAnchorOffset>200</RightAnchorOffset>
    <BottomAnchorOffset>8</BottomAnchorOffset>
    <TopAnchorToTop>false</TopAnchorToTop>
    <BottomAnchorToTop>false</BottomAnchorToTop>
    <LeftAnchorToLeft>true</LeftAnchorToLeft>
    <RightAnchorToLeft>true</RightAnchorToLeft>
    <Style_Checkbox>false</Style_Checkbox>
    <TooltipReference>Applies the character select zone</TooltipReference>
    <Text>Apply</Text>
    <TextColor>
      <R>255</R>
      <G>255</G>
      <B>255</B>
    </TextColor>
    <ButtonDrawTemplate>
      <Normal>A_SmallBtnNormal</Normal>
      <Pressed>A_SmallBtnPressed</Pressed>
      <Flyby>A_SmallBtnFlyby</Flyby>
      <Disabled>A_SmallBtnDisabled</Disabled>
      <PressedFlyby>A_SmallBtnPressedFlyby</PressedFlyby>
    </ButtonDrawTemplate>
  </Button>
  
  <Screen item="ZealZoneSelect">
    <RelativePosition>false</RelativePosition>
    <Location>
      <X>480</X>
      <Y>480</Y>
    </Location>
    <Size>
      <CX>620</CX>
      <CY>800</CY>
    </Size>
    <Style_VScroll>false</Style_VScroll>
    <Style_HScroll>false</Style_HScroll>
    <Style_Transparent>false</Style_Transparent>
    <DrawTemplate>WDT_Def</DrawTemplate>
    <Style_Titlebar>true</Style_Titlebar>
    <Style_Closebox>false</Style_Closebox>
    <Style_Minimizebox>false</Style_Minimizebox>
    <Style_Border>true</Style_Border>
    <Style_Sizable>true</Style_Sizable>
    <Pieces>Zeal_ZoneSelect_ListBox</Pieces>
	<Pieces>Zeal_ZoneSelect_Apply</Pieces>
	<Pieces>Zeal_ZoneSelect_CurrentZone</Pieces>
  </Screen>
</XML>