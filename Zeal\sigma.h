#pragma once

#include "zeal_settings.h"
#include "game_ui.h"

// Forward declarations
class ZealService;
class UIManager;

/**
 * Sigma - Custom modifications system for Zeal
 * 
 * This class provides a separate module system for custom Zeal modifications
 * that can be easily maintained and merged when Zeal updates. All custom
 * features should be implemented through this system to keep them isolated
 * from core Zeal functionality.
 */
class Sigma {
 public:
  Sigma(ZealService *zeal);
  ~Sigma();

  // Tradeskill functionality
  bool HandleAltRightClick(Zeal::GameUI::InvSlot *inv_slot);
  
  // Settings for Sigma features
  ZealSetting<bool> tradeskill_alt_rightclick_enabled = {true, "Sigma", "TradeskillAltRightClick", true};
  ZealSetting<bool> tradeskill_show_messages = {true, "Sigma", "TradeskillShowMessages", true};

 private:
  ZealService *zeal_service;
  
  // Tradeskill helper methods
  bool IsValidTradeskillTransfer(Zeal::GameUI::InvSlot *src_slot);
  bool TransferSingleItemToTradeskill(Zeal::GameUI::InvSlot *src_slot);
  Zeal::GameUI::ContainerWnd *FindActiveTradeskillContainer();
  bool MoveSingleItemFromStack(Zeal::GameUI::InvSlot *src_slot, int dst_slot_id);
};
