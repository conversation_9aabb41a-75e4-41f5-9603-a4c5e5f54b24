<?xml version="1.0" encoding="us-ascii"?>
<XML ID="EQInterfaceDefinitionLanguage">
  <Schema xmlns="EverQuestData" xmlns:dt="EverQuestDataTypes" />

	

	
	<Button item="OGP_FastItemDestroyCheckbox">
		<ScreenID>OGP_FastItemDestroyCheckbox</ScreenID>
		<RelativePosition>true</RelativePosition>
		<Location>
			<X>20</X>
			<Y>30</Y>
		</Location>
		<Size>
			<CX>300</CX>
			<CY>40</CY>
		</Size>
		<Style_VScroll>false</Style_VScroll>
		<Style_HScroll>false</Style_HScroll>
		<Style_Transparent>false</Style_Transparent>
		<Style_Checkbox>true</Style_Checkbox>
		<Text>Fast Item Destroy</Text>
		<TextColor>
				<R>255</R>
				<G>255</G>
				<B>255</B>
		</TextColor>
		<ButtonDrawTemplate>
			<Normal>A_BtnNormal</Normal>
			<Pressed>A_BtnPressed</Pressed>
			<Flyby>A_BtnFlyby</Flyby>
			<Disabled>A_BtnDisabled</Disabled>
			<PressedFlyby>A_BtnPressedFlyby</PressedFlyby>
		</ButtonDrawTemplate>
	</Button>

	
	<Button item="OGP_GuildInvitesCheckbox">
		<ScreenID>OGP_GuildInvitesCheckbox</ScreenID>
		<RelativePosition>true</RelativePosition>
		<Location>
			<X>20</X>
			<Y>70</Y>
		</Location>
		<Size>
			<CX>300</CX>
			<CY>40</CY>
		</Size>
		<Style_VScroll>false</Style_VScroll>
		<Style_HScroll>false</Style_HScroll>
		<Style_Transparent>false</Style_Transparent>
		<Style_Checkbox>true</Style_Checkbox>
		<Text>Receive Guild Invites</Text>
		<TextColor>
				<R>255</R>
				<G>255</G>
				<B>255</B>
		</TextColor>
		<ButtonDrawTemplate>
			<Normal>A_BtnNormal</Normal>
			<Pressed>A_BtnPressed</Pressed>
			<Flyby>A_BtnFlyby</Flyby>
			<Disabled>A_BtnDisabled</Disabled>
			<PressedFlyby>A_BtnPressedFlyby</PressedFlyby>
		</ButtonDrawTemplate>
	</Button>

	
	<Button item="OGP_LootAutosplitCheckbox">
		<ScreenID>OGP_LootAutosplitCheckbox</ScreenID>
		<RelativePosition>true</RelativePosition>
		<Location>
			<X>20</X>
			<Y>110</Y>
		</Location>
		<Size>
			<CX>300</CX>
			<CY>40</CY>
		</Size>
		<Style_VScroll>false</Style_VScroll>
		<Style_HScroll>false</Style_HScroll>
		<Style_Transparent>false</Style_Transparent>
		<Style_Checkbox>true</Style_Checkbox>
		<Text>Autosplit</Text>
		<TextColor>
				<R>255</R>
				<G>255</G>
				<B>255</B>
		</TextColor>
		<ButtonDrawTemplate>
			<Normal>A_BtnNormal</Normal>
			<Pressed>A_BtnPressed</Pressed>
			<Flyby>A_BtnFlyby</Flyby>
			<Disabled>A_BtnDisabled</Disabled>
			<PressedFlyby>A_BtnPressedFlyby</PressedFlyby>
		</ButtonDrawTemplate>
	</Button>

	
	<Label item="OGP_PlayerTradeLabel">
		<ScreenID>OGP_PlayerTradeLabel</ScreenID>
		<RelativePosition>true</RelativePosition>
		<Location>
			<X>20</X>
			<Y>260</Y>
		</Location>
		<Size>
			<CX>300</CX>
			<CY>28</CY>
		</Size>
		<Text>Allow trading with:</Text>
		<TextColor>
				<R>255</R>
				<G>255</G>
				<B>255</B>
		</TextColor>
		<NoWrap>true</NoWrap>
		<AlignCenter>false</AlignCenter>
		<AlignRight>false</AlignRight>
	</Label>

	<Combobox item="OGP_PlayerTradeCombobox">
		<ScreenID>OGP_PlayerTradeCombobox</ScreenID>
		<DrawTemplate>WDT_Inner</DrawTemplate>
		<Location>
			<X>20</X>
			<Y>300</Y>
		</Location>
		<Size>
			<CX>300</CX>
			<CY>48</CY>
		</Size>
		<ListHeight>60</ListHeight>
		<Button>BDT_Combo</Button>
		<Style_Border>true</Style_Border>
		<Choices>Everyone</Choices>
		<Choices>Party members only</Choices>
		<Choices>No one</Choices>
	</Combobox>

	
	<Button item="OGP_LoadSkinButton">
		<ScreenID>OGP_LoadSkinButton</ScreenID>
		<RelativePosition>true</RelativePosition>
		<Location>
			<X>20</X>
			<Y>360</Y>
		</Location>
		<Size>
			<CX>300</CX>
			<CY>40</CY>
		</Size>
		<Style_VScroll>false</Style_VScroll>
		<Style_HScroll>false</Style_HScroll>
		<Style_Transparent>false</Style_Transparent>
		<Style_Checkbox>false</Style_Checkbox>
		<Text>Load UI Skin</Text>
		<TextColor>
				<R>255</R>
				<G>255</G>
				<B>255</B>
		</TextColor>
		<ButtonDrawTemplate>
			<Normal>A_BtnNormal</Normal>
			<Pressed>A_BtnPressed</Pressed>
			<Flyby>A_BtnFlyby</Flyby>
			<Disabled>A_BtnDisabled</Disabled>
			<PressedFlyby>A_BtnPressedFlyby</PressedFlyby>
		</ButtonDrawTemplate>
	</Button>

	
	<Label item="OGP_ItemDroppingLabel">
		<ScreenID>OGP_ItemDroppingLabel</ScreenID>
		<RelativePosition>true</RelativePosition>
		<Location>
			<X>400</X>
			<Y>260</Y>
		</Location>
		<Size>
			<CX>300</CX>
			<CY>28</CY>
		</Size>
		<Text>Fast Drop:</Text>
		<TextColor>
				<R>255</R>
				<G>255</G>
				<B>255</B>
		</TextColor>
		<NoWrap>true</NoWrap>
		<AlignCenter>false</AlignCenter>
		<AlignRight>false</AlignRight>
	</Label>

	<Combobox item="OGP_ItemDroppingCombobox">
		<ScreenID>OGP_ItemDroppingCombobox</ScreenID>
		<DrawTemplate>WDT_Inner</DrawTemplate>
		<Location>
			<X>400</X>
			<Y>300</Y>
		</Location>
		<Size>
			<CX>300</CX>
			<CY>48</CY>
		</Size>
		<ListHeight>60</ListHeight>
		<Button>BDT_Combo</Button>
		<Style_Border>true</Style_Border>
		<Choices>With confirmation</Choices>
		<Choices>Without confirmation</Choices>
		<Choices>Never</Choices>
	</Combobox>

	
	<Button item="OGP_AnonymousCheckbox">
		<ScreenID>OGP_AnonymousCheckbox</ScreenID>
		<RelativePosition>true</RelativePosition>
		<Location>
			<X>20</X>
			<Y>150</Y>
		</Location>
		<Size>
			<CX>300</CX>
			<CY>40</CY>
		</Size>
		<Style_VScroll>false</Style_VScroll>
		<Style_HScroll>false</Style_HScroll>
		<Style_Transparent>false</Style_Transparent>
		<Style_Checkbox>true</Style_Checkbox>
		<Text>Anonymous</Text>
		<TextColor>
				<R>255</R>
				<G>255</G>
				<B>255</B>
		</TextColor>
		<ButtonDrawTemplate>
			<Normal>A_BtnNormal</Normal>
			<Pressed>A_BtnPressed</Pressed>
			<Flyby>A_BtnFlyby</Flyby>
			<Disabled>A_BtnDisabled</Disabled>
			<PressedFlyby>A_BtnPressedFlyby</PressedFlyby>
		</ButtonDrawTemplate>
	</Button>

	
	<Button item="OGP_RoleplayingCheckbox">
		<ScreenID>OGP_RoleplayingCheckbox</ScreenID>
		<RelativePosition>true</RelativePosition>
		<Location>
			<X>20</X>
			<Y>190</Y>
		</Location>
		<Size>
			<CX>300</CX>
			<CY>40</CY>
		</Size>
		<Style_VScroll>false</Style_VScroll>
		<Style_HScroll>false</Style_HScroll>
		<Style_Transparent>false</Style_Transparent>
		<Style_Checkbox>true</Style_Checkbox>
		<Text>Roleplaying</Text>
		<TextColor>
				<R>255</R>
				<G>255</G>
				<B>255</B>
		</TextColor>
		<ButtonDrawTemplate>
			<Normal>A_BtnNormal</Normal>
			<Pressed>A_BtnPressed</Pressed>
			<Flyby>A_BtnFlyby</Flyby>
			<Disabled>A_BtnDisabled</Disabled>
			<PressedFlyby>A_BtnPressedFlyby</PressedFlyby>
		</ButtonDrawTemplate>
	</Button>

	
	<Label item="OGP_SoundRealismLabel">
		<ScreenID>OGP_SoundRealismLabel</ScreenID>
		<RelativePosition>true</RelativePosition>
		<Location>
			<X>400</X>
			<Y>20</Y>
		</Location>
		<Size>
			<CX>300</CX>
			<CY>28</CY>
		</Size>
		<Text>Sound Realism</Text>
		<TextColor>
				<R>255</R>
				<G>255</G>
				<B>255</B>
		</TextColor>
		<NoWrap>true</NoWrap>
		<AlignCenter>false</AlignCenter>
		<AlignRight>false</AlignRight>
	</Label>

	<Slider item="OGP_SoundRealismSlider">
		<ScreenID>OGP_SoundRealismSlider</ScreenID>
		<RelativePosition>true</RelativePosition>
		<Location>
			<X>400</X>
			<Y>60</Y>
		</Location>
		<Size>
			<CX>200</CX>
			<CY>32</CY>
		</Size>
		<SliderArt>SDT_DefSlider</SliderArt>
	</Slider>

	<Label item="OGP_SoundRealismValueLabel">
		<ScreenID>OGP_SoundRealismValueLabel</ScreenID>
		<RelativePosition>true</RelativePosition>
		<Location>
			<X>600</X>
			<Y>60</Y>
		</Location>
		<Size>
			<CX>100</CX>
			<CY>32</CY>
		</Size>
		<Text>0 %</Text>
		<TextColor>
				<R>255</R>
				<G>255</G>
				<B>255</B>
		</TextColor>
		<NoWrap>true</NoWrap>
		<AlignCenter>false</AlignCenter>
		<AlignRight>true</AlignRight>
	</Label>

	
	<Label item="OGP_MusicVolumeLabel">
		<ScreenID>OGP_MusicVolumeLabel</ScreenID>
		<RelativePosition>true</RelativePosition>
		<Location>
			<X>400</X>
			<Y>100</Y>
		</Location>
		<Size>
			<CX>300</CX>
			<CY>28</CY>
		</Size>
		<Text>Music Volume</Text>
		<TextColor>
				<R>255</R>
				<G>255</G>
				<B>255</B>
		</TextColor>
		<NoWrap>true</NoWrap>
		<AlignCenter>false</AlignCenter>
		<AlignRight>false</AlignRight>
	</Label>

	<Slider item="OGP_MusicVolumeSlider">
		<ScreenID>OGP_MusicVolumeSlider</ScreenID>
		<RelativePosition>true</RelativePosition>
		<Location>
			<X>400</X>
			<Y>140</Y>
		</Location>
		<Size>
			<CX>200</CX>
			<CY>32</CY>
		</Size>
		<SliderArt>SDT_DefSlider</SliderArt>
	</Slider>

	<Label item="OGP_MusicVolumeValueLabel">
		<ScreenID>OGP_MusicVolumeValueLabel</ScreenID>
		<RelativePosition>true</RelativePosition>
		<Location>
			<X>600</X>
			<Y>140</Y>
		</Location>
		<Size>
			<CX>100</CX>
			<CY>32</CY>
		</Size>
		<Text>0 %</Text>
		<TextColor>
				<R>255</R>
				<G>255</G>
				<B>255</B>
		</TextColor>
		<NoWrap>true</NoWrap>
		<AlignCenter>false</AlignCenter>
		<AlignRight>true</AlignRight>
	</Label>

	
	<Label item="OGP_SoundVolumeLabel">
		<ScreenID>OGP_SoundVolumeLabel</ScreenID>
		<RelativePosition>true</RelativePosition>
		<Location>
			<X>400</X>
			<Y>180</Y>
		</Location>
		<Size>
			<CX>300</CX>
			<CY>28</CY>
		</Size>
		<Text>Sound Volume</Text>
		<TextColor>
				<R>255</R>
				<G>255</G>
				<B>255</B>
		</TextColor>
		<NoWrap>true</NoWrap>
		<AlignCenter>false</AlignCenter>
		<AlignRight>false</AlignRight>
	</Label>

	<Slider item="OGP_SoundVolumeSlider">
		<ScreenID>OGP_SoundVolumeSlider</ScreenID>
		<RelativePosition>true</RelativePosition>
		<Location>
			<X>400</X>
			<Y>220</Y>
		</Location>
		<Size>
			<CX>200</CX>
			<CY>32</CY>
		</Size>
		<SliderArt>SDT_DefSlider</SliderArt>
	</Slider>

	<Label item="OGP_SoundVolumeValueLabel">
		<ScreenID>OGP_SoundVolumeValueLabel</ScreenID>
		<RelativePosition>true</RelativePosition>
		<Location>
			<X>600</X>
			<Y>220</Y>
		</Location>
		<Size>
			<CX>100</CX>
			<CY>32</CY>
		</Size>
		<Text>0 %</Text>
		<TextColor>
				<R>255</R>
				<G>255</G>
				<B>255</B>
		</TextColor>
		<NoWrap>true</NoWrap>
		<AlignCenter>false</AlignCenter>
		<AlignRight>true</AlignRight>
	</Label>

	
	<Page item="OptionsGeneralPage">
		<ScreenID>OptionsGeneralPage</ScreenID>
		<RelativePosition>true</RelativePosition>

		

		<Style_VScroll>false</Style_VScroll>
		<Style_HScroll>false</Style_HScroll>
		<Style_Transparent>false</Style_Transparent>
		<TooltipReference />
		<DrawTemplate>WDT_Def</DrawTemplate>
		<TabText>General</TabText>
		<TabTextColor>
				<R>255</R>
				<G>255</G>
				<B>255</B>
		</TabTextColor>
		<TabTextActiveColor>
				<R>255</R>
				<G>255</G>
				<B>0</B>
		</TabTextActiveColor>
		<Style_Sizable>true</Style_Sizable>
			<Pieces>OGP_FastItemDestroyCheckbox</Pieces>
			<Pieces>OGP_GuildInvitesCheckbox</Pieces>
			<Pieces>OGP_LootAutosplitCheckbox</Pieces>
			<Pieces>OGP_AnonymousCheckbox</Pieces>
			<Pieces>OGP_RoleplayingCheckbox</Pieces>
			<Pieces>OGP_LoadSkinButton</Pieces>
			<Pieces>OGP_SoundRealismLabel</Pieces>
			<Pieces>OGP_SoundRealismSlider</Pieces>
			<Pieces>OGP_SoundRealismValueLabel</Pieces>
			<Pieces>OGP_MusicVolumeLabel</Pieces>
			<Pieces>OGP_MusicVolumeSlider</Pieces>
			<Pieces>OGP_MusicVolumeValueLabel</Pieces>
			<Pieces>OGP_SoundVolumeLabel</Pieces>
			<Pieces>OGP_SoundVolumeSlider</Pieces>
			<Pieces>OGP_SoundVolumeValueLabel</Pieces>
			<Pieces>OGP_ItemDroppingLabel</Pieces>
			<Pieces>OGP_ItemDroppingCombobox</Pieces>
			<Pieces>OGP_PlayerTradeLabel</Pieces>
			<Pieces>OGP_PlayerTradeCombobox</Pieces>
	</Page>

	

	
	<Button item="ODP_PCNamesCheckbox">
		<ScreenID>ODP_PCNamesCheckbox</ScreenID>
		<RelativePosition>true</RelativePosition>
		<Location>
			<X>20</X>
			<Y>30</Y>
		</Location>
		<Size>
			<CX>300</CX>
			<CY>40</CY>
		</Size>
		<Style_VScroll>false</Style_VScroll>
		<Style_HScroll>false</Style_HScroll>
		<Style_Transparent>false</Style_Transparent>
		<Style_Checkbox>true</Style_Checkbox>
		<Text>Show PC Names</Text>
		<TextColor>
				<R>255</R>
				<G>255</G>
				<B>255</B>
		</TextColor>
		<ButtonDrawTemplate>
			<Normal>A_BtnNormal</Normal>
			<Pressed>A_BtnPressed</Pressed>
			<Flyby>A_BtnFlyby</Flyby>
			<Disabled>A_BtnDisabled</Disabled>
			<PressedFlyby>A_BtnPressedFlyby</PressedFlyby>
		</ButtonDrawTemplate>
	</Button>

	
	<Button item="ODP_NPCNamesCheckbox">
		<ScreenID>ODP_NPCNamesCheckbox</ScreenID>
		<RelativePosition>true</RelativePosition>
		<Location>
			<X>20</X>
			<Y>70</Y>
		</Location>
		<Size>
			<CX>300</CX>
			<CY>40</CY>
		</Size>
		<Style_VScroll>false</Style_VScroll>
		<Style_HScroll>false</Style_HScroll>
		<Style_Transparent>false</Style_Transparent>
		<Style_Checkbox>true</Style_Checkbox>
		<Text>Show NPC Names</Text>
		<TextColor>
				<R>255</R>
				<G>255</G>
				<B>255</B>
		</TextColor>
		<ButtonDrawTemplate>
			<Normal>A_BtnNormal</Normal>
			<Pressed>A_BtnPressed</Pressed>
			<Flyby>A_BtnFlyby</Flyby>
			<Disabled>A_BtnDisabled</Disabled>
			<PressedFlyby>A_BtnPressedFlyby</PressedFlyby>
		</ButtonDrawTemplate>
	</Button>

	
	<Button item="ODP_LevelOfDetailCheckbox">
		<ScreenID>ODP_LevelOfDetailCheckbox</ScreenID>
		<RelativePosition>true</RelativePosition>
		<Location>
			<X>20</X>
			<Y>110</Y>
		</Location>
		<Size>
			<CX>300</CX>
			<CY>40</CY>
		</Size>
		<Style_VScroll>false</Style_VScroll>
		<Style_HScroll>false</Style_HScroll>
		<Style_Transparent>false</Style_Transparent>
		<Style_Checkbox>true</Style_Checkbox>
		<Text>Level of Detail</Text>
		<TextColor>
				<R>255</R>
				<G>255</G>
				<B>255</B>
		</TextColor>
		<ButtonDrawTemplate>
			<Normal>A_BtnNormal</Normal>
			<Pressed>A_BtnPressed</Pressed>
			<Flyby>A_BtnFlyby</Flyby>
			<Disabled>A_BtnDisabled</Disabled>
			<PressedFlyby>A_BtnPressedFlyby</PressedFlyby>
		</ButtonDrawTemplate>
	</Button>

	
	<Label item="ODP_SkyLabel">
		<ScreenID>ODP_SkyLabel</ScreenID>
		<RelativePosition>true</RelativePosition>
		<Location>
			<X>20</X>
			<Y>200</Y>
		</Location>
		<Size>
			<CX>300</CX>
			<CY>28</CY>
		</Size>
		<Text>Sky:</Text>
		<TextColor>
				<R>255</R>
				<G>255</G>
				<B>255</B>
		</TextColor>
		<NoWrap>true</NoWrap>
		<AlignCenter>false</AlignCenter>
		<AlignRight>false</AlignRight>
	</Label>

	<Combobox item="ODP_SkyCombobox">
		<ScreenID>ODP_SkyCombobox</ScreenID>
		<DrawTemplate>WDT_Inner</DrawTemplate>
		<Location>
			<X>20</X>
			<Y>240</Y>
		</Location>
		<Size>
			<CX>300</CX>
			<CY>48</CY>
		</Size>
		<ListHeight>100</ListHeight>
		<Button>BDT_Combo</Button>
		<Style_Border>true</Style_Border>
		<Choices>3D Sky Off</Choices>
		<Choices>Single Layer</Choices>
		<Choices>Double Layer</Choices>
	</Combobox>
	
	
	<Label item="ODP_GammaLabel">
		<ScreenID>ODP_GammaLabel</ScreenID>
		<RelativePosition>true</RelativePosition>
		<Location>
			<X>400</X>
			<Y>20</Y>
		</Location>
		<Size>
			<CX>300</CX>
			<CY>28</CY>
		</Size>
		<Text>Gamma</Text>
		<TextColor>
				<R>255</R>
				<G>255</G>
				<B>255</B>
		</TextColor>
		<NoWrap>true</NoWrap>
		<AlignCenter>false</AlignCenter>
		<AlignRight>false</AlignRight>
	</Label>

	<Slider item="ODP_GammaSlider">
		<ScreenID>ODP_GammaSlider</ScreenID>
		<RelativePosition>true</RelativePosition>
		<Location>
			<X>400</X>
			<Y>60</Y>
		</Location>
		<Size>
			<CX>200</CX>
			<CY>32</CY>
		</Size>
		<SliderArt>SDT_DefSlider</SliderArt>
	</Slider>

	<Label item="ODP_GammaValueLabel">
		<ScreenID>ODP_GammaValueLabel</ScreenID>
		<RelativePosition>true</RelativePosition>
		<Location>
			<X>600</X>
			<Y>60</Y>
		</Location>
		<Size>
			<CX>100</CX>
			<CY>32</CY>
		</Size>
		<Text>0 %</Text>
		<TextColor>
				<R>255</R>
				<G>255</G>
				<B>255</B>
		</TextColor>
		<NoWrap>true</NoWrap>
		<AlignCenter>false</AlignCenter>
		<AlignRight>true</AlignRight>
	</Label>

	
	<Label item="ODP_ClipPlaneLabel">
		<ScreenID>ODP_ClipPlaneLabel</ScreenID>
		<RelativePosition>true</RelativePosition>
		<Location>
			<X>400</X>
			<Y>104</Y>
		</Location>
		<Size>
			<CX>300</CX>
			<CY>28</CY>
		</Size>
		<Text>Far Clip Plane</Text>
		<TextColor>
				<R>255</R>
				<G>255</G>
				<B>255</B>
		</TextColor>
		<NoWrap>true</NoWrap>
		<AlignCenter>false</AlignCenter>
		<AlignRight>false</AlignRight>
	</Label>

	<Slider item="ODP_ClipPlaneSlider">
		<ScreenID>ODP_ClipPlaneSlider</ScreenID>
		<RelativePosition>true</RelativePosition>
		<Location>
			<X>400</X>
			<Y>144</Y>
		</Location>
		<Size>
			<CX>200</CX>
			<CY>32</CY>
		</Size>
		<SliderArt>SDT_DefSlider</SliderArt>
	</Slider>

	<Label item="ODP_ClipPlaneValueLabel">
		<ScreenID>ODP_ClipPlaneValueLabel</ScreenID>
		<RelativePosition>true</RelativePosition>
		<Location>
			<X>600</X>
			<Y>144</Y>
		</Location>
		<Size>
			<CX>100</CX>
			<CY>32</CY>
		</Size>
		<Text>0 %</Text>
		<TextColor>
				<R>255</R>
				<G>255</G>
				<B>255</B>
		</TextColor>
		<NoWrap>true</NoWrap>
		<AlignCenter>false</AlignCenter>
		<AlignRight>true</AlignRight>
	</Label>

	
	<Button item="ODP_VideoModesButton">
		<ScreenID>ODP_VideoModesButton</ScreenID>
		<RelativePosition>true</RelativePosition>
		<Location>
			<X>20</X>
			<Y>150</Y>
		</Location>
		<Size>
			<CX>300</CX>
			<CY>40</CY>
		</Size>
		<Style_VScroll>false</Style_VScroll>
		<Style_HScroll>false</Style_HScroll>
		<Style_Transparent>false</Style_Transparent>
		<Text>Video Modes</Text>
		<TextColor>
				<R>255</R>
				<G>255</G>
				<B>255</B>
		</TextColor>
		<ButtonDrawTemplate>
			<Normal>A_BtnNormal</Normal>
			<Pressed>A_BtnPressed</Pressed>
			<Flyby>A_BtnFlyby</Flyby>
			<Disabled>A_BtnDisabled</Disabled>
			<PressedFlyby>A_BtnPressedFlyby</PressedFlyby>
		</ButtonDrawTemplate>
	</Button>

    
	<Label item="ODP_SpellParticlesLabel">
		<ScreenID>ODP_SpellParticlesLabel</ScreenID>
		<RelativePosition>true</RelativePosition>
		<Location>
			<X>400</X>
			<Y>200</Y>
		</Location>
		<Size>
			<CX>300</CX>
			<CY>28</CY>
		</Size>
		<Text>Spell Particles</Text>
		<TextColor>
				<R>255</R>
				<G>255</G>
				<B>255</B>
		</TextColor>
		<NoWrap>true</NoWrap>
		<AlignCenter>false</AlignCenter>
		<AlignRight>false</AlignRight>
	</Label>

	
	<Label item="ODP_SpellParticlesNearClipLabel">
		<ScreenID>ODP_SpellParticlesNearClipLabel</ScreenID>
		<RelativePosition>true</RelativePosition>
		<Location>
			<X>440</X>
			<Y>240</Y>
		</Location>
		<Size>
			<CX>200</CX>
			<CY>28</CY>
		</Size>
		<Text>Near Clip Plane:</Text>
		<TextColor>
				<R>255</R>
				<G>255</G>
				<B>255</B>
		</TextColor>
		<NoWrap>true</NoWrap>
		<AlignCenter>false</AlignCenter>
		<AlignRight>false</AlignRight>
	</Label>

	<Combobox item="ODP_SpellParticlesNearClipCombobox">
		<ScreenID>ODP_SpellParticlesNearClipCombobox</ScreenID>
		<DrawTemplate>WDT_Inner</DrawTemplate>
		<Location>
			<X>440</X>
			<Y>280</Y>
		</Location>
		<Size>
			<CX>200</CX>
			<CY>48</CY>
		</Size>
		<ListHeight>100</ListHeight>
		<Button>BDT_Combo</Button>
		<Style_Border>true</Style_Border>
		<Choices>Near</Choices>
		<Choices>Medium</Choices>
		<Choices>Far</Choices>
		<Choices>Farthest</Choices>
	</Combobox>

	
	<Label item="ODP_SpellParticlesDensityLabel">
		<ScreenID>ODP_SpellParticlesDensityLabel</ScreenID>
		<RelativePosition>true</RelativePosition>
		<Location>
			<X>440</X>
			<Y>340</Y>
		</Location>
		<Size>
			<CX>200</CX>
			<CY>28</CY>
		</Size>
		<Text>Density:</Text>
		<TextColor>
				<R>255</R>
				<G>255</G>
				<B>255</B>
		</TextColor>
		<NoWrap>true</NoWrap>
		<AlignCenter>false</AlignCenter>
		<AlignRight>false</AlignRight>
	</Label>

	<Combobox item="ODP_SpellParticlesDensityCombobox">
		<ScreenID>ODP_SpellParticlesDensityCombobox</ScreenID>
		<DrawTemplate>WDT_Inner</DrawTemplate>
		<Location>
			<X>440</X>
			<Y>380</Y>
		</Location>
		<Size>
			<CX>200</CX>
			<CY>48</CY>
		</Size>
		<ListHeight>100</ListHeight>
		<Button>BDT_Combo</Button>
		<Style_Border>true</Style_Border>
		<Choices>Off</Choices>
		<Choices>Low</Choices>
		<Choices>Medium</Choices>
		<Choices>High</Choices>
	</Combobox>

	
	<Label item="ODP_SpellParticlesOpacityLabel">
		<ScreenID>ODP_SpellParticlesOpacityLabel</ScreenID>
		<RelativePosition>true</RelativePosition>
		<Location>
			<X>440</X>
			<Y>440</Y>
		</Location>
		<Size>
			<CX>300</CX>
			<CY>28</CY>
		</Size>
		<Text>Opacity</Text>
		<TextColor>
				<R>255</R>
				<G>255</G>
				<B>255</B>
		</TextColor>
		<NoWrap>true</NoWrap>
		<AlignCenter>false</AlignCenter>
		<AlignRight>false</AlignRight>
	</Label>

	<Slider item="ODP_SpellParticlesOpacitySlider">
		<ScreenID>ODP_SpellParticlesOpacitySlider</ScreenID>
		<RelativePosition>true</RelativePosition>
		<Location>
			<X>440</X>
			<Y>480</Y>
		</Location>
		<Size>
			<CX>200</CX>
			<CY>32</CY>
		</Size>
		<SliderArt>SDT_DefSlider</SliderArt>
	</Slider>

	<Label item="ODP_SpellParticlesOpacityValueLabel">
		<ScreenID>ODP_SpellParticlesOpacityValueLabel</ScreenID>
		<RelativePosition>true</RelativePosition>
		<Location>
			<X>620</X>
			<Y>480</Y>
		</Location>
		<Size>
			<CX>100</CX>
			<CY>32</CY>
		</Size>
		<Text>0 %</Text>
		<TextColor>
				<R>255</R>
				<G>255</G>
				<B>255</B>
		</TextColor>
		<NoWrap>true</NoWrap>
		<AlignCenter>false</AlignCenter>
		<AlignRight>true</AlignRight>
	</Label>

    
	<Label item="ODP_OtherParticlesLabel">
		<ScreenID>ODP_OtherParticlesLabel</ScreenID>
		<RelativePosition>true</RelativePosition>
		<Location>
			<X>400</X>
			<Y>540</Y>
		</Location>
		<Size>
			<CX>300</CX>
			<CY>28</CY>
		</Size>
		<Text>Other Particles</Text>
		<TextColor>
				<R>255</R>
				<G>255</G>
				<B>255</B>
		</TextColor>
		<NoWrap>true</NoWrap>
		<AlignCenter>false</AlignCenter>
		<AlignRight>false</AlignRight>
	</Label>

	
	<Label item="ODP_ParticleDensityLabel">
		<ScreenID>ODP_ParticleDensityLabel</ScreenID>
		<RelativePosition>true</RelativePosition>
		<Location>
			<X>440</X>
			<Y>580</Y>
		</Location>
		<Size>
			<CX>200</CX>
			<CY>28</CY>
		</Size>
		<Text>Density:</Text>
		<TextColor>
				<R>255</R>
				<G>255</G>
				<B>255</B>
		</TextColor>
		<NoWrap>true</NoWrap>
		<AlignCenter>false</AlignCenter>
		<AlignRight>false</AlignRight>
	</Label>

	<Combobox item="ODP_ParticleDensityCombobox">
		<ScreenID>ODP_ParticleDensityCombobox</ScreenID>
		<DrawTemplate>WDT_Inner</DrawTemplate>
		<Location>
			<X>440</X>
			<Y>620</Y>
		</Location>
		<Size>
			<CX>200</CX>
			<CY>48</CY>
		</Size>
		<ListHeight>100</ListHeight>
		<Button>BDT_Combo</Button>
		<Style_Border>true</Style_Border>
		<Choices>Off</Choices>
		<Choices>Low</Choices>
		<Choices>Medium</Choices>
		<Choices>High</Choices>
	</Combobox>

	
	<Label item="ODP_FadeDelayLabel">
		<ScreenID>ODP_FadeDelayLabel</ScreenID>
		<RelativePosition>true</RelativePosition>
		<Location>
			<X>20</X>
			<Y>300</Y>
		</Location>
		<Size>
			<CX>300</CX>
			<CY>28</CY>
		</Size>
		<Text>Fade Delay (seconds)</Text>
		<TextColor>
				<R>255</R>
				<G>255</G>
				<B>255</B>
		</TextColor>
		<NoWrap>true</NoWrap>
		<AlignCenter>false</AlignCenter>
		<AlignRight>false</AlignRight>
	</Label>

	<Slider item="ODP_FadeDelaySlider">
		<ScreenID>ODP_FadeDelaySlider</ScreenID>
		<RelativePosition>true</RelativePosition>
		<Location>
			<X>20</X>
			<Y>340</Y>
		</Location>
		<Size>
			<CX>200</CX>
			<CY>32</CY>
		</Size>
		<SliderArt>SDT_DefSlider</SliderArt>
	</Slider>

	<Label item="ODP_FadeDelayValueLabel">
		<ScreenID>ODP_FadeDelayValueLabel</ScreenID>
		<RelativePosition>true</RelativePosition>
		<Location>
			<X>220</X>
			<Y>340</Y>
		</Location>
		<Size>
			<CX>100</CX>
			<CY>32</CY>
		</Size>
		<Text>0.0</Text>
		<TextColor>
				<R>255</R>
				<G>255</G>
				<B>255</B>
		</TextColor>
		<NoWrap>true</NoWrap>
		<AlignCenter>false</AlignCenter>
		<AlignRight>true</AlignRight>
	</Label>

	
	<Label item="ODP_FadeDurationLabel">
		<ScreenID>ODP_FadeDurationLabel</ScreenID>
		<RelativePosition>true</RelativePosition>
		<Location>
			<X>20</X>
			<Y>380</Y>
		</Location>
		<Size>
			<CX>300</CX>
			<CY>28</CY>
		</Size>
		<Text>Fade Duration (seconds)</Text>
		<TextColor>
				<R>255</R>
				<G>255</G>
				<B>255</B>
		</TextColor>
		<NoWrap>true</NoWrap>
		<AlignCenter>false</AlignCenter>
		<AlignRight>false</AlignRight>
	</Label>

	<Slider item="ODP_FadeDurationSlider">
		<ScreenID>ODP_FadeDurationSlider</ScreenID>
		<RelativePosition>true</RelativePosition>
		<Location>
			<X>20</X>
			<Y>420</Y>
		</Location>
		<Size>
			<CX>200</CX>
			<CY>32</CY>
		</Size>
		<SliderArt>SDT_DefSlider</SliderArt>
	</Slider>

	<Label item="ODP_FadeDurationValueLabel">
		<ScreenID>ODP_FadeDurationValueLabel</ScreenID>
		<RelativePosition>true</RelativePosition>
		<Location>
			<X>220</X>
			<Y>420</Y>
		</Location>
		<Size>
			<CX>100</CX>
			<CY>32</CY>
		</Size>
		<Text>0.5</Text>
		<TextColor>
				<R>255</R>
				<G>255</G>
				<B>255</B>
		</TextColor>
		<NoWrap>true</NoWrap>
		<AlignCenter>false</AlignCenter>
		<AlignRight>true</AlignRight>
	</Label>

	
	<Label item="ODP_WindowAlphaLabel">
		<ScreenID>ODP_WindowAlphaLabel</ScreenID>
		<RelativePosition>true</RelativePosition>
		<Location>
			<X>20</X>
			<Y>460</Y>
		</Location>
		<Size>
			<CX>300</CX>
			<CY>28</CY>
		</Size>
		<Text>Window Transparency</Text>
		<TextColor>
				<R>255</R>
				<G>255</G>
				<B>255</B>
		</TextColor>
		<NoWrap>true</NoWrap>
		<AlignCenter>false</AlignCenter>
		<AlignRight>false</AlignRight>
	</Label>

	<Slider item="ODP_WindowAlphaSlider">
		<ScreenID>ODP_WindowAlphaSlider</ScreenID>
		<RelativePosition>true</RelativePosition>
		<Location>
			<X>20</X>
			<Y>500</Y>
		</Location>
		<Size>
			<CX>200</CX>
			<CY>32</CY>
		</Size>
		<SliderArt>SDT_DefSlider</SliderArt>
	</Slider>

	<Label item="ODP_WindowAlphaValueLabel">
		<ScreenID>ODP_WindowAlphaValueLabel</ScreenID>
		<RelativePosition>true</RelativePosition>
		<Location>
			<X>220</X>
			<Y>500</Y>
		</Location>
		<Size>
			<CX>100</CX>
			<CY>32</CY>
		</Size>
		<Text>0 %</Text>
		<TextColor>
				<R>255</R>
				<G>255</G>
				<B>255</B>
		</TextColor>
		<NoWrap>true</NoWrap>
		<AlignCenter>false</AlignCenter>
		<AlignRight>true</AlignRight>
	</Label>

	
	<Label item="ODP_FadeToAlphaLabel">
		<ScreenID>ODP_FadeToAlphaLabel</ScreenID>
		<RelativePosition>true</RelativePosition>
		<Location>
			<X>20</X>
			<Y>540</Y>
		</Location>
		<Size>
			<CX>300</CX>
			<CY>28</CY>
		</Size>
		<Text>Fade-to Transparency</Text>
		<TextColor>
				<R>255</R>
				<G>255</G>
				<B>255</B>
		</TextColor>
		<NoWrap>true</NoWrap>
		<AlignCenter>false</AlignCenter>
		<AlignRight>false</AlignRight>
	</Label>

	<Slider item="ODP_FadeToAlphaSlider">
		<ScreenID>ODP_FadeToAlphaSlider</ScreenID>
		<RelativePosition>true</RelativePosition>
		<Location>
			<X>20</X>
			<Y>580</Y>
		</Location>
		<Size>
			<CX>200</CX>
			<CY>32</CY>
		</Size>
		<SliderArt>SDT_DefSlider</SliderArt>
	</Slider>

	<Label item="ODP_FadeToAlphaValueLabel">
		<ScreenID>ODP_FadeToAlphaValueLabel</ScreenID>
		<RelativePosition>true</RelativePosition>
		<Location>
			<X>220</X>
			<Y>580</Y>
		</Location>
		<Size>
			<CX>100</CX>
			<CY>32</CY>
		</Size>
		<Text>0 %</Text>
		<TextColor>
				<R>255</R>
				<G>255</G>
				<B>255</B>
		</TextColor>
		<NoWrap>true</NoWrap>
		<AlignCenter>false</AlignCenter>
		<AlignRight>true</AlignRight>
	</Label>

	
	<Page item="OptionsDisplayPage">
		<ScreenID>OptionsDisplayPage</ScreenID>
		<RelativePosition>true</RelativePosition>

		

		<Style_VScroll>false</Style_VScroll>
		<Style_HScroll>false</Style_HScroll>
		<Style_Transparent>false</Style_Transparent>
		<DrawTemplate>WDT_Def</DrawTemplate>
		<TabText>Display</TabText>
		<TabTextColor>
				<R>255</R>
				<G>255</G>
				<B>255</B>
		</TabTextColor>
		<TabTextActiveColor>
				<R>255</R>
				<G>255</G>
				<B>0</B>
		</TabTextActiveColor>
		<Style_Sizable>true</Style_Sizable>
			<Pieces>ODP_PCNamesCheckbox</Pieces>
			<Pieces>ODP_NPCNamesCheckbox</Pieces>
			<Pieces>ODP_LevelOfDetailCheckbox</Pieces>
			<Pieces>ODP_GammaLabel</Pieces>
			<Pieces>ODP_GammaSlider</Pieces>
			<Pieces>ODP_GammaValueLabel</Pieces>
			<Pieces>ODP_ClipPlaneLabel</Pieces>
			<Pieces>ODP_ClipPlaneSlider</Pieces>
			<Pieces>ODP_ClipPlaneValueLabel</Pieces>
			<Pieces>ODP_VideoModesButton</Pieces>
			<Pieces>ODP_FadeDelayLabel</Pieces>
			<Pieces>ODP_FadeDelaySlider</Pieces>
			<Pieces>ODP_FadeDelayValueLabel</Pieces>
			<Pieces>ODP_FadeDurationLabel</Pieces>
			<Pieces>ODP_FadeDurationSlider</Pieces>
			<Pieces>ODP_FadeDurationValueLabel</Pieces>
			<Pieces>ODP_WindowAlphaLabel</Pieces>
			<Pieces>ODP_WindowAlphaSlider</Pieces>
			<Pieces>ODP_WindowAlphaValueLabel</Pieces>
			<Pieces>ODP_FadeToAlphaLabel</Pieces>
			<Pieces>ODP_FadeToAlphaSlider</Pieces>
			<Pieces>ODP_FadeToAlphaValueLabel</Pieces>
			<Pieces>ODP_SkyLabel</Pieces>
			<Pieces>ODP_SkyCombobox</Pieces>
			<Pieces>ODP_SpellParticlesLabel</Pieces>
			<Pieces>ODP_OtherParticlesLabel</Pieces>
			<Pieces>ODP_SpellParticlesOpacityLabel</Pieces>
			<Pieces>ODP_SpellParticlesOpacitySlider</Pieces>
			<Pieces>ODP_SpellParticlesOpacityValueLabel</Pieces>
			<Pieces>ODP_ParticleDensityLabel</Pieces>
			<Pieces>ODP_ParticleDensityCombobox</Pieces>
			<Pieces>ODP_SpellParticlesDensityLabel</Pieces>
			<Pieces>ODP_SpellParticlesDensityCombobox</Pieces>
			<Pieces>ODP_SpellParticlesNearClipLabel</Pieces>
			<Pieces>ODP_SpellParticlesNearClipCombobox</Pieces>
	</Page>

	

	
	<Button item="OMP_InvertYAxisCheckbox">
		<ScreenID>OMP_InvertYAxisCheckbox</ScreenID>
		<RelativePosition>true</RelativePosition>
		<Location>
			<X>20</X>
			<Y>20</Y>
		</Location>
		<Size>
			<CX>300</CX>
			<CY>40</CY>
		</Size>
		<Style_VScroll>false</Style_VScroll>
		<Style_HScroll>false</Style_HScroll>
		<Style_Transparent>false</Style_Transparent>
		<Style_Checkbox>true</Style_Checkbox>
		<Text>Invert Y Axis</Text>
		<TextColor>
				<R>255</R>
				<G>255</G>
				<B>255</B>
		</TextColor>
		<ButtonDrawTemplate>
			<Normal>A_BtnNormal</Normal>
			<Pressed>A_BtnPressed</Pressed>
			<Flyby>A_BtnFlyby</Flyby>
			<Disabled>A_BtnDisabled</Disabled>
			<PressedFlyby>A_BtnPressedFlyby</PressedFlyby>
		</ButtonDrawTemplate>
	</Button>

	
	<Button item="OMP_LookSpringCheckbox">
		<ScreenID>OMP_LookSpringCheckbox</ScreenID>
		<RelativePosition>true</RelativePosition>
		<Location>
			<X>20</X>
			<Y>60</Y>
		</Location>
		<Size>
			<CX>300</CX>
			<CY>40</CY>
		</Size>
		<Style_VScroll>false</Style_VScroll>
		<Style_HScroll>false</Style_HScroll>
		<Style_Transparent>false</Style_Transparent>
		<Style_Checkbox>true</Style_Checkbox>
		<Text>Look Spring</Text>
		<TextColor>
				<R>255</R>
				<G>255</G>
				<B>255</B>
		</TextColor>
		<ButtonDrawTemplate>
			<Normal>A_BtnNormal</Normal>
			<Pressed>A_BtnPressed</Pressed>
			<Flyby>A_BtnFlyby</Flyby>
			<Disabled>A_BtnDisabled</Disabled>
			<PressedFlyby>A_BtnPressedFlyby</PressedFlyby>
		</ButtonDrawTemplate>
	</Button>

	
	<Button item="OMP_MouseLookCheckbox">
		<ScreenID>OMP_MouseLookCheckbox</ScreenID>
		<RelativePosition>true</RelativePosition>
		<Location>
			<X>20</X>
			<Y>100</Y>
		</Location>
		<Size>
			<CX>300</CX>
			<CY>40</CY>
		</Size>
		<Style_VScroll>false</Style_VScroll>
		<Style_HScroll>false</Style_HScroll>
		<Style_Transparent>false</Style_Transparent>
		<Style_Checkbox>true</Style_Checkbox>
		<Text>Mouse Look</Text>
		<TextColor>
				<R>255</R>
				<G>255</G>
				<B>255</B>
		</TextColor>
		<ButtonDrawTemplate>
			<Normal>A_BtnNormal</Normal>
			<Pressed>A_BtnPressed</Pressed>
			<Flyby>A_BtnFlyby</Flyby>
			<Disabled>A_BtnDisabled</Disabled>
			<PressedFlyby>A_BtnPressedFlyby</PressedFlyby>
		</ButtonDrawTemplate>
	</Button>

	
	<Label item="OMP_MouseSensitivityLabel">
		<ScreenID>OMP_MouseSensitivityLabel</ScreenID>
		<RelativePosition>true</RelativePosition>
		<Location>
			<X>400</X>
			<Y>20</Y>
		</Location>
		<Size>
			<CX>300</CX>
			<CY>28</CY>
		</Size>
		<Text>Mouselook Sensitivity</Text>
		<TextColor>
				<R>255</R>
				<G>255</G>
				<B>255</B>
		</TextColor>
		<NoWrap>true</NoWrap>
		<AlignCenter>false</AlignCenter>
		<AlignRight>false</AlignRight>
	</Label>

	<Slider item="OMP_MouseSensitivitySlider">
		<ScreenID>OMP_MouseSensitivitySlider</ScreenID>
		<RelativePosition>true</RelativePosition>
		<Location>
			<X>400</X>
			<Y>60</Y>
		</Location>
		<Size>
			<CX>200</CX>
			<CY>32</CY>
		</Size>
		<SliderArt>SDT_DefSlider</SliderArt>
	</Slider>

	<Label item="OMP_MouseSensitivityValueLabel">
		<ScreenID>OMP_MouseSensitivityValueLabel</ScreenID>
		<RelativePosition>true</RelativePosition>
		<Location>
			<X>600</X>
			<Y>60</Y>
		</Location>
		<Size>
			<CX>100</CX>
			<CY>32</CY>
		</Size>
		<Text>0 %</Text>
		<TextColor>
				<R>255</R>
				<G>255</G>
				<B>255</B>
		</TextColor>
		<NoWrap>true</NoWrap>
		<AlignCenter>false</AlignCenter>
		<AlignRight>true</AlignRight>
	</Label>

	
	<Page item="OptionsMousePage">
		<ScreenID>OptionsMousePage</ScreenID>
		<RelativePosition>true</RelativePosition>

		

		<Style_VScroll>false</Style_VScroll>
		<Style_HScroll>false</Style_HScroll>
		<Style_Transparent>false</Style_Transparent>
		<TooltipReference />
		<DrawTemplate>WDT_Def</DrawTemplate>
		<TabText>Mouse</TabText>
		<TabTextColor>
				<R>255</R>
				<G>255</G>
				<B>255</B>
		</TabTextColor>
		<TabTextActiveColor>
				<R>255</R>
				<G>255</G>
				<B>0</B>
		</TabTextActiveColor>
		<Style_Sizable>true</Style_Sizable>
			<Pieces>OMP_InvertYAxisCheckbox</Pieces>
			<Pieces>OMP_LookSpringCheckbox</Pieces>
			<Pieces>OMP_MouseLookCheckbox</Pieces>
			<Pieces>OMP_MouseSensitivityLabel</Pieces>
			<Pieces>OMP_MouseSensitivitySlider</Pieces>
			<Pieces>OMP_MouseSensitivityValueLabel</Pieces>
	</Page>

	
	
	<Label item="OKP_KeyboardFilterLabel">
		<ScreenID>OKP_KeyboardFilterLabel</ScreenID>
		<RelativePosition>true</RelativePosition>
		<Location>
			<X>20</X>
			<Y>20</Y>
		</Location>
		<Size>
			<CX>240</CX>
			<CY>28</CY>
		</Size>
		<Text>Select category:</Text>
		<TextColor>
				<R>255</R>
				<G>255</G>
				<B>255</B>
		</TextColor>
		<NoWrap>true</NoWrap>
		<AlignCenter>false</AlignCenter>
		<AlignRight>false</AlignRight>
	</Label>

	<Combobox item="OKP_KeyboardFilterCombobox">
		<ScreenID>OKP_KeyboardFilterCombobox</ScreenID>
		<DrawTemplate>WDT_Inner</DrawTemplate>
		<Location>
			<X>40</X>
			<Y>48</Y>
		</Location>
		<Size>
			<CX>300</CX>
			<CY>48</CY>
		</Size>
		<ListHeight>300</ListHeight>
		<Button>BDT_Combo</Button>
		<Style_Border>true</Style_Border>
			<Choices>Movement</Choices>)
			<Choices>Commands</Choices>)
			<Choices>Spell casting</Choices>
			<Choices>Target</Choices>
			<Choices>Camera</Choices>
			<Choices>Chat</Choices>
			<Choices>UI</Choices>
			<Choices>Macros</Choices>
			<Choices>All</Choices>
	</Combobox>

	
	<Button item="OKP_DefaultKeysButton">
		<ScreenID>OKP_DefaultKeysButton</ScreenID>
		<RelativePosition>true</RelativePosition>
		<Location>
			<X>400</X>
			<Y>30</Y>
		</Location>
		<Size>
			<CX>300</CX>
			<CY>40</CY>
		</Size>
		<Style_VScroll>false</Style_VScroll>
		<Style_HScroll>false</Style_HScroll>
		<Style_Transparent>false</Style_Transparent>
		<Text>Reload defaults</Text>
		<TextColor>
				<R>255</R>
				<G>255</G>
				<B>255</B>
		</TextColor>
		<ButtonDrawTemplate>
			<Normal>A_BtnNormal</Normal>
			<Pressed>A_BtnPressed</Pressed>
			<Flyby>A_BtnFlyby</Flyby>
			<Disabled>A_BtnDisabled</Disabled>
			<PressedFlyby>A_BtnPressedFlyby</PressedFlyby>
		</ButtonDrawTemplate>
	</Button>

	
	<Listbox item="OKP_KeyboardAssignmentList">
		<ScreenID>OKP_KeyboardAssignmentList</ScreenID>
		<DrawTemplate>WDT_Inner</DrawTemplate>
		<RelativePosition>true</RelativePosition>
		
		<AutoStretch>true</AutoStretch>
		<TopAnchorToTop>true</TopAnchorToTop>
		<BottomAnchorToTop>false</BottomAnchorToTop>
		<LeftAnchorToLeft>true</LeftAnchorToLeft>
		<RightAnchorToLeft>false</RightAnchorToLeft>
		<TopAnchorOffset>120</TopAnchorOffset>
		<LeftAnchorOffset>10</LeftAnchorOffset>
		<RightAnchorOffset>10</RightAnchorOffset>
		<BottomAnchorOffset>10</BottomAnchorOffset>
		<Style_Border>true</Style_Border>
		<Style_VScroll>true</Style_VScroll>
		<Columns>
			<Width>360</Width>
			<Heading>Command</Heading>
		</Columns>
		<Columns>
			<Width>150</Width>
			<Heading>Keypress</Heading>
		</Columns>
		<Columns>
			<Width>150</Width>
			<Heading>Alternate</Heading>
		</Columns>
	</Listbox>

	
	<Page item="OptionsKeyboardPage">
		<ScreenID>OptionsKeyboardPage</ScreenID>
		<RelativePosition>true</RelativePosition>

		

		<Style_VScroll>false</Style_VScroll>
		<Style_HScroll>false</Style_HScroll>
		<Style_Transparent>false</Style_Transparent>
		<DrawTemplate>WDT_Def</DrawTemplate>
		<TabText>Keyboard</TabText>
		<TabTextColor>
				<R>255</R>
				<G>255</G>
				<B>255</B>
		</TabTextColor>
		<TabTextActiveColor>
				<R>255</R>
				<G>255</G>
				<B>0</B>
		</TabTextActiveColor>
		<Style_Sizable>true</Style_Sizable>
			<Pieces>OKP_KeyboardAssignmentList</Pieces>
			<Pieces>OKP_DefaultKeysButton</Pieces>
			<Pieces>OKP_KeyboardFilterLabel</Pieces>
			<Pieces>OKP_KeyboardFilterCombobox</Pieces>
	</Page>

	
	<Button item="OFP_GuildChat">
		<ScreenID>Chat0</ScreenID>
		<RelativePosition>true</RelativePosition>
		<Location>
			<X>20</X>
			<Y>20</Y>
		</Location>
		<Size>
			<CX>300</CX>
			<CY>40</CY>
		</Size>
		<Style_VScroll>false</Style_VScroll>
		<Style_HScroll>false</Style_HScroll>
		<Style_Transparent>false</Style_Transparent>
		<Style_Checkbox>true</Style_Checkbox>
		<Text>Guild Chat</Text>
		<TextColor>
				<R>255</R>
				<G>255</G>
				<B>255</B>
		</TextColor>
		<ButtonDrawTemplate>
			<Normal>A_BtnNormal</Normal>
			<Pressed>A_BtnPressed</Pressed>
			<Flyby>A_BtnFlyby</Flyby>
			<Disabled>A_BtnDisabled</Disabled>
			<PressedFlyby>A_BtnPressedFlyby</PressedFlyby>
		</ButtonDrawTemplate>
	</Button>
	<Button item="OFP_Socials">
		<ScreenID>Chat1</ScreenID>
		<RelativePosition>true</RelativePosition>
		<Location>
			<X>20</X>
			<Y>60</Y>
		</Location>
		<Size>
			<CX>300</CX>
			<CY>40</CY>
		</Size>
		<Style_VScroll>false</Style_VScroll>
		<Style_HScroll>false</Style_HScroll>
		<Style_Transparent>false</Style_Transparent>
		<Style_Checkbox>true</Style_Checkbox>
		<Text>Socials</Text>
		<TextColor>
				<R>255</R>
				<G>255</G>
				<B>255</B>
		</TextColor>
		<ButtonDrawTemplate>
			<Normal>A_BtnNormal</Normal>
			<Pressed>A_BtnPressed</Pressed>
			<Flyby>A_BtnFlyby</Flyby>
			<Disabled>A_BtnDisabled</Disabled>
			<PressedFlyby>A_BtnPressedFlyby</PressedFlyby>
		</ButtonDrawTemplate>
	</Button>
	<Button item="OFP_GroupChat">
		<ScreenID>Chat2</ScreenID>
		<RelativePosition>true</RelativePosition>
		<Location>
			<X>20</X>
			<Y>100</Y>
		</Location>
		<Size>
			<CX>300</CX>
			<CY>40</CY>
		</Size>
		<Style_VScroll>false</Style_VScroll>
		<Style_HScroll>false</Style_HScroll>
		<Style_Transparent>false</Style_Transparent>
		<Style_Checkbox>true</Style_Checkbox>
		<Text>Group Chat</Text>
		<TextColor>
				<R>255</R>
				<G>255</G>
				<B>255</B>
		</TextColor>
		<ButtonDrawTemplate>
			<Normal>A_BtnNormal</Normal>
			<Pressed>A_BtnPressed</Pressed>
			<Flyby>A_BtnFlyby</Flyby>
			<Disabled>A_BtnDisabled</Disabled>
			<PressedFlyby>A_BtnPressedFlyby</PressedFlyby>
		</ButtonDrawTemplate>
	</Button>
	<Button item="OFP_Shouts">
		<ScreenID>Chat3</ScreenID>
		<RelativePosition>true</RelativePosition>
		<Location>
			<X>20</X>
			<Y>140</Y>
		</Location>
		<Size>
			<CX>300</CX>
			<CY>40</CY>
		</Size>
		<Style_VScroll>false</Style_VScroll>
		<Style_HScroll>false</Style_HScroll>
		<Style_Transparent>false</Style_Transparent>
		<Style_Checkbox>true</Style_Checkbox>
		<Text>Shouts</Text>
		<TextColor>
				<R>255</R>
				<G>255</G>
				<B>255</B>
		</TextColor>
		<ButtonDrawTemplate>
			<Normal>A_BtnNormal</Normal>
			<Pressed>A_BtnPressed</Pressed>
			<Flyby>A_BtnFlyby</Flyby>
			<Disabled>A_BtnDisabled</Disabled>
			<PressedFlyby>A_BtnPressedFlyby</PressedFlyby>
		</ButtonDrawTemplate>
	</Button>
	<Button item="OFP_Auctions">
		<ScreenID>Chat4</ScreenID>
		<RelativePosition>true</RelativePosition>
		<Location>
			<X>20</X>
			<Y>180</Y>
		</Location>
		<Size>
			<CX>300</CX>
			<CY>40</CY>
		</Size>
		<Style_VScroll>false</Style_VScroll>
		<Style_HScroll>false</Style_HScroll>
		<Style_Transparent>false</Style_Transparent>
		<Style_Checkbox>true</Style_Checkbox>
		<Text>Auctions</Text>
		<TextColor>
				<R>255</R>
				<G>255</G>
				<B>255</B>
		</TextColor>
		<ButtonDrawTemplate>
			<Normal>A_BtnNormal</Normal>
			<Pressed>A_BtnPressed</Pressed>
			<Flyby>A_BtnFlyby</Flyby>
			<Disabled>A_BtnDisabled</Disabled>
			<PressedFlyby>A_BtnPressedFlyby</PressedFlyby>
		</ButtonDrawTemplate>
	</Button>
	<Button item="OFP_OOC">
		<ScreenID>Chat5</ScreenID>
		<RelativePosition>true</RelativePosition>
		<Location>
			<X>20</X>
			<Y>220</Y>
		</Location>
		<Size>
			<CX>300</CX>
			<CY>40</CY>
		</Size>
		<Style_VScroll>false</Style_VScroll>
		<Style_HScroll>false</Style_HScroll>
		<Style_Transparent>false</Style_Transparent>
		<Style_Checkbox>true</Style_Checkbox>
		<Text>Out of Character</Text>
		<TextColor>
				<R>255</R>
				<G>255</G>
				<B>255</B>
		</TextColor>
		<ButtonDrawTemplate>
			<Normal>A_BtnNormal</Normal>
			<Pressed>A_BtnPressed</Pressed>
			<Flyby>A_BtnFlyby</Flyby>
			<Disabled>A_BtnDisabled</Disabled>
			<PressedFlyby>A_BtnPressedFlyby</PressedFlyby>
		</ButtonDrawTemplate>
	</Button>
	<Button item="OFP_MyMisses">
		<ScreenID>Chat6</ScreenID>
		<RelativePosition>true</RelativePosition>
		<Location>
			<X>20</X>
			<Y>260</Y>
		</Location>
		<Size>
			<CX>300</CX>
			<CY>40</CY>
		</Size>
		<Style_VScroll>false</Style_VScroll>
		<Style_HScroll>false</Style_HScroll>
		<Style_Transparent>false</Style_Transparent>
		<Style_Checkbox>true</Style_Checkbox>
		<Text>My Misses</Text>
		<TextColor>
				<R>255</R>
				<G>255</G>
				<B>255</B>
		</TextColor>
		<ButtonDrawTemplate>
			<Normal>A_BtnNormal</Normal>
			<Pressed>A_BtnPressed</Pressed>
			<Flyby>A_BtnFlyby</Flyby>
			<Disabled>A_BtnDisabled</Disabled>
			<PressedFlyby>A_BtnPressedFlyby</PressedFlyby>
		</ButtonDrawTemplate>
	</Button>
	<Button item="OFP_OthersMisses">
		<ScreenID>Chat7</ScreenID>
		<RelativePosition>true</RelativePosition>
		<Location>
			<X>20</X>
			<Y>300</Y>
		</Location>
		<Size>
			<CX>300</CX>
			<CY>40</CY>
		</Size>
		<Style_VScroll>false</Style_VScroll>
		<Style_HScroll>false</Style_HScroll>
		<Style_Transparent>false</Style_Transparent>
		<Style_Checkbox>true</Style_Checkbox>
		<Text>Others Misses</Text>
		<TextColor>
				<R>255</R>
				<G>255</G>
				<B>255</B>
		</TextColor>
		<ButtonDrawTemplate>
			<Normal>A_BtnNormal</Normal>
			<Pressed>A_BtnPressed</Pressed>
			<Flyby>A_BtnFlyby</Flyby>
			<Disabled>A_BtnDisabled</Disabled>
			<PressedFlyby>A_BtnPressedFlyby</PressedFlyby>
		</ButtonDrawTemplate>
	</Button>
	<Button item="OFP_OthersHits">
		<ScreenID>Chat8</ScreenID>
		<RelativePosition>true</RelativePosition>
		<Location>
			<X>20</X>
			<Y>340</Y>
		</Location>
		<Size>
			<CX>300</CX>
			<CY>40</CY>
		</Size>
		<Style_VScroll>false</Style_VScroll>
		<Style_HScroll>false</Style_HScroll>
		<Style_Transparent>false</Style_Transparent>
		<Style_Checkbox>true</Style_Checkbox>
		<Text>Others Hits</Text>
		<TextColor>
				<R>255</R>
				<G>255</G>
				<B>255</B>
		</TextColor>
		<ButtonDrawTemplate>
			<Normal>A_BtnNormal</Normal>
			<Pressed>A_BtnPressed</Pressed>
			<Flyby>A_BtnFlyby</Flyby>
			<Disabled>A_BtnDisabled</Disabled>
			<PressedFlyby>A_BtnPressedFlyby</PressedFlyby>
		</ButtonDrawTemplate>
	</Button>
	<Button item="OFP_ATKRMissingMe">
		<ScreenID>Chat9</ScreenID>
		<RelativePosition>true</RelativePosition>
		<Location>
			<X>20</X>
			<Y>380</Y>
		</Location>
		<Size>
			<CX>300</CX>
			<CY>40</CY>
		</Size>
		<Style_VScroll>false</Style_VScroll>
		<Style_HScroll>false</Style_HScroll>
		<Style_Transparent>false</Style_Transparent>
		<Style_Checkbox>true</Style_Checkbox>
		<Text>Attacker Missing Me</Text>
		<TextColor>
				<R>255</R>
				<G>255</G>
				<B>255</B>
		</TextColor>
		<ButtonDrawTemplate>
			<Normal>A_BtnNormal</Normal>
			<Pressed>A_BtnPressed</Pressed>
			<Flyby>A_BtnFlyby</Flyby>
			<Disabled>A_BtnDisabled</Disabled>
			<PressedFlyby>A_BtnPressedFlyby</PressedFlyby>
		</ButtonDrawTemplate>
	</Button>
	<Button item="OFP_DamageShields">
		<ScreenID>DamageShields</ScreenID>
		<RelativePosition>true</RelativePosition>
		<Location>
			<X>20</X>
			<Y>420</Y>
		</Location>
		<Size>
			<CX>300</CX>
			<CY>40</CY>
		</Size>
		<Style_VScroll>false</Style_VScroll>
		<Style_HScroll>false</Style_HScroll>
		<Style_Transparent>false</Style_Transparent>
		<Style_Checkbox>true</Style_Checkbox>
		<Text>Damage Shields</Text>
		<TextColor>
				<R>255</R>
				<G>255</G>
				<B>255</B>
		</TextColor>
		<ButtonDrawTemplate>
			<Normal>A_BtnNormal</Normal>
			<Pressed>A_BtnPressed</Pressed>
			<Flyby>A_BtnFlyby</Flyby>
			<Disabled>A_BtnDisabled</Disabled>
			<PressedFlyby>A_BtnPressedFlyby</PressedFlyby>
		</ButtonDrawTemplate>
	</Button>
	<Button item="OFP_NPCSpells">
		<ScreenID>NPCSpells</ScreenID>
		<RelativePosition>true</RelativePosition>
		<Location>
			<X>20</X>
			<Y>460</Y>
		</Location>
		<Size>
			<CX>300</CX>
			<CY>40</CY>
		</Size>
		<Style_VScroll>false</Style_VScroll>
		<Style_HScroll>false</Style_HScroll>
		<Style_Transparent>false</Style_Transparent>
		<Style_Checkbox>true</Style_Checkbox>
		<Text>NPC Spells</Text>
		<TextColor>
				<R>255</R>
				<G>255</G>
				<B>255</B>
		</TextColor>
		<ButtonDrawTemplate>
			<Normal>A_BtnNormal</Normal>
			<Pressed>A_BtnPressed</Pressed>
			<Flyby>A_BtnFlyby</Flyby>
			<Disabled>A_BtnDisabled</Disabled>
			<PressedFlyby>A_BtnPressedFlyby</PressedFlyby>
		</ButtonDrawTemplate>
	</Button>

	
	<Label item="OFP_PCSpellsLabel">
		<ScreenID>PCSpellsLabel</ScreenID>
		<RelativePosition>true</RelativePosition>
		<Location>
			<X>380</X>
			<Y>20</Y>
		</Location>
		<Size>
			<CX>300</CX>
			<CY>28</CY>
		</Size>
		<Text>PC Spells</Text>
		<TextColor>
				<R>255</R>
				<G>255</G>
				<B>255</B>
		</TextColor>
		<NoWrap>true</NoWrap>
		<AlignCenter>false</AlignCenter>
		<AlignRight>false</AlignRight>
	</Label>

	<Combobox item="OFP_PCSpellsComboBox">
		<ScreenID>PCSpellsComboBox</ScreenID>
		<DrawTemplate>WDT_Inner</DrawTemplate>
		<Location>
			<X>380</X>
			<Y>60</Y>
		</Location>
		<Size>
			<CX>300</CX>
			<CY>48</CY>
		</Size>
		<ListHeight>60</ListHeight>
		<Button>BDT_Combo</Button>
		<Style_Border>true</Style_Border>
		<Choices>All</Choices>
		<Choices>Off</Choices>
		<Choices>Group</Choices>
	</Combobox>

	
	<Label item="OFP_BardSongsLabel">
		<ScreenID>BardSongsLabel</ScreenID>
		<RelativePosition>true</RelativePosition>
		<Location>
			<X>380</X>
			<Y>120</Y>
		</Location>
		<Size>
			<CX>300</CX>
			<CY>28</CY>
		</Size>
		<Text>Bard Songs</Text>
		<TextColor>
				<R>255</R>
				<G>255</G>
				<B>255</B>
		</TextColor>
		<NoWrap>true</NoWrap>
		<AlignCenter>false</AlignCenter>
		<AlignRight>false</AlignRight>
	</Label>

	<Combobox item="OFP_BardSongsComboBox">
		<ScreenID>BardSongsComboBox</ScreenID>
		<DrawTemplate>WDT_Inner</DrawTemplate>
		<Location>
			<X>380</X>
			<Y>160</Y>
		</Location>
		<Size>
			<CX>300</CX>
			<CY>48</CY>
		</Size>
		<ListHeight>60</ListHeight>
		<Button>BDT_Combo</Button>
		<Style_Border>true</Style_Border>
		<Choices>All</Choices>
		<Choices>Me</Choices>
		<Choices>Group</Choices>
		<Choices>Off</Choices>
	</Combobox>
	
	<Label item="OFP_CriticalSpellsLabel">
		<ScreenID>CriticalSpellsLabel</ScreenID>
		<RelativePosition>true</RelativePosition>
		<Location>
			<X>380</X>
			<Y>220</Y>
		</Location>
		<Size>
			<CX>300</CX>
			<CY>28</CY>
		</Size>
		<Text>Critical Spells</Text>
		<TextColor>
				<R>255</R>
				<G>255</G>
				<B>255</B>
		</TextColor>
		<NoWrap>true</NoWrap>
		<AlignCenter>false</AlignCenter>
		<AlignRight>false</AlignRight>
	</Label>

	<Combobox item="OFP_CriticalSpellsComboBox">
		<ScreenID>CriticalSpellsComboBox</ScreenID>
		<DrawTemplate>WDT_Inner</DrawTemplate>
		<Location>
			<X>380</X>
			<Y>260</Y>
		</Location>
		<Size>
			<CX>300</CX>
			<CY>48</CY>
		</Size>
		<ListHeight>60</ListHeight>
		<Button>BDT_Combo</Button>
		<Style_Border>true</Style_Border>
		<Choices>All</Choices>
		<Choices>Me</Choices>
		<Choices>Off</Choices>
	</Combobox>
	
	<Label item="OFP_CriticalMeleeLabel">
		<ScreenID>CriticalMeleeLabel</ScreenID>
		<RelativePosition>true</RelativePosition>
		<Location>
			<X>380</X>
			<Y>320</Y>
		</Location>
		<Size>
			<CX>300</CX>
			<CY>28</CY>
		</Size>
		<Text>Critical Melee</Text>
		<TextColor>
				<R>255</R>
				<G>255</G>
				<B>255</B>
		</TextColor>
		<NoWrap>true</NoWrap>
		<AlignCenter>false</AlignCenter>
		<AlignRight>false</AlignRight>
	</Label>

	<Combobox item="OFP_CriticalMeleeComboBox">
		<ScreenID>CriticalMeleeComboBox</ScreenID>
		<DrawTemplate>WDT_Inner</DrawTemplate>
		<Location>
			<X>380</X>
			<Y>360</Y>
		</Location>
		<Size>
			<CX>300</CX>
			<CY>48</CY>
		</Size>
		<ListHeight>60</ListHeight>
		<Button>BDT_Combo</Button>
		<Style_Border>true</Style_Border>
		<Choices>All</Choices>
		<Choices>Me</Choices>
		<Choices>Off</Choices>
	</Combobox>

	<Page item="OptionsChatPage">
		<ScreenID>OptionsChatPage</ScreenID>
		<RelativePosition>true</RelativePosition>

		

		<Style_VScroll>false</Style_VScroll>
		<Style_HScroll>false</Style_HScroll>
		<Style_Transparent>false</Style_Transparent>
		<DrawTemplate>WDT_Def</DrawTemplate>
		<TabText>Filters</TabText>
		<TabTextColor>
				<R>255</R>
				<G>255</G>
				<B>255</B>
		</TabTextColor>
		<TabTextActiveColor>
				<R>255</R>
				<G>255</G>
				<B>0</B>
		</TabTextActiveColor>
		<Style_Sizable>true</Style_Sizable>
			<Pieces>OFP_GuildChat</Pieces>
			<Pieces>OFP_Socials</Pieces>
			<Pieces>OFP_GroupChat</Pieces>
			<Pieces>OFP_Shouts</Pieces>
			<Pieces>OFP_Auctions</Pieces>
			<Pieces>OFP_OOC</Pieces>
			<Pieces>OFP_MyMisses</Pieces>
			<Pieces>OFP_OthersMisses</Pieces>
			<Pieces>OFP_OthersHits</Pieces>
			<Pieces>OFP_ATKRMissingMe</Pieces>
			<Pieces>OFP_DamageShields</Pieces>
			<Pieces>OFP_NPCSpells</Pieces>
			<Pieces>OFP_PCSpellsLabel</Pieces>
			<Pieces>OFP_BardSongsLabel</Pieces>
			<Pieces>OFP_CriticalSpellsLabel</Pieces>
			<Pieces>OFP_CriticalMeleeLabel</Pieces>
			<Pieces>OFP_CriticalMeleeComboBox</Pieces>
			<Pieces>OFP_CriticalSpellsComboBox</Pieces>
			<Pieces>OFP_BardSongsComboBox</Pieces>
			<Pieces>OFP_PCSpellsComboBox</Pieces>
	</Page>



	<Button item="OCP_UserColorDefault">
		<ScreenID>UserColorDefault</ScreenID>
		<RelativePosition>true</RelativePosition>
		<Location>
			<X>210</X>
			<Y>20</Y>
		</Location>
		<Size>
			<CX>300</CX>
			<CY>40</CY>
		</Size>
		<Style_VScroll>false</Style_VScroll>
		<Style_HScroll>false</Style_HScroll>
		<Style_Transparent>false</Style_Transparent>
		<Style_Checkbox>false</Style_Checkbox>
		<Text>Reset Default Colors</Text>
		<TextColor>
				<R>255</R>
				<G>255</G>
				<B>255</B>
		</TextColor>
		<ButtonDrawTemplate>
			<Normal>A_BtnNormal</Normal>
			<Pressed>A_BtnPressed</Pressed>
			<Flyby>A_BtnFlyby</Flyby>
			<Disabled>A_BtnDisabled</Disabled>
			<PressedFlyby>A_BtnPressedFlyby</PressedFlyby>
		</ButtonDrawTemplate>
	</Button>
	<Button item="OCP_UserColor0">
		<ScreenID>UserColor0</ScreenID>
		<RelativePosition>true</RelativePosition>
		<Location>
			<X>20</X>
			<Y>80</Y>
		</Location>
		<Size>
			<CX>300</CX>
			<CY>40</CY>
		</Size>
		<Style_VScroll>false</Style_VScroll>
		<Style_HScroll>false</Style_HScroll>
		<Style_Transparent>false</Style_Transparent>
		<Style_Checkbox>false</Style_Checkbox>
		<Text>1 - Say</Text>
		<TextColor>
				<R>255</R>
				<G>255</G>
				<B>255</B>
		</TextColor>
		<ButtonDrawTemplate>
			<Normal>A_BtnNormal</Normal>
			<Pressed>A_BtnPressed</Pressed>
			<Flyby>A_BtnFlyby</Flyby>
			<Disabled>A_BtnDisabled</Disabled>
			<PressedFlyby>A_BtnPressedFlyby</PressedFlyby>
		</ButtonDrawTemplate>
	</Button>
	<Button item="OCP_UserColor1">
		<ScreenID>UserColor1</ScreenID>
		<RelativePosition>true</RelativePosition>
		<Location>
			<X>380</X>
			<Y>80</Y>
		</Location>
		<Size>
			<CX>300</CX>
			<CY>40</CY>
		</Size>
		<Style_VScroll>false</Style_VScroll>
		<Style_HScroll>false</Style_HScroll>
		<Style_Transparent>false</Style_Transparent>
		<Style_Checkbox>false</Style_Checkbox>
		<Text>2 - Tell</Text>
		<TextColor>
				<R>255</R>
				<G>255</G>
				<B>255</B>
		</TextColor>
		<ButtonDrawTemplate>
			<Normal>A_BtnNormal</Normal>
			<Pressed>A_BtnPressed</Pressed>
			<Flyby>A_BtnFlyby</Flyby>
			<Disabled>A_BtnDisabled</Disabled>
			<PressedFlyby>A_BtnPressedFlyby</PressedFlyby>
		</ButtonDrawTemplate>
	</Button>
	<Button item="OCP_UserColor2">
		<ScreenID>UserColor2</ScreenID>
		<RelativePosition>true</RelativePosition>
		<Location>
			<X>20</X>
			<Y>120</Y>
		</Location>
		<Size>
			<CX>300</CX>
			<CY>40</CY>
		</Size>
		<Style_VScroll>false</Style_VScroll>
		<Style_HScroll>false</Style_HScroll>
		<Style_Transparent>false</Style_Transparent>
		<Style_Checkbox>false</Style_Checkbox>
		<Text>3 - Group</Text>
		<TextColor>
				<R>255</R>
				<G>255</G>
				<B>255</B>
		</TextColor>
		<ButtonDrawTemplate>
			<Normal>A_BtnNormal</Normal>
			<Pressed>A_BtnPressed</Pressed>
			<Flyby>A_BtnFlyby</Flyby>
			<Disabled>A_BtnDisabled</Disabled>
			<PressedFlyby>A_BtnPressedFlyby</PressedFlyby>
		</ButtonDrawTemplate>
	</Button>
	<Button item="OCP_UserColor3">
		<ScreenID>UserColor3</ScreenID>
		<RelativePosition>true</RelativePosition>
		<Location>
			<X>380</X>
			<Y>120</Y>
		</Location>
		<Size>
			<CX>300</CX>
			<CY>40</CY>
		</Size>
		<Style_VScroll>false</Style_VScroll>
		<Style_HScroll>false</Style_HScroll>
		<Style_Transparent>false</Style_Transparent>
		<Style_Checkbox>false</Style_Checkbox>
		<Text>4 - Guild</Text>
		<TextColor>
				<R>255</R>
				<G>255</G>
				<B>255</B>
		</TextColor>
		<ButtonDrawTemplate>
			<Normal>A_BtnNormal</Normal>
			<Pressed>A_BtnPressed</Pressed>
			<Flyby>A_BtnFlyby</Flyby>
			<Disabled>A_BtnDisabled</Disabled>
			<PressedFlyby>A_BtnPressedFlyby</PressedFlyby>
		</ButtonDrawTemplate>
	</Button>
	<Button item="OCP_UserColor4">
		<ScreenID>UserColor4</ScreenID>
		<RelativePosition>true</RelativePosition>
		<Location>
			<X>20</X>
			<Y>160</Y>
		</Location>
		<Size>
			<CX>300</CX>
			<CY>40</CY>
		</Size>
		<Style_VScroll>false</Style_VScroll>
		<Style_HScroll>false</Style_HScroll>
		<Style_Transparent>false</Style_Transparent>
		<Style_Checkbox>false</Style_Checkbox>
		<Text>5 - OOC</Text>
		<TextColor>
				<R>255</R>
				<G>255</G>
				<B>255</B>
		</TextColor>
		<ButtonDrawTemplate>
			<Normal>A_BtnNormal</Normal>
			<Pressed>A_BtnPressed</Pressed>
			<Flyby>A_BtnFlyby</Flyby>
			<Disabled>A_BtnDisabled</Disabled>
			<PressedFlyby>A_BtnPressedFlyby</PressedFlyby>
		</ButtonDrawTemplate>
	</Button>
	<Button item="OCP_UserColor5">
		<ScreenID>UserColor5</ScreenID>
		<Text>6 - Auction</Text>
		<RelativePosition>true</RelativePosition>
		<Location>
			<X>380</X>
			<Y>160</Y>
		</Location>
		<Size>
			<CX>300</CX>
			<CY>40</CY>
		</Size>
		<Style_VScroll>false</Style_VScroll>
		<Style_HScroll>false</Style_HScroll>
		<Style_Transparent>false</Style_Transparent>
		<Style_Checkbox>false</Style_Checkbox>
		<Text>6 - Auction</Text>
		<TextColor>
				<R>255</R>
				<G>255</G>
				<B>255</B>
		</TextColor>
		<ButtonDrawTemplate>
			<Normal>A_BtnNormal</Normal>
			<Pressed>A_BtnPressed</Pressed>
			<Flyby>A_BtnFlyby</Flyby>
			<Disabled>A_BtnDisabled</Disabled>
			<PressedFlyby>A_BtnPressedFlyby</PressedFlyby>
		</ButtonDrawTemplate>
	</Button>
	<Button item="OCP_UserColor6">
		<ScreenID>UserColor6</ScreenID>
		<RelativePosition>true</RelativePosition>
		<Location>
			<X>20</X>
			<Y>200</Y>
		</Location>
		<Size>
			<CX>300</CX>
			<CY>40</CY>
		</Size>
		<Style_VScroll>false</Style_VScroll>
		<Style_HScroll>false</Style_HScroll>
		<Style_Transparent>false</Style_Transparent>
		<Style_Checkbox>false</Style_Checkbox>
		<Text>7 - Shout</Text>
		<TextColor>
				<R>255</R>
				<G>255</G>
				<B>255</B>
		</TextColor>
		<ButtonDrawTemplate>
			<Normal>A_BtnNormal</Normal>
			<Pressed>A_BtnPressed</Pressed>
			<Flyby>A_BtnFlyby</Flyby>
			<Disabled>A_BtnDisabled</Disabled>
			<PressedFlyby>A_BtnPressedFlyby</PressedFlyby>
		</ButtonDrawTemplate>
	</Button>
	<Button item="OCP_UserColor7">
		<ScreenID>UserColor7</ScreenID>
		<RelativePosition>true</RelativePosition>
		<Location>
			<X>380</X>
			<Y>200</Y>
		</Location>
		<Size>
			<CX>300</CX>
			<CY>40</CY>
		</Size>
		<Style_VScroll>false</Style_VScroll>
		<Style_HScroll>false</Style_HScroll>
		<Style_Transparent>false</Style_Transparent>
		<Style_Checkbox>false</Style_Checkbox>
		<Text>8 - Emote</Text>
		<TextColor>
				<R>255</R>
				<G>255</G>
				<B>255</B>
		</TextColor>
		<ButtonDrawTemplate>
			<Normal>A_BtnNormal</Normal>
			<Pressed>A_BtnPressed</Pressed>
			<Flyby>A_BtnFlyby</Flyby>
			<Disabled>A_BtnDisabled</Disabled>
			<PressedFlyby>A_BtnPressedFlyby</PressedFlyby>
		</ButtonDrawTemplate>
	</Button>
	<Button item="OCP_UserColor8">
		<ScreenID>UserColor8</ScreenID>
		<RelativePosition>true</RelativePosition>
		<Location>
			<X>20</X>
			<Y>240</Y>
		</Location>
		<Size>
			<CX>300</CX>
			<CY>40</CY>
		</Size>
		<Style_VScroll>false</Style_VScroll>
		<Style_HScroll>false</Style_HScroll>
		<Style_Transparent>false</Style_Transparent>
		<Style_Checkbox>false</Style_Checkbox>
		<Text>9 - Spells</Text>
		<TextColor>
				<R>255</R>
				<G>255</G>
				<B>255</B>
		</TextColor>
		<ButtonDrawTemplate>
			<Normal>A_BtnNormal</Normal>
			<Pressed>A_BtnPressed</Pressed>
			<Flyby>A_BtnFlyby</Flyby>
			<Disabled>A_BtnDisabled</Disabled>
			<PressedFlyby>A_BtnPressedFlyby</PressedFlyby>
		</ButtonDrawTemplate>
	</Button>
	<Button item="OCP_UserColor9">
		<ScreenID>UserColor9</ScreenID>
		<RelativePosition>true</RelativePosition>
		<Location>
			<X>380</X>
			<Y>240</Y>
		</Location>
		<Size>
			<CX>300</CX>
			<CY>40</CY>
		</Size>
		<Style_VScroll>false</Style_VScroll>
		<Style_HScroll>false</Style_HScroll>
		<Style_Transparent>false</Style_Transparent>
		<Style_Checkbox>false</Style_Checkbox>
		<Text>10 - You hit other</Text>
		<TextColor>
				<R>255</R>
				<G>255</G>
				<B>255</B>
		</TextColor>
		<ButtonDrawTemplate>
			<Normal>A_BtnNormal</Normal>
			<Pressed>A_BtnPressed</Pressed>
			<Flyby>A_BtnFlyby</Flyby>
			<Disabled>A_BtnDisabled</Disabled>
			<PressedFlyby>A_BtnPressedFlyby</PressedFlyby>
		</ButtonDrawTemplate>
	</Button>
	<Button item="OCP_UserColor10">
		<ScreenID>UserColor10</ScreenID>
		<RelativePosition>true</RelativePosition>
		<Location>
			<X>20</X>
			<Y>280</Y>
		</Location>
		<Size>
			<CX>300</CX>
			<CY>40</CY>
		</Size>
		<Style_VScroll>false</Style_VScroll>
		<Style_HScroll>false</Style_HScroll>
		<Style_Transparent>false</Style_Transparent>
		<Style_Checkbox>false</Style_Checkbox>
		<Text>11 - Other hits you</Text>
		<TextColor>
				<R>255</R>
				<G>255</G>
				<B>255</B>
		</TextColor>
		<ButtonDrawTemplate>
			<Normal>A_BtnNormal</Normal>
			<Pressed>A_BtnPressed</Pressed>
			<Flyby>A_BtnFlyby</Flyby>
			<Disabled>A_BtnDisabled</Disabled>
			<PressedFlyby>A_BtnPressedFlyby</PressedFlyby>
		</ButtonDrawTemplate>
	</Button>
	<Button item="OCP_UserColor11">
		<ScreenID>UserColor11</ScreenID>
		<RelativePosition>true</RelativePosition>
		<Location>
			<X>380</X>
			<Y>280</Y>
		</Location>
		<Size>
			<CX>300</CX>
			<CY>40</CY>
		</Size>
		<Style_VScroll>false</Style_VScroll>
		<Style_HScroll>false</Style_HScroll>
		<Style_Transparent>false</Style_Transparent>
		<Style_Checkbox>false</Style_Checkbox>
		<Text>12 - You miss other</Text>
		<TextColor>
				<R>255</R>
				<G>255</G>
				<B>255</B>
		</TextColor>
		<ButtonDrawTemplate>
			<Normal>A_BtnNormal</Normal>
			<Pressed>A_BtnPressed</Pressed>
			<Flyby>A_BtnFlyby</Flyby>
			<Disabled>A_BtnDisabled</Disabled>
			<PressedFlyby>A_BtnPressedFlyby</PressedFlyby>
		</ButtonDrawTemplate>
	</Button>
	<Button item="OCP_UserColor12">
		<ScreenID>UserColor12</ScreenID>
		<RelativePosition>true</RelativePosition>
		<Location>
			<X>20</X>
			<Y>320</Y>
		</Location>
		<Size>
			<CX>300</CX>
			<CY>40</CY>
		</Size>
		<Style_VScroll>false</Style_VScroll>
		<Style_HScroll>false</Style_HScroll>
		<Style_Transparent>false</Style_Transparent>
		<Style_Checkbox>false</Style_Checkbox>
		<Text>13 - Other misses you</Text>
		<TextColor>
				<R>255</R>
				<G>255</G>
				<B>255</B>
		</TextColor>
		<ButtonDrawTemplate>
			<Normal>A_BtnNormal</Normal>
			<Pressed>A_BtnPressed</Pressed>
			<Flyby>A_BtnFlyby</Flyby>
			<Disabled>A_BtnDisabled</Disabled>
			<PressedFlyby>A_BtnPressedFlyby</PressedFlyby>
		</ButtonDrawTemplate>
	</Button>
	<Button item="OCP_UserColor13">
		<ScreenID>UserColor13</ScreenID>
		<RelativePosition>true</RelativePosition>
		<Location>
			<X>380</X>
			<Y>320</Y>
		</Location>
		<Size>
			<CX>300</CX>
			<CY>40</CY>
		</Size>
		<Style_VScroll>false</Style_VScroll>
		<Style_HScroll>false</Style_HScroll>
		<Style_Transparent>false</Style_Transparent>
		<Style_Checkbox>false</Style_Checkbox>
		<Text>14 - Some broadcasts</Text>
		<TextColor>
				<R>255</R>
				<G>255</G>
				<B>255</B>
		</TextColor>
		<ButtonDrawTemplate>
			<Normal>A_BtnNormal</Normal>
			<Pressed>A_BtnPressed</Pressed>
			<Flyby>A_BtnFlyby</Flyby>
			<Disabled>A_BtnDisabled</Disabled>
			<PressedFlyby>A_BtnPressedFlyby</PressedFlyby>
		</ButtonDrawTemplate>
	</Button>
	<Button item="OCP_UserColor14">
		<ScreenID>UserColor14</ScreenID>
		<RelativePosition>true</RelativePosition>
		<Location>
			<X>20</X>
			<Y>360</Y>
		</Location>
		<Size>
			<CX>300</CX>
			<CY>40</CY>
		</Size>
		<Style_VScroll>false</Style_VScroll>
		<Style_HScroll>false</Style_HScroll>
		<Style_Transparent>false</Style_Transparent>
		<Style_Checkbox>false</Style_Checkbox>
		<Text>15 - Skills</Text>
		<TextColor>
				<R>255</R>
				<G>255</G>
				<B>255</B>
		</TextColor>
		<ButtonDrawTemplate>
			<Normal>A_BtnNormal</Normal>
			<Pressed>A_BtnPressed</Pressed>
			<Flyby>A_BtnFlyby</Flyby>
			<Disabled>A_BtnDisabled</Disabled>
			<PressedFlyby>A_BtnPressedFlyby</PressedFlyby>
		</ButtonDrawTemplate>
	</Button>
	<Button item="OCP_UserColor15">
		<ScreenID>UserColor15</ScreenID>
		<RelativePosition>true</RelativePosition>
		<Location>
			<X>380</X>
			<Y>360</Y>
		</Location>
		<Size>
			<CX>300</CX>
			<CY>40</CY>
		</Size>
		<Style_VScroll>false</Style_VScroll>
		<Style_HScroll>false</Style_HScroll>
		<Style_Transparent>false</Style_Transparent>
		<Style_Checkbox>false</Style_Checkbox>
		<Text>16 - Special Abilities</Text>
		<TextColor>
				<R>255</R>
				<G>255</G>
				<B>255</B>
		</TextColor>
		<ButtonDrawTemplate>
			<Normal>A_BtnNormal</Normal>
			<Pressed>A_BtnPressed</Pressed>
			<Flyby>A_BtnFlyby</Flyby>
			<Disabled>A_BtnDisabled</Disabled>
			<PressedFlyby>A_BtnPressedFlyby</PressedFlyby>
		</ButtonDrawTemplate>
	</Button>
	<Button item="OCP_UserColor16">
		<ScreenID>UserColor16</ScreenID>
		<RelativePosition>true</RelativePosition>
		<Location>
			<X>20</X>
			<Y>400</Y>
		</Location>
		<Size>
			<CX>300</CX>
			<CY>40</CY>
		</Size>
		<Style_VScroll>false</Style_VScroll>
		<Style_HScroll>false</Style_HScroll>
		<Style_Transparent>false</Style_Transparent>
		<Style_Checkbox>false</Style_Checkbox>
		<Text>17 - Unused at this time</Text>
		<TextColor>
				<R>255</R>
				<G>255</G>
				<B>255</B>
		</TextColor>
		<ButtonDrawTemplate>
			<Normal>A_BtnNormal</Normal>
			<Pressed>A_BtnPressed</Pressed>
			<Flyby>A_BtnFlyby</Flyby>
			<Disabled>A_BtnDisabled</Disabled>
			<PressedFlyby>A_BtnPressedFlyby</PressedFlyby>
		</ButtonDrawTemplate>
	</Button>
	<Button item="OCP_UserColor17">
		<ScreenID>UserColor17</ScreenID>
		<RelativePosition>true</RelativePosition>
		<Location>
			<X>380</X>
			<Y>400</Y>
		</Location>
		<Size>
			<CX>300</CX>
			<CY>40</CY>
		</Size>
		<Style_VScroll>false</Style_VScroll>
		<Style_HScroll>false</Style_HScroll>
		<Style_Transparent>false</Style_Transparent>
		<Style_Checkbox>false</Style_Checkbox>
		<Text>18 - Default Text</Text>
		<TextColor>
				<R>255</R>
				<G>255</G>
				<B>255</B>
		</TextColor>
		<ButtonDrawTemplate>
			<Normal>A_BtnNormal</Normal>
			<Pressed>A_BtnPressed</Pressed>
			<Flyby>A_BtnFlyby</Flyby>
			<Disabled>A_BtnDisabled</Disabled>
			<PressedFlyby>A_BtnPressedFlyby</PressedFlyby>
		</ButtonDrawTemplate>
	</Button>
	<Button item="OCP_UserColor18">
		<ScreenID>UserColor18</ScreenID>
		<RelativePosition>true</RelativePosition>
		<Location>
			<X>20</X>
			<Y>440</Y>
		</Location>
		<Size>
			<CX>300</CX>
			<CY>40</CY>
		</Size>
		<Style_VScroll>false</Style_VScroll>
		<Style_HScroll>false</Style_HScroll>
		<Style_Transparent>false</Style_Transparent>
		<Style_Checkbox>false</Style_Checkbox>
		<Text>19 - Unused at this time</Text>
		<TextColor>
				<R>255</R>
				<G>255</G>
				<B>255</B>
		</TextColor>
		<ButtonDrawTemplate>
			<Normal>A_BtnNormal</Normal>
			<Pressed>A_BtnPressed</Pressed>
			<Flyby>A_BtnFlyby</Flyby>
			<Disabled>A_BtnDisabled</Disabled>
			<PressedFlyby>A_BtnPressedFlyby</PressedFlyby>
		</ButtonDrawTemplate>
	</Button>
	<Button item="OCP_UserColor19">
		<ScreenID>UserColor19</ScreenID>
		<RelativePosition>true</RelativePosition>
		<Location>
			<X>380</X>
			<Y>440</Y>
		</Location>
		<Size>
			<CX>300</CX>
			<CY>40</CY>
		</Size>
		<Style_VScroll>false</Style_VScroll>
		<Style_HScroll>false</Style_HScroll>
		<Style_Transparent>false</Style_Transparent>
		<Style_Checkbox>false</Style_Checkbox>
		<Text>20 - Merchant Offer Price</Text>
		<TextColor>
				<R>255</R>
				<G>255</G>
				<B>255</B>
		</TextColor>
		<ButtonDrawTemplate>
			<Normal>A_BtnNormal</Normal>
			<Pressed>A_BtnPressed</Pressed>
			<Flyby>A_BtnFlyby</Flyby>
			<Disabled>A_BtnDisabled</Disabled>
			<PressedFlyby>A_BtnPressedFlyby</PressedFlyby>
		</ButtonDrawTemplate>
	</Button>
	<Button item="OCP_UserColor20">
		<ScreenID>UserColor20</ScreenID>
		<RelativePosition>true</RelativePosition>
		<Location>
			<X>20</X>
			<Y>480</Y>
		</Location>
		<Size>
			<CX>300</CX>
			<CY>40</CY>
		</Size>
		<Style_VScroll>false</Style_VScroll>
		<Style_HScroll>false</Style_HScroll>
		<Style_Transparent>false</Style_Transparent>
		<Style_Checkbox>false</Style_Checkbox>
		<Text>21 - Merchant Buy/Sell</Text>
		<TextColor>
				<R>255</R>
				<G>255</G>
				<B>255</B>
		</TextColor>
		<ButtonDrawTemplate>
			<Normal>A_BtnNormal</Normal>
			<Pressed>A_BtnPressed</Pressed>
			<Flyby>A_BtnFlyby</Flyby>
			<Disabled>A_BtnDisabled</Disabled>
			<PressedFlyby>A_BtnPressedFlyby</PressedFlyby>
		</ButtonDrawTemplate>
	</Button>
	<Button item="OCP_UserColor21">
		<ScreenID>UserColor21</ScreenID>
		<RelativePosition>true</RelativePosition>
		<Location>
			<X>380</X>
			<Y>480</Y>
		</Location>
		<Size>
			<CX>300</CX>
			<CY>40</CY>
		</Size>
		<Style_VScroll>false</Style_VScroll>
		<Style_HScroll>false</Style_HScroll>
		<Style_Transparent>false</Style_Transparent>
		<Style_Checkbox>false</Style_Checkbox>
		<Text>22 - Your death message</Text>
		<TextColor>
				<R>255</R>
				<G>255</G>
				<B>255</B>
		</TextColor>
		<ButtonDrawTemplate>
			<Normal>A_BtnNormal</Normal>
			<Pressed>A_BtnPressed</Pressed>
			<Flyby>A_BtnFlyby</Flyby>
			<Disabled>A_BtnDisabled</Disabled>
			<PressedFlyby>A_BtnPressedFlyby</PressedFlyby>
		</ButtonDrawTemplate>
	</Button>
	<Button item="OCP_UserColor22">
		<ScreenID>UserColor22</ScreenID>
		<RelativePosition>true</RelativePosition>
		<Location>
			<X>20</X>
			<Y>520</Y>
		</Location>
		<Size>
			<CX>300</CX>
			<CY>40</CY>
		</Size>
		<Style_VScroll>false</Style_VScroll>
		<Style_HScroll>false</Style_HScroll>
		<Style_Transparent>false</Style_Transparent>
		<Style_Checkbox>false</Style_Checkbox>
		<Text>23 - Other death message</Text>
		<TextColor>
				<R>255</R>
				<G>255</G>
				<B>255</B>
		</TextColor>
		<ButtonDrawTemplate>
			<Normal>A_BtnNormal</Normal>
			<Pressed>A_BtnPressed</Pressed>
			<Flyby>A_BtnFlyby</Flyby>
			<Disabled>A_BtnDisabled</Disabled>
			<PressedFlyby>A_BtnPressedFlyby</PressedFlyby>
		</ButtonDrawTemplate>
	</Button>
	<Button item="OCP_UserColor23">
		<ScreenID>UserColor23</ScreenID>
		<RelativePosition>true</RelativePosition>
		<Location>
			<X>380</X>
			<Y>520</Y>
		</Location>
		<Size>
			<CX>300</CX>
			<CY>40</CY>
		</Size>
		<Style_VScroll>false</Style_VScroll>
		<Style_HScroll>false</Style_HScroll>
		<Style_Transparent>false</Style_Transparent>
		<Style_Checkbox>false</Style_Checkbox>
		<Text>24 - Other damage other</Text>
		<TextColor>
				<R>255</R>
				<G>255</G>
				<B>255</B>
		</TextColor>
		<ButtonDrawTemplate>
			<Normal>A_BtnNormal</Normal>
			<Pressed>A_BtnPressed</Pressed>
			<Flyby>A_BtnFlyby</Flyby>
			<Disabled>A_BtnDisabled</Disabled>
			<PressedFlyby>A_BtnPressedFlyby</PressedFlyby>
		</ButtonDrawTemplate>
	</Button>
	<Button item="OCP_UserColor24">
		<ScreenID>UserColor24</ScreenID>
		<RelativePosition>true</RelativePosition>
		<Location>
			<X>20</X>
			<Y>560</Y>
		</Location>
		<Size>
			<CX>300</CX>
			<CY>40</CY>
		</Size>
		<Style_VScroll>false</Style_VScroll>
		<Style_HScroll>false</Style_HScroll>
		<Style_Transparent>false</Style_Transparent>
		<Style_Checkbox>false</Style_Checkbox>
		<Text>25 - Other miss other</Text>
		<TextColor>
				<R>255</R>
				<G>255</G>
				<B>255</B>
		</TextColor>
		<ButtonDrawTemplate>
			<Normal>A_BtnNormal</Normal>
			<Pressed>A_BtnPressed</Pressed>
			<Flyby>A_BtnFlyby</Flyby>
			<Disabled>A_BtnDisabled</Disabled>
			<PressedFlyby>A_BtnPressedFlyby</PressedFlyby>
		</ButtonDrawTemplate>
	</Button>
	<Button item="OCP_UserColor25">
		<ScreenID>UserColor25</ScreenID>
		<RelativePosition>true</RelativePosition>
		<Location>
			<X>380</X>
			<Y>560</Y>
		</Location>
		<Size>
			<CX>300</CX>
			<CY>40</CY>
		</Size>
		<Style_VScroll>false</Style_VScroll>
		<Style_HScroll>false</Style_HScroll>
		<Style_Transparent>false</Style_Transparent>
		<Style_Checkbox>false</Style_Checkbox>
		<Text>26 - /who command</Text>
		<TextColor>
				<R>255</R>
				<G>255</G>
				<B>255</B>
		</TextColor>
		<ButtonDrawTemplate>
			<Normal>A_BtnNormal</Normal>
			<Pressed>A_BtnPressed</Pressed>
			<Flyby>A_BtnFlyby</Flyby>
			<Disabled>A_BtnDisabled</Disabled>
			<PressedFlyby>A_BtnPressedFlyby</PressedFlyby>
		</ButtonDrawTemplate>
	</Button>
	<Button item="OCP_UserColor26">
		<ScreenID>UserColor26</ScreenID>
		<RelativePosition>true</RelativePosition>
		<Location>
			<X>20</X>
			<Y>600</Y>
		</Location>
		<Size>
			<CX>300</CX>
			<CY>40</CY>
		</Size>
		<Style_VScroll>false</Style_VScroll>
		<Style_HScroll>false</Style_HScroll>
		<Style_Transparent>false</Style_Transparent>
		<Style_Checkbox>false</Style_Checkbox>
		<Text>27 - yell for help</Text>
		<TextColor>
				<R>255</R>
				<G>255</G>
				<B>255</B>
		</TextColor>
		<ButtonDrawTemplate>
			<Normal>A_BtnNormal</Normal>
			<Pressed>A_BtnPressed</Pressed>
			<Flyby>A_BtnFlyby</Flyby>
			<Disabled>A_BtnDisabled</Disabled>
			<PressedFlyby>A_BtnPressedFlyby</PressedFlyby>
		</ButtonDrawTemplate>
	</Button>
	<Button item="OCP_UserColor27">
		<ScreenID>UserColor27</ScreenID>
		<RelativePosition>true</RelativePosition>
		<Location>
			<X>380</X>
			<Y>600</Y>
		</Location>
		<Size>
			<CX>300</CX>
			<CY>40</CY>
		</Size>
		<Style_VScroll>false</Style_VScroll>
		<Style_HScroll>false</Style_HScroll>
		<Style_Transparent>false</Style_Transparent>
		<Style_Checkbox>false</Style_Checkbox>
		<Text>28 - Hit for non-melee</Text>
		<TextColor>
				<R>255</R>
				<G>255</G>
				<B>255</B>
		</TextColor>
		<ButtonDrawTemplate>
			<Normal>A_BtnNormal</Normal>
			<Pressed>A_BtnPressed</Pressed>
			<Flyby>A_BtnFlyby</Flyby>
			<Disabled>A_BtnDisabled</Disabled>
			<PressedFlyby>A_BtnPressedFlyby</PressedFlyby>
		</ButtonDrawTemplate>
	</Button>
	<Button item="OCP_UserColor28">
		<ScreenID>UserColor28</ScreenID>
		<RelativePosition>true</RelativePosition>
		<Location>
			<X>20</X>
			<Y>640</Y>
		</Location>
		<Size>
			<CX>300</CX>
			<CY>40</CY>
		</Size>
		<Style_VScroll>false</Style_VScroll>
		<Style_HScroll>false</Style_HScroll>
		<Style_Transparent>false</Style_Transparent>
		<Style_Checkbox>false</Style_Checkbox>
		<Text>29 - Spell worn off</Text>
		<TextColor>
				<R>255</R>
				<G>255</G>
				<B>255</B>
		</TextColor>
		<ButtonDrawTemplate>
			<Normal>A_BtnNormal</Normal>
			<Pressed>A_BtnPressed</Pressed>
			<Flyby>A_BtnFlyby</Flyby>
			<Disabled>A_BtnDisabled</Disabled>
			<PressedFlyby>A_BtnPressedFlyby</PressedFlyby>
		</ButtonDrawTemplate>
	</Button>
	<Button item="OCP_UserColor29">
		<ScreenID>UserColor29</ScreenID>
		<RelativePosition>true</RelativePosition>
		<Location>
			<X>380</X>
			<Y>640</Y>
		</Location>
		<Size>
			<CX>300</CX>
			<CY>40</CY>
		</Size>
		<Style_VScroll>false</Style_VScroll>
		<Style_HScroll>false</Style_HScroll>
		<Style_Transparent>false</Style_Transparent>
		<Style_Checkbox>false</Style_Checkbox>
		<Text>30 - Money splits</Text>
		<TextColor>
				<R>255</R>
				<G>255</G>
				<B>255</B>
		</TextColor>
		<ButtonDrawTemplate>
			<Normal>A_BtnNormal</Normal>
			<Pressed>A_BtnPressed</Pressed>
			<Flyby>A_BtnFlyby</Flyby>
			<Disabled>A_BtnDisabled</Disabled>
			<PressedFlyby>A_BtnPressedFlyby</PressedFlyby>
		</ButtonDrawTemplate>
	</Button>
	<Button item="OCP_UserColor30">
		<ScreenID>UserColor30</ScreenID>
		<RelativePosition>true</RelativePosition>
		<Location>
			<X>20</X>
			<Y>680</Y>
		</Location>
		<Size>
			<CX>300</CX>
			<CY>40</CY>
		</Size>
		<Style_VScroll>false</Style_VScroll>
		<Style_HScroll>false</Style_HScroll>
		<Style_Transparent>false</Style_Transparent>
		<Style_Checkbox>false</Style_Checkbox>
		<Text>31 - Loot message</Text>
		<TextColor>
				<R>255</R>
				<G>255</G>
				<B>255</B>
		</TextColor>
		<ButtonDrawTemplate>
			<Normal>A_BtnNormal</Normal>
			<Pressed>A_BtnPressed</Pressed>
			<Flyby>A_BtnFlyby</Flyby>
			<Disabled>A_BtnDisabled</Disabled>
			<PressedFlyby>A_BtnPressedFlyby</PressedFlyby>
		</ButtonDrawTemplate>
	</Button>
	<Button item="OCP_UserColor31">
		<ScreenID>UserColor31</ScreenID>
		<RelativePosition>true</RelativePosition>
		<Location>
			<X>380</X>
			<Y>680</Y>
		</Location>
		<Size>
			<CX>300</CX>
			<CY>40</CY>
		</Size>
		<Style_VScroll>false</Style_VScroll>
		<Style_HScroll>false</Style_HScroll>
		<Style_Transparent>false</Style_Transparent>
		<Style_Checkbox>false</Style_Checkbox>
		<Text>32 - Dice Roll (/random)</Text>
		<TextColor>
				<R>255</R>
				<G>255</G>
				<B>255</B>
		</TextColor>
		<ButtonDrawTemplate>
			<Normal>A_BtnNormal</Normal>
			<Pressed>A_BtnPressed</Pressed>
			<Flyby>A_BtnFlyby</Flyby>
			<Disabled>A_BtnDisabled</Disabled>
			<PressedFlyby>A_BtnPressedFlyby</PressedFlyby>
		</ButtonDrawTemplate>
	</Button>
	<Button item="OCP_UserColor32">
		<ScreenID>UserColor32</ScreenID>
		<RelativePosition>true</RelativePosition>
		<Location>
			<X>20</X>
			<Y>720</Y>
		</Location>
		<Size>
			<CX>300</CX>
			<CY>40</CY>
		</Size>
		<Style_VScroll>false</Style_VScroll>
		<Style_HScroll>false</Style_HScroll>
		<Style_Transparent>false</Style_Transparent>
		<Style_Checkbox>false</Style_Checkbox>
		<Text>33 - Others spells</Text>
		<TextColor>
				<R>255</R>
				<G>255</G>
				<B>255</B>
		</TextColor>
		<ButtonDrawTemplate>
			<Normal>A_BtnNormal</Normal>
			<Pressed>A_BtnPressed</Pressed>
			<Flyby>A_BtnFlyby</Flyby>
			<Disabled>A_BtnDisabled</Disabled>
			<PressedFlyby>A_BtnPressedFlyby</PressedFlyby>
		</ButtonDrawTemplate>
	</Button>
	<Button item="OCP_UserColor33">
		<ScreenID>UserColor33</ScreenID>
		<RelativePosition>true</RelativePosition>
		<Location>
			<X>380</X>
			<Y>720</Y>
		</Location>
		<Size>
			<CX>300</CX>
			<CY>40</CY>
		</Size>
		<Style_VScroll>false</Style_VScroll>
		<Style_HScroll>false</Style_HScroll>
		<Style_Transparent>false</Style_Transparent>
		<Style_Checkbox>false</Style_Checkbox>
		<Text>34 - Spell Failures</Text>
		<TextColor>
				<R>255</R>
				<G>255</G>
				<B>255</B>
		</TextColor>
		<ButtonDrawTemplate>
			<Normal>A_BtnNormal</Normal>
			<Pressed>A_BtnPressed</Pressed>
			<Flyby>A_BtnFlyby</Flyby>
			<Disabled>A_BtnDisabled</Disabled>
			<PressedFlyby>A_BtnPressedFlyby</PressedFlyby>
		</ButtonDrawTemplate>
	</Button>
	<Button item="OCP_UserColor34">
		<ScreenID>UserColor34</ScreenID>
		<RelativePosition>true</RelativePosition>
		<Location>
			<X>20</X>
			<Y>760</Y>
		</Location>
		<Size>
			<CX>300</CX>
			<CY>40</CY>
		</Size>
		<Style_VScroll>false</Style_VScroll>
		<Style_HScroll>false</Style_HScroll>
		<Style_Transparent>false</Style_Transparent>
		<Style_Checkbox>false</Style_Checkbox>
		<Text>35 - Chat Channel</Text>
		<TextColor>
				<R>255</R>
				<G>255</G>
				<B>255</B>
		</TextColor>
		<ButtonDrawTemplate>
			<Normal>A_BtnNormal</Normal>
			<Pressed>A_BtnPressed</Pressed>
			<Flyby>A_BtnFlyby</Flyby>
			<Disabled>A_BtnDisabled</Disabled>
			<PressedFlyby>A_BtnPressedFlyby</PressedFlyby>
		</ButtonDrawTemplate>
	</Button>
	<Button item="OCP_UserColor35">
		<ScreenID>UserColor35</ScreenID>
		<RelativePosition>true</RelativePosition>
		<Location>
			<X>380</X>
			<Y>760</Y>
		</Location>
		<Size>
			<CX>300</CX>
			<CY>40</CY>
		</Size>
		<Style_VScroll>false</Style_VScroll>
		<Style_HScroll>false</Style_HScroll>
		<Style_Transparent>false</Style_Transparent>
		<Style_Checkbox>false</Style_Checkbox>
		<Text>36 - Chat Channel 1</Text>
		<TextColor>
				<R>255</R>
				<G>255</G>
				<B>255</B>
		</TextColor>
		<ButtonDrawTemplate>
			<Normal>A_BtnNormal</Normal>
			<Pressed>A_BtnPressed</Pressed>
			<Flyby>A_BtnFlyby</Flyby>
			<Disabled>A_BtnDisabled</Disabled>
			<PressedFlyby>A_BtnPressedFlyby</PressedFlyby>
		</ButtonDrawTemplate>
	</Button>
	<Button item="OCP_UserColor36">
		<ScreenID>UserColor36</ScreenID>
		<RelativePosition>true</RelativePosition>
		<Location>
			<X>20</X>
			<Y>800</Y>
		</Location>
		<Size>
			<CX>300</CX>
			<CY>40</CY>
		</Size>
		<Style_VScroll>false</Style_VScroll>
		<Style_HScroll>false</Style_HScroll>
		<Style_Transparent>false</Style_Transparent>
		<Style_Checkbox>false</Style_Checkbox>
		<Text>37 - Chat Channel 2</Text>
		<TextColor>
				<R>255</R>
				<G>255</G>
				<B>255</B>
		</TextColor>
		<ButtonDrawTemplate>
			<Normal>A_BtnNormal</Normal>
			<Pressed>A_BtnPressed</Pressed>
			<Flyby>A_BtnFlyby</Flyby>
			<Disabled>A_BtnDisabled</Disabled>
			<PressedFlyby>A_BtnPressedFlyby</PressedFlyby>
		</ButtonDrawTemplate>
	</Button>
	<Button item="OCP_UserColor37">
		<ScreenID>UserColor37</ScreenID>
		<RelativePosition>true</RelativePosition>
		<Location>
			<X>380</X>
			<Y>800</Y>
		</Location>
		<Size>
			<CX>300</CX>
			<CY>40</CY>
		</Size>
		<Style_VScroll>false</Style_VScroll>
		<Style_HScroll>false</Style_HScroll>
		<Style_Transparent>false</Style_Transparent>
		<Style_Checkbox>false</Style_Checkbox>
		<Text>38 - Chat Channel 3</Text>
		<TextColor>
				<R>255</R>
				<G>255</G>
				<B>255</B>
		</TextColor>
		<ButtonDrawTemplate>
			<Normal>A_BtnNormal</Normal>
			<Pressed>A_BtnPressed</Pressed>
			<Flyby>A_BtnFlyby</Flyby>
			<Disabled>A_BtnDisabled</Disabled>
			<PressedFlyby>A_BtnPressedFlyby</PressedFlyby>
		</ButtonDrawTemplate>
	</Button>
	<Button item="OCP_UserColor38">
		<ScreenID>UserColor38</ScreenID>
		<RelativePosition>true</RelativePosition>
		<Location>
			<X>20</X>
			<Y>840</Y>
		</Location>
		<Size>
			<CX>300</CX>
			<CY>40</CY>
		</Size>
		<Style_VScroll>false</Style_VScroll>
		<Style_HScroll>false</Style_HScroll>
		<Style_Transparent>false</Style_Transparent>
		<Style_Checkbox>false</Style_Checkbox>
		<Text>39 - Chat Channel 4</Text>
		<TextColor>
				<R>255</R>
				<G>255</G>
				<B>255</B>
		</TextColor>
		<ButtonDrawTemplate>
			<Normal>A_BtnNormal</Normal>
			<Pressed>A_BtnPressed</Pressed>
			<Flyby>A_BtnFlyby</Flyby>
			<Disabled>A_BtnDisabled</Disabled>
			<PressedFlyby>A_BtnPressedFlyby</PressedFlyby>
		</ButtonDrawTemplate>
	</Button>
	<Button item="OCP_UserColor39">
		<ScreenID>UserColor39</ScreenID>
		<RelativePosition>true</RelativePosition>
		<Location>
			<X>380</X>
			<Y>840</Y>
		</Location>
		<Size>
			<CX>300</CX>
			<CY>40</CY>
		</Size>
		<Style_VScroll>false</Style_VScroll>
		<Style_HScroll>false</Style_HScroll>
		<Style_Transparent>false</Style_Transparent>
		<Style_Checkbox>false</Style_Checkbox>
		<Text>40 - Chat Channel 5</Text>
		<TextColor>
				<R>255</R>
				<G>255</G>
				<B>255</B>
		</TextColor>
		<ButtonDrawTemplate>
			<Normal>A_BtnNormal</Normal>
			<Pressed>A_BtnPressed</Pressed>
			<Flyby>A_BtnFlyby</Flyby>
			<Disabled>A_BtnDisabled</Disabled>
			<PressedFlyby>A_BtnPressedFlyby</PressedFlyby>
		</ButtonDrawTemplate>
	</Button>
	<Button item="OCP_UserColor40">
		<ScreenID>UserColor40</ScreenID>
		<RelativePosition>true</RelativePosition>
		<Location>
			<X>20</X>
			<Y>880</Y>
		</Location>
		<Size>
			<CX>300</CX>
			<CY>40</CY>
		</Size>
		<Style_VScroll>false</Style_VScroll>
		<Style_HScroll>false</Style_HScroll>
		<Style_Transparent>false</Style_Transparent>
		<Style_Checkbox>false</Style_Checkbox>
		<Text>41 - Chat Channel 6</Text>
		<TextColor>
				<R>255</R>
				<G>255</G>
				<B>255</B>
		</TextColor>
		<ButtonDrawTemplate>
			<Normal>A_BtnNormal</Normal>
			<Pressed>A_BtnPressed</Pressed>
			<Flyby>A_BtnFlyby</Flyby>
			<Disabled>A_BtnDisabled</Disabled>
			<PressedFlyby>A_BtnPressedFlyby</PressedFlyby>
		</ButtonDrawTemplate>
	</Button>
	<Button item="OCP_UserColor41">
		<ScreenID>UserColor41</ScreenID>
		<RelativePosition>true</RelativePosition>
		<Location>
			<X>380</X>
			<Y>880</Y>
		</Location>
		<Size>
			<CX>300</CX>
			<CY>40</CY>
		</Size>
		<Style_VScroll>false</Style_VScroll>
		<Style_HScroll>false</Style_HScroll>
		<Style_Transparent>false</Style_Transparent>
		<Style_Checkbox>false</Style_Checkbox>
		<Text>42 - Chat Channel 7</Text>
		<TextColor>
				<R>255</R>
				<G>255</G>
				<B>255</B>
		</TextColor>
		<ButtonDrawTemplate>
			<Normal>A_BtnNormal</Normal>
			<Pressed>A_BtnPressed</Pressed>
			<Flyby>A_BtnFlyby</Flyby>
			<Disabled>A_BtnDisabled</Disabled>
			<PressedFlyby>A_BtnPressedFlyby</PressedFlyby>
		</ButtonDrawTemplate>
	</Button>
	<Button item="OCP_UserColor42">
		<ScreenID>UserColor42</ScreenID>
		<RelativePosition>true</RelativePosition>
		<Location>
			<X>20</X>
			<Y>920</Y>
		</Location>
		<Size>
			<CX>300</CX>
			<CY>40</CY>
		</Size>
		<Style_VScroll>false</Style_VScroll>
		<Style_HScroll>false</Style_HScroll>
		<Style_Transparent>false</Style_Transparent>
		<Style_Checkbox>false</Style_Checkbox>
		<Text>43 - Chat Channel 8</Text>
		<TextColor>
				<R>255</R>
				<G>255</G>
				<B>255</B>
		</TextColor>
		<ButtonDrawTemplate>
			<Normal>A_BtnNormal</Normal>
			<Pressed>A_BtnPressed</Pressed>
			<Flyby>A_BtnFlyby</Flyby>
			<Disabled>A_BtnDisabled</Disabled>
			<PressedFlyby>A_BtnPressedFlyby</PressedFlyby>
		</ButtonDrawTemplate>
	</Button>
	<Button item="OCP_UserColor43">
		<ScreenID>UserColor43</ScreenID>
		<RelativePosition>true</RelativePosition>
		<Location>
			<X>380</X>
			<Y>920</Y>
		</Location>
		<Size>
			<CX>300</CX>
			<CY>40</CY>
		</Size>
		<Style_VScroll>false</Style_VScroll>
		<Style_HScroll>false</Style_HScroll>
		<Style_Transparent>false</Style_Transparent>
		<Style_Checkbox>false</Style_Checkbox>
		<Text>44 - Chat Channel 9</Text>
		<TextColor>
				<R>255</R>
				<G>255</G>
				<B>255</B>
		</TextColor>
		<ButtonDrawTemplate>
			<Normal>A_BtnNormal</Normal>
			<Pressed>A_BtnPressed</Pressed>
			<Flyby>A_BtnFlyby</Flyby>
			<Disabled>A_BtnDisabled</Disabled>
			<PressedFlyby>A_BtnPressedFlyby</PressedFlyby>
		</ButtonDrawTemplate>
	</Button>
	<Button item="OCP_UserColor44">
		<ScreenID>UserColor44</ScreenID>
		<RelativePosition>true</RelativePosition>
		<Location>
			<X>20</X>
			<Y>960</Y>
		</Location>
		<Size>
			<CX>300</CX>
			<CY>40</CY>
		</Size>
		<Style_VScroll>false</Style_VScroll>
		<Style_HScroll>false</Style_HScroll>
		<Style_Transparent>false</Style_Transparent>
		<Style_Checkbox>false</Style_Checkbox>
		<Text>45 - Chat Channel 10</Text>
		<TextColor>
				<R>255</R>
				<G>255</G>
				<B>255</B>
		</TextColor>
		<ButtonDrawTemplate>
			<Normal>A_BtnNormal</Normal>
			<Pressed>A_BtnPressed</Pressed>
			<Flyby>A_BtnFlyby</Flyby>
			<Disabled>A_BtnDisabled</Disabled>
			<PressedFlyby>A_BtnPressedFlyby</PressedFlyby>
		</ButtonDrawTemplate>
	</Button>
	<Button item="OCP_UserColor45">
		<ScreenID>UserColor45</ScreenID>
		<RelativePosition>true</RelativePosition>
		<Location>
			<X>380</X>
			<Y>960</Y>
		</Location>
		<Size>
			<CX>300</CX>
			<CY>40</CY>
		</Size>
		<Style_VScroll>false</Style_VScroll>
		<Style_HScroll>false</Style_HScroll>
		<Style_Transparent>false</Style_Transparent>
		<Style_Checkbox>false</Style_Checkbox>
		<Text>46 - Melee Crits</Text>
		<TextColor>
				<R>255</R>
				<G>255</G>
				<B>255</B>
		</TextColor>
		<ButtonDrawTemplate>
			<Normal>A_BtnNormal</Normal>
			<Pressed>A_BtnPressed</Pressed>
			<Flyby>A_BtnFlyby</Flyby>
			<Disabled>A_BtnDisabled</Disabled>
			<PressedFlyby>A_BtnPressedFlyby</PressedFlyby>
		</ButtonDrawTemplate>
	</Button>
	<Button item="OCP_UserColor46">
		<ScreenID>UserColor46</ScreenID>
		<RelativePosition>true</RelativePosition>
		<Location>
			<X>20</X>
			<Y>1000</Y>
		</Location>
		<Size>
			<CX>300</CX>
			<CY>40</CY>
		</Size>
		<Style_VScroll>false</Style_VScroll>
		<Style_HScroll>false</Style_HScroll>
		<Style_Transparent>false</Style_Transparent>
		<Style_Checkbox>false</Style_Checkbox>
		<Text>47 - Spell Crits</Text>
		<TextColor>
				<R>255</R>
				<G>255</G>
				<B>255</B>
		</TextColor>
		<ButtonDrawTemplate>
			<Normal>A_BtnNormal</Normal>
			<Pressed>A_BtnPressed</Pressed>
			<Flyby>A_BtnFlyby</Flyby>
			<Disabled>A_BtnDisabled</Disabled>
			<PressedFlyby>A_BtnPressedFlyby</PressedFlyby>
		</ButtonDrawTemplate>
	</Button>
	<Button item="OCP_UserColor47">
		<ScreenID>UserColor47</ScreenID>
		<RelativePosition>true</RelativePosition>
		<Location>
			<X>380</X>
			<Y>1000</Y>
		</Location>
		<Size>
			<CX>300</CX>
			<CY>40</CY>
		</Size>
		<Style_VScroll>false</Style_VScroll>
		<Style_HScroll>false</Style_HScroll>
		<Style_Transparent>false</Style_Transparent>
		<Style_Checkbox>false</Style_Checkbox>
		<Text>48 - Too far away (melee)</Text>
		<TextColor>
				<R>255</R>
				<G>255</G>
				<B>255</B>
		</TextColor>
		<ButtonDrawTemplate>
			<Normal>A_BtnNormal</Normal>
			<Pressed>A_BtnPressed</Pressed>
			<Flyby>A_BtnFlyby</Flyby>
			<Disabled>A_BtnDisabled</Disabled>
			<PressedFlyby>A_BtnPressedFlyby</PressedFlyby>
		</ButtonDrawTemplate>
	</Button>
	<Button item="OCP_UserColor48">
		<ScreenID>UserColor48</ScreenID>
		<RelativePosition>true</RelativePosition>
		<Location>
			<X>20</X>
			<Y>1040</Y>
		</Location>
		<Size>
			<CX>300</CX>
			<CY>40</CY>
		</Size>
		<Style_VScroll>false</Style_VScroll>
		<Style_HScroll>false</Style_HScroll>
		<Style_Transparent>false</Style_Transparent>
		<Style_Checkbox>false</Style_Checkbox>
		<Text>49 - NPC Rampage</Text>
		<TextColor>
				<R>255</R>
				<G>255</G>
				<B>255</B>
		</TextColor>
		<ButtonDrawTemplate>
			<Normal>A_BtnNormal</Normal>
			<Pressed>A_BtnPressed</Pressed>
			<Flyby>A_BtnFlyby</Flyby>
			<Disabled>A_BtnDisabled</Disabled>
			<PressedFlyby>A_BtnPressedFlyby</PressedFlyby>
		</ButtonDrawTemplate>
	</Button>
	<Button item="OCP_UserColor49">
		<ScreenID>UserColor49</ScreenID>
		<RelativePosition>true</RelativePosition>
		<Location>
			<X>380</X>
			<Y>1040</Y>
		</Location>
		<Size>
			<CX>300</CX>
			<CY>40</CY>
		</Size>
		<Style_VScroll>false</Style_VScroll>
		<Style_HScroll>false</Style_HScroll>
		<Style_Transparent>false</Style_Transparent>
		<Style_Checkbox>false</Style_Checkbox>
		<Text>50 - NPC Flurry</Text>
		<TextColor>
				<R>255</R>
				<G>255</G>
				<B>255</B>
		</TextColor>
		<ButtonDrawTemplate>
			<Normal>A_BtnNormal</Normal>
			<Pressed>A_BtnPressed</Pressed>
			<Flyby>A_BtnFlyby</Flyby>
			<Disabled>A_BtnDisabled</Disabled>
			<PressedFlyby>A_BtnPressedFlyby</PressedFlyby>
		</ButtonDrawTemplate>
	</Button>
	<Button item="OCP_UserColor50">
		<ScreenID>UserColor50</ScreenID>
		<RelativePosition>true</RelativePosition>
		<Location>
			<X>20</X>
			<Y>1080</Y>
		</Location>
		<Size>
			<CX>300</CX>
			<CY>40</CY>
		</Size>
		<Style_VScroll>false</Style_VScroll>
		<Style_HScroll>false</Style_HScroll>
		<Style_Transparent>false</Style_Transparent>
		<Style_Checkbox>false</Style_Checkbox>
		<Text>51 - NPC Enrage</Text>
		<TextColor>
				<R>255</R>
				<G>255</G>
				<B>255</B>
		</TextColor>
		<ButtonDrawTemplate>
			<Normal>A_BtnNormal</Normal>
			<Pressed>A_BtnPressed</Pressed>
			<Flyby>A_BtnFlyby</Flyby>
			<Disabled>A_BtnDisabled</Disabled>
			<PressedFlyby>A_BtnPressedFlyby</PressedFlyby>
		</ButtonDrawTemplate>
	</Button>
	<Button item="OCP_UserColor51">
		<ScreenID>UserColor51</ScreenID>
		<RelativePosition>true</RelativePosition>
		<Location>
			<X>380</X>
			<Y>1080</Y>
		</Location>
		<Size>
			<CX>300</CX>
			<CY>40</CY>
		</Size>
		<Style_VScroll>false</Style_VScroll>
		<Style_HScroll>false</Style_HScroll>
		<Style_Transparent>false</Style_Transparent>
		<Style_Checkbox>false</Style_Checkbox>
		<Text>52 - Say echo</Text>
		<TextColor>
				<R>255</R>
				<G>255</G>
				<B>255</B>
		</TextColor>
		<ButtonDrawTemplate>
			<Normal>A_BtnNormal</Normal>
			<Pressed>A_BtnPressed</Pressed>
			<Flyby>A_BtnFlyby</Flyby>
			<Disabled>A_BtnDisabled</Disabled>
			<PressedFlyby>A_BtnPressedFlyby</PressedFlyby>
		</ButtonDrawTemplate>
	</Button>
	<Button item="OCP_UserColor52">
		<ScreenID>UserColor52</ScreenID>
		<RelativePosition>true</RelativePosition>
		<Location>
			<X>20</X>
			<Y>1120</Y>
		</Location>
		<Size>
			<CX>300</CX>
			<CY>40</CY>
		</Size>
		<Style_VScroll>false</Style_VScroll>
		<Style_HScroll>false</Style_HScroll>
		<Style_Transparent>false</Style_Transparent>
		<Style_Checkbox>false</Style_Checkbox>
		<Text>53 - Tell echo</Text>
		<TextColor>
				<R>255</R>
				<G>255</G>
				<B>255</B>
		</TextColor>
		<ButtonDrawTemplate>
			<Normal>A_BtnNormal</Normal>
			<Pressed>A_BtnPressed</Pressed>
			<Flyby>A_BtnFlyby</Flyby>
			<Disabled>A_BtnDisabled</Disabled>
			<PressedFlyby>A_BtnPressedFlyby</PressedFlyby>
		</ButtonDrawTemplate>
	</Button>
	<Button item="OCP_UserColor53">
		<ScreenID>UserColor53</ScreenID>
		<RelativePosition>true</RelativePosition>
		<Location>
			<X>380</X>
			<Y>1120</Y>
		</Location>
		<Size>
			<CX>300</CX>
			<CY>40</CY>
		</Size>
		<Style_VScroll>false</Style_VScroll>
		<Style_HScroll>false</Style_HScroll>
		<Style_Transparent>false</Style_Transparent>
		<Style_Checkbox>false</Style_Checkbox>
		<Text>54 - Group echo</Text>
		<TextColor>
				<R>255</R>
				<G>255</G>
				<B>255</B>
		</TextColor>
		<ButtonDrawTemplate>
			<Normal>A_BtnNormal</Normal>
			<Pressed>A_BtnPressed</Pressed>
			<Flyby>A_BtnFlyby</Flyby>
			<Disabled>A_BtnDisabled</Disabled>
			<PressedFlyby>A_BtnPressedFlyby</PressedFlyby>
		</ButtonDrawTemplate>
	</Button>
	<Button item="OCP_UserColor54">
		<ScreenID>UserColor54</ScreenID>
		<RelativePosition>true</RelativePosition>
		<Location>
			<X>20</X>
			<Y>1160</Y>
		</Location>
		<Size>
			<CX>300</CX>
			<CY>40</CY>
		</Size>
		<Style_VScroll>false</Style_VScroll>
		<Style_HScroll>false</Style_HScroll>
		<Style_Transparent>false</Style_Transparent>
		<Style_Checkbox>false</Style_Checkbox>
		<Text>55 - Guild echo</Text>
		<TextColor>
				<R>255</R>
				<G>255</G>
				<B>255</B>
		</TextColor>
		<ButtonDrawTemplate>
			<Normal>A_BtnNormal</Normal>
			<Pressed>A_BtnPressed</Pressed>
			<Flyby>A_BtnFlyby</Flyby>
			<Disabled>A_BtnDisabled</Disabled>
			<PressedFlyby>A_BtnPressedFlyby</PressedFlyby>
		</ButtonDrawTemplate>
	</Button>
	<Button item="OCP_UserColor55">
		<ScreenID>UserColor55</ScreenID>
		<RelativePosition>true</RelativePosition>
		<Location>
			<X>380</X>
			<Y>1160</Y>
		</Location>
		<Size>
			<CX>300</CX>
			<CY>40</CY>
		</Size>
		<Style_VScroll>false</Style_VScroll>
		<Style_HScroll>false</Style_HScroll>
		<Style_Transparent>false</Style_Transparent>
		<Style_Checkbox>false</Style_Checkbox>
		<Text>56 - OOC echo</Text>
		<TextColor>
				<R>255</R>
				<G>255</G>
				<B>255</B>
		</TextColor>
		<ButtonDrawTemplate>
			<Normal>A_BtnNormal</Normal>
			<Pressed>A_BtnPressed</Pressed>
			<Flyby>A_BtnFlyby</Flyby>
			<Disabled>A_BtnDisabled</Disabled>
			<PressedFlyby>A_BtnPressedFlyby</PressedFlyby>
		</ButtonDrawTemplate>
	</Button>
	<Button item="OCP_UserColor56">
		<ScreenID>UserColor56</ScreenID>
		<RelativePosition>true</RelativePosition>
		<Location>
			<X>20</X>
			<Y>1200</Y>
		</Location>
		<Size>
			<CX>300</CX>
			<CY>40</CY>
		</Size>
		<Style_VScroll>false</Style_VScroll>
		<Style_HScroll>false</Style_HScroll>
		<Style_Transparent>false</Style_Transparent>
		<Style_Checkbox>false</Style_Checkbox>
		<Text>57 - Auction echo</Text>
		<TextColor>
				<R>255</R>
				<G>255</G>
				<B>255</B>
		</TextColor>
		<ButtonDrawTemplate>
			<Normal>A_BtnNormal</Normal>
			<Pressed>A_BtnPressed</Pressed>
			<Flyby>A_BtnFlyby</Flyby>
			<Disabled>A_BtnDisabled</Disabled>
			<PressedFlyby>A_BtnPressedFlyby</PressedFlyby>
		</ButtonDrawTemplate>
	</Button>
	<Button item="OCP_UserColor57">
		<ScreenID>UserColor57</ScreenID>
		<RelativePosition>true</RelativePosition>
		<Location>
			<X>380</X>
			<Y>1200</Y>
		</Location>
		<Size>
			<CX>300</CX>
			<CY>40</CY>
		</Size>
		<Style_VScroll>false</Style_VScroll>
		<Style_HScroll>false</Style_HScroll>
		<Style_Transparent>false</Style_Transparent>
		<Style_Checkbox>false</Style_Checkbox>
		<Text>58 - Shout echo</Text>
		<TextColor>
				<R>255</R>
				<G>255</G>
				<B>255</B>
		</TextColor>
		<ButtonDrawTemplate>
			<Normal>A_BtnNormal</Normal>
			<Pressed>A_BtnPressed</Pressed>
			<Flyby>A_BtnFlyby</Flyby>
			<Disabled>A_BtnDisabled</Disabled>
			<PressedFlyby>A_BtnPressedFlyby</PressedFlyby>
		</ButtonDrawTemplate>
	</Button>
	<Button item="OCP_UserColor58">
		<ScreenID>UserColor58</ScreenID>
		<RelativePosition>true</RelativePosition>
		<Location>
			<X>20</X>
			<Y>1240</Y>
		</Location>
		<Size>
			<CX>300</CX>
			<CY>40</CY>
		</Size>
		<Style_VScroll>false</Style_VScroll>
		<Style_HScroll>false</Style_HScroll>
		<Style_Transparent>false</Style_Transparent>
		<Style_Checkbox>false</Style_Checkbox>
		<Text>59 - Emote echo</Text>
		<TextColor>
				<R>255</R>
				<G>255</G>
				<B>255</B>
		</TextColor>
		<ButtonDrawTemplate>
			<Normal>A_BtnNormal</Normal>
			<Pressed>A_BtnPressed</Pressed>
			<Flyby>A_BtnFlyby</Flyby>
			<Disabled>A_BtnDisabled</Disabled>
			<PressedFlyby>A_BtnPressedFlyby</PressedFlyby>
		</ButtonDrawTemplate>
	</Button>
	<Button item="OCP_UserColor59">
		<ScreenID>UserColor59</ScreenID>
		<RelativePosition>true</RelativePosition>
		<Location>
			<X>380</X>
			<Y>1240</Y>
		</Location>
		<Size>
			<CX>300</CX>
			<CY>40</CY>
		</Size>
		<Style_VScroll>false</Style_VScroll>
		<Style_HScroll>false</Style_HScroll>
		<Style_Transparent>false</Style_Transparent>
		<Style_Checkbox>false</Style_Checkbox>
		<Text>60 - Chat Channel 1 echo</Text>
		<TextColor>
				<R>255</R>
				<G>255</G>
				<B>255</B>
		</TextColor>
		<ButtonDrawTemplate>
			<Normal>A_BtnNormal</Normal>
			<Pressed>A_BtnPressed</Pressed>
			<Flyby>A_BtnFlyby</Flyby>
			<Disabled>A_BtnDisabled</Disabled>
			<PressedFlyby>A_BtnPressedFlyby</PressedFlyby>
		</ButtonDrawTemplate>
	</Button>
	<Button item="OCP_UserColor60">
		<ScreenID>UserColor60</ScreenID>
		<RelativePosition>true</RelativePosition>
		<Location>
			<X>20</X>
			<Y>1280</Y>
		</Location>
		<Size>
			<CX>300</CX>
			<CY>40</CY>
		</Size>
		<Style_VScroll>false</Style_VScroll>
		<Style_HScroll>false</Style_HScroll>
		<Style_Transparent>false</Style_Transparent>
		<Style_Checkbox>false</Style_Checkbox>
		<Text>61 - Chat Channel 2 echo</Text>
		<TextColor>
				<R>255</R>
				<G>255</G>
				<B>255</B>
		</TextColor>
		<ButtonDrawTemplate>
			<Normal>A_BtnNormal</Normal>
			<Pressed>A_BtnPressed</Pressed>
			<Flyby>A_BtnFlyby</Flyby>
			<Disabled>A_BtnDisabled</Disabled>
			<PressedFlyby>A_BtnPressedFlyby</PressedFlyby>
		</ButtonDrawTemplate>
	</Button>
	<Button item="OCP_UserColor61">
		<ScreenID>UserColor61</ScreenID>
		<RelativePosition>true</RelativePosition>
		<Location>
			<X>380</X>
			<Y>1280</Y>
		</Location>
		<Size>
			<CX>300</CX>
			<CY>40</CY>
		</Size>
		<Style_VScroll>false</Style_VScroll>
		<Style_HScroll>false</Style_HScroll>
		<Style_Transparent>false</Style_Transparent>
		<Style_Checkbox>false</Style_Checkbox>
		<Text>62 - Chat Channel 3 echo</Text>
		<TextColor>
				<R>255</R>
				<G>255</G>
				<B>255</B>
		</TextColor>
		<ButtonDrawTemplate>
			<Normal>A_BtnNormal</Normal>
			<Pressed>A_BtnPressed</Pressed>
			<Flyby>A_BtnFlyby</Flyby>
			<Disabled>A_BtnDisabled</Disabled>
			<PressedFlyby>A_BtnPressedFlyby</PressedFlyby>
		</ButtonDrawTemplate>
	</Button>
	<Button item="OCP_UserColor62">
		<ScreenID>UserColor62</ScreenID>
		<RelativePosition>true</RelativePosition>
		<Location>
			<X>20</X>
			<Y>1320</Y>
		</Location>
		<Size>
			<CX>300</CX>
			<CY>40</CY>
		</Size>
		<Style_VScroll>false</Style_VScroll>
		<Style_HScroll>false</Style_HScroll>
		<Style_Transparent>false</Style_Transparent>
		<Style_Checkbox>false</Style_Checkbox>
		<Text>63 - Chat Channel 4 echo</Text>
		<TextColor>
				<R>255</R>
				<G>255</G>
				<B>255</B>
		</TextColor>
		<ButtonDrawTemplate>
			<Normal>A_BtnNormal</Normal>
			<Pressed>A_BtnPressed</Pressed>
			<Flyby>A_BtnFlyby</Flyby>
			<Disabled>A_BtnDisabled</Disabled>
			<PressedFlyby>A_BtnPressedFlyby</PressedFlyby>
		</ButtonDrawTemplate>
	</Button>
	<Button item="OCP_UserColor63">
		<ScreenID>UserColor63</ScreenID>
		<RelativePosition>true</RelativePosition>
		<Location>
			<X>380</X>
			<Y>1320</Y>
		</Location>
		<Size>
			<CX>300</CX>
			<CY>40</CY>
		</Size>
		<Style_VScroll>false</Style_VScroll>
		<Style_HScroll>false</Style_HScroll>
		<Style_Transparent>false</Style_Transparent>
		<Style_Checkbox>false</Style_Checkbox>
		<Text>64 - Chat Channel 5 echo</Text>
		<TextColor>
				<R>255</R>
				<G>255</G>
				<B>255</B>
		</TextColor>
		<ButtonDrawTemplate>
			<Normal>A_BtnNormal</Normal>
			<Pressed>A_BtnPressed</Pressed>
			<Flyby>A_BtnFlyby</Flyby>
			<Disabled>A_BtnDisabled</Disabled>
			<PressedFlyby>A_BtnPressedFlyby</PressedFlyby>
		</ButtonDrawTemplate>
	</Button>
	<Button item="OCP_UserColor64">
		<ScreenID>UserColor64</ScreenID>
		<RelativePosition>true</RelativePosition>
		<Location>
			<X>20</X>
			<Y>1360</Y>
		</Location>
		<Size>
			<CX>300</CX>
			<CY>40</CY>
		</Size>
		<Style_VScroll>false</Style_VScroll>
		<Style_HScroll>false</Style_HScroll>
		<Style_Transparent>false</Style_Transparent>
		<Style_Checkbox>false</Style_Checkbox>
		<Text>65 - Chat Channel 6 echo</Text>
		<TextColor>
				<R>255</R>
				<G>255</G>
				<B>255</B>
		</TextColor>
		<ButtonDrawTemplate>
			<Normal>A_BtnNormal</Normal>
			<Pressed>A_BtnPressed</Pressed>
			<Flyby>A_BtnFlyby</Flyby>
			<Disabled>A_BtnDisabled</Disabled>
			<PressedFlyby>A_BtnPressedFlyby</PressedFlyby>
		</ButtonDrawTemplate>
	</Button>
	<Button item="OCP_UserColor65">
		<ScreenID>UserColor65</ScreenID>
		<RelativePosition>true</RelativePosition>
		<Location>
			<X>380</X>
			<Y>1360</Y>
		</Location>
		<Size>
			<CX>300</CX>
			<CY>40</CY>
		</Size>
		<Style_VScroll>false</Style_VScroll>
		<Style_HScroll>false</Style_HScroll>
		<Style_Transparent>false</Style_Transparent>
		<Style_Checkbox>false</Style_Checkbox>
		<Text>66 - Chat Channel 7 echo</Text>
		<TextColor>
				<R>255</R>
				<G>255</G>
				<B>255</B>
		</TextColor>
		<ButtonDrawTemplate>
			<Normal>A_BtnNormal</Normal>
			<Pressed>A_BtnPressed</Pressed>
			<Flyby>A_BtnFlyby</Flyby>
			<Disabled>A_BtnDisabled</Disabled>
			<PressedFlyby>A_BtnPressedFlyby</PressedFlyby>
		</ButtonDrawTemplate>
	</Button>
	<Button item="OCP_UserColor66">
		<ScreenID>UserColor66</ScreenID>
		<RelativePosition>true</RelativePosition>
		<Location>
			<X>20</X>
			<Y>1400</Y>
		</Location>
		<Size>
			<CX>300</CX>
			<CY>40</CY>
		</Size>
		<Style_VScroll>false</Style_VScroll>
		<Style_HScroll>false</Style_HScroll>
		<Style_Transparent>false</Style_Transparent>
		<Style_Checkbox>false</Style_Checkbox>
		<Text>67 - Chat Channel 8 echo</Text>
		<TextColor>
				<R>255</R>
				<G>255</G>
				<B>255</B>
		</TextColor>
		<ButtonDrawTemplate>
			<Normal>A_BtnNormal</Normal>
			<Pressed>A_BtnPressed</Pressed>
			<Flyby>A_BtnFlyby</Flyby>
			<Disabled>A_BtnDisabled</Disabled>
			<PressedFlyby>A_BtnPressedFlyby</PressedFlyby>
		</ButtonDrawTemplate>
	</Button>
	<Button item="OCP_UserColor67">
		<ScreenID>UserColor67</ScreenID>
		<RelativePosition>true</RelativePosition>
		<Location>
			<X>380</X>
			<Y>1400</Y>
		</Location>
		<Size>
			<CX>300</CX>
			<CY>40</CY>
		</Size>
		<Style_VScroll>false</Style_VScroll>
		<Style_HScroll>false</Style_HScroll>
		<Style_Transparent>false</Style_Transparent>
		<Style_Checkbox>false</Style_Checkbox>
		<Text>68 - Chat Channel 9 echo</Text>
		<TextColor>
				<R>255</R>
				<G>255</G>
				<B>255</B>
		</TextColor>
		<ButtonDrawTemplate>
			<Normal>A_BtnNormal</Normal>
			<Pressed>A_BtnPressed</Pressed>
			<Flyby>A_BtnFlyby</Flyby>
			<Disabled>A_BtnDisabled</Disabled>
			<PressedFlyby>A_BtnPressedFlyby</PressedFlyby>
		</ButtonDrawTemplate>
	</Button>
	<Button item="OCP_UserColor68">
		<ScreenID>UserColor68</ScreenID>
		<RelativePosition>true</RelativePosition>
		<Location>
			<X>20</X>
			<Y>1440</Y>
		</Location>
		<Size>
			<CX>300</CX>
			<CY>40</CY>
		</Size>
		<Style_VScroll>false</Style_VScroll>
		<Style_HScroll>false</Style_HScroll>
		<Style_Transparent>false</Style_Transparent>
		<Style_Checkbox>false</Style_Checkbox>
		<Text>69 - Chat Channel 10 echo</Text>
		<TextColor>
				<R>255</R>
				<G>255</G>
				<B>255</B>
		</TextColor>
		<ButtonDrawTemplate>
			<Normal>A_BtnNormal</Normal>
			<Pressed>A_BtnPressed</Pressed>
			<Flyby>A_BtnFlyby</Flyby>
			<Disabled>A_BtnDisabled</Disabled>
			<PressedFlyby>A_BtnPressedFlyby</PressedFlyby>
		</ButtonDrawTemplate>
	</Button>
	<Button item="OCP_UserColor69">
		<ScreenID>UserColor69</ScreenID>
		<RelativePosition>true</RelativePosition>
		<Location>
			<X>380</X>
			<Y>1440</Y>
		</Location>
		<Size>
			<CX>300</CX>
			<CY>40</CY>
		</Size>
		<Style_VScroll>false</Style_VScroll>
		<Style_HScroll>false</Style_HScroll>
		<Style_Transparent>false</Style_Transparent>
		<Style_Checkbox>false</Style_Checkbox>
		<Text>70 - Unused at this time</Text>
		<TextColor>
				<R>255</R>
				<G>255</G>
				<B>255</B>
		</TextColor>
		<ButtonDrawTemplate>
			<Normal>A_BtnNormal</Normal>
			<Pressed>A_BtnPressed</Pressed>
			<Flyby>A_BtnFlyby</Flyby>
			<Disabled>A_BtnDisabled</Disabled>
			<PressedFlyby>A_BtnPressedFlyby</PressedFlyby>
		</ButtonDrawTemplate>
	</Button>
	<Button item="OCP_UserColor70"> 
		<ScreenID>UserColor70</ScreenID>
		<RelativePosition>true</RelativePosition>
		<Location>
			<X>20</X>
			<Y>1480</Y>
		</Location>
		<Size>
			<CX>300</CX>
			<CY>40</CY>
		</Size>
		<Style_VScroll>false</Style_VScroll>
		<Style_HScroll>false</Style_HScroll>
		<Style_Transparent>false</Style_Transparent>
		<Style_Checkbox>false</Style_Checkbox>
		<Text>71 - Item Tags</Text>
		<TextColor>
				<R>255</R>
				<G>255</G>
				<B>255</B>
		</TextColor>
		<ButtonDrawTemplate>
			<Normal>A_BtnNormal</Normal>
			<Pressed>A_BtnPressed</Pressed>
			<Flyby>A_BtnFlyby</Flyby>
			<Disabled>A_BtnDisabled</Disabled>
			<PressedFlyby>A_BtnPressedFlyby</PressedFlyby>
		</ButtonDrawTemplate>
	</Button>

	<Button item="OCP_UserColor71"> 
		<ScreenID>UserColor71</ScreenID>
		<RelativePosition>true</RelativePosition>
		<Location>
			<X>380</X>
			<Y>1480</Y>
		</Location>
		<Size>
			<CX>300</CX>
			<CY>40</CY>
		</Size>
		<Style_VScroll>false</Style_VScroll>
		<Style_HScroll>false</Style_HScroll>
		<Style_Transparent>false</Style_Transparent>
		<Style_Checkbox>false</Style_Checkbox>
		<Text>72 - Raid Say</Text>
		<TextColor>
				<R>255</R>
				<G>255</G>
				<B>255</B>
		</TextColor>
		<ButtonDrawTemplate>
			<Normal>A_BtnNormal</Normal>
			<Pressed>A_BtnPressed</Pressed>
			<Flyby>A_BtnFlyby</Flyby>
			<Disabled>A_BtnDisabled</Disabled>
			<PressedFlyby>A_BtnPressedFlyby</PressedFlyby>
		</ButtonDrawTemplate>
	</Button>


	<Page item="OptionsColorPage">
		<ScreenID>OptionsColorPage</ScreenID>
		<RelativePosition>true</RelativePosition>

		

		<Style_VScroll>true</Style_VScroll>
		<Style_HScroll>false</Style_HScroll>
		<Style_Transparent>false</Style_Transparent>
		<DrawTemplate>WDT_Def</DrawTemplate>
		<TabText>Colors</TabText>
		<TabTextColor>
				<R>255</R>
				<G>255</G>
				<B>255</B>
		</TabTextColor>
		<TabTextActiveColor>
				<R>255</R>
				<G>255</G>
				<B>0</B>
		</TabTextActiveColor>
		<Style_Sizable>true</Style_Sizable>
			<Pieces>OCP_UserColorDefault</Pieces>
			<Pieces>OCP_UserColor0</Pieces>
			<Pieces>OCP_UserColor1</Pieces>
			<Pieces>OCP_UserColor2</Pieces>
			<Pieces>OCP_UserColor3</Pieces>
			<Pieces>OCP_UserColor4</Pieces>
			<Pieces>OCP_UserColor5</Pieces>
			<Pieces>OCP_UserColor6</Pieces>
			<Pieces>OCP_UserColor7</Pieces>
			<Pieces>OCP_UserColor8</Pieces>
			<Pieces>OCP_UserColor9</Pieces>
			<Pieces>OCP_UserColor10</Pieces>
			<Pieces>OCP_UserColor11</Pieces>
			<Pieces>OCP_UserColor12</Pieces>
			<Pieces>OCP_UserColor13</Pieces>
			<Pieces>OCP_UserColor14</Pieces>
			<Pieces>OCP_UserColor15</Pieces>
			<Pieces>OCP_UserColor16</Pieces>
			<Pieces>OCP_UserColor17</Pieces>
			<Pieces>OCP_UserColor18</Pieces>
			<Pieces>OCP_UserColor19</Pieces>
			<Pieces>OCP_UserColor20</Pieces>
			<Pieces>OCP_UserColor21</Pieces>
			<Pieces>OCP_UserColor22</Pieces>
			<Pieces>OCP_UserColor23</Pieces>
			<Pieces>OCP_UserColor24</Pieces>
			<Pieces>OCP_UserColor25</Pieces>
			<Pieces>OCP_UserColor26</Pieces>
			<Pieces>OCP_UserColor27</Pieces>
			<Pieces>OCP_UserColor28</Pieces>
			<Pieces>OCP_UserColor29</Pieces>
			<Pieces>OCP_UserColor30</Pieces>
			<Pieces>OCP_UserColor31</Pieces>
			<Pieces>OCP_UserColor32</Pieces>
			<Pieces>OCP_UserColor33</Pieces>
			<Pieces>OCP_UserColor34</Pieces>
			<Pieces>OCP_UserColor35</Pieces>
			<Pieces>OCP_UserColor36</Pieces>
			<Pieces>OCP_UserColor37</Pieces>
			<Pieces>OCP_UserColor38</Pieces>
			<Pieces>OCP_UserColor39</Pieces>
			<Pieces>OCP_UserColor40</Pieces>
			<Pieces>OCP_UserColor41</Pieces>
			<Pieces>OCP_UserColor42</Pieces>
			<Pieces>OCP_UserColor43</Pieces>
			<Pieces>OCP_UserColor44</Pieces>
			<Pieces>OCP_UserColor45</Pieces>
			<Pieces>OCP_UserColor46</Pieces>
			<Pieces>OCP_UserColor47</Pieces>
			<Pieces>OCP_UserColor48</Pieces>
			<Pieces>OCP_UserColor49</Pieces>
			<Pieces>OCP_UserColor50</Pieces>
			<Pieces>OCP_UserColor51</Pieces>
			<Pieces>OCP_UserColor52</Pieces>
			<Pieces>OCP_UserColor53</Pieces>
			<Pieces>OCP_UserColor54</Pieces>
			<Pieces>OCP_UserColor55</Pieces>
			<Pieces>OCP_UserColor56</Pieces>
			<Pieces>OCP_UserColor57</Pieces>
			<Pieces>OCP_UserColor58</Pieces>
			<Pieces>OCP_UserColor59</Pieces>
			<Pieces>OCP_UserColor60</Pieces>
			<Pieces>OCP_UserColor61</Pieces>
			<Pieces>OCP_UserColor62</Pieces>
			<Pieces>OCP_UserColor63</Pieces>
			<Pieces>OCP_UserColor64</Pieces>
			<Pieces>OCP_UserColor65</Pieces>
			<Pieces>OCP_UserColor66</Pieces>
			<Pieces>OCP_UserColor67</Pieces>
			<Pieces>OCP_UserColor68</Pieces>
			<Pieces>OCP_UserColor69</Pieces>
			<Pieces>OCP_UserColor70</Pieces>
			<Pieces>OCP_UserColor71</Pieces>
	</Page>

	

	<TabBox item="OPTW_OptionsSubwindows">
		<ScreenID>OPTW_OptionsSubwindows</ScreenID>
		<RelativePosition>true</RelativePosition>

		<AutoStretch>true</AutoStretch>
		<TopAnchorToTop>true</TopAnchorToTop>
		<BottomAnchorToTop>false</BottomAnchorToTop>
		<LeftAnchorToLeft>true</LeftAnchorToLeft>
		<RightAnchorToLeft>false</RightAnchorToLeft>

		<TabBorderTemplate>FT_DefTabBorder</TabBorderTemplate>
		<PageBorderTemplate>FT_DefPageBorder</PageBorderTemplate>
			<Pages>OptionsGeneralPage</Pages>
			<Pages>OptionsDisplayPage</Pages>
			<Pages>OptionsMousePage</Pages>
			<Pages>OptionsKeyboardPage</Pages>
			<Pages>OptionsChatPage</Pages>
			<Pages>OptionsColorPage</Pages>
	</TabBox>

	<Screen item="OptionsWindow">
		<ScreenID />
		<RelativePosition>false</RelativePosition>
		<Location>
			<X>180</X>
			<Y>94</Y>
		</Location>
		<Size>
			<CX>800</CX>
			<CY>790</CY>
		</Size>
		<Text>Options</Text>
		<Style_VScroll>false</Style_VScroll>
		<Style_HScroll>false</Style_HScroll>
		<Style_Transparent>false</Style_Transparent>
		<TooltipReference />
		<DrawTemplate>WDT_Def</DrawTemplate>
		<Style_Titlebar>true</Style_Titlebar>
		<Style_Closebox>true</Style_Closebox>
		<Style_Minimizebox>true</Style_Minimizebox>
		<Style_Border>true</Style_Border>
		<Style_Sizable>true</Style_Sizable>
			<Pieces>OPTW_OptionsSubwindows</Pieces>
	</Screen>

</XML>