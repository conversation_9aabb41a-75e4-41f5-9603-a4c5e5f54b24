#include "sigma.h"

#include "zeal.h"
#include "equip_item.h"
#include "game_addresses.h"
#include "game_functions.h"
#include "game_structures.h"
#include "game_ui.h"
#include "hook_wrapper.h"

// Static hook function for right-click handling
static void __fastcall Sigma_CInvSlot_HandleRButtonUp(Zeal::GameUI::InvSlot *inv_slot, int unused_edx, int x, int y) {
  // First check if Sigma should handle this Alt+Right-click
  if (ZealService::get_instance()->sigma->HandleAltRightClick(inv_slot)) {
    return;  // Sigma handled it, don't pass to other handlers
  }

  // Check if EquipItem should handle this right-click
  if (ZealService::get_instance()->equip_item_hook->HandleRButtonUp(inv_slot)) {
    return;  // EquipItem handled it
  }

  // Pass to original handler
  ZealService::get_instance()->hooks->hook_map["CInvSlot_HandleRButtonUp"]->original(Sigma_CInvSlot_HandleRButtonUp)(
      inv_slot, unused_edx, x, y);
}

Sigma::Sigma(ZealService *zeal) : zeal_service(zeal) {
  // Initialize Sigma module
  if (!Zeal::Game::is_new_ui()) {
    return;
  }

  // Replace the existing hook with our own that checks Sigma first, then EquipItem
  // Note: This replaces the hook that EquipItem set up, so we need to call EquipItem's handler manually
  zeal->hooks->Add("CInvSlot_HandleRButtonUp", 0x422804, Sigma_CInvSlot_HandleRButtonUp, hook_type_detour);
}

Sigma::~Sigma() {
  // Cleanup
}

bool Sigma::HandleAltRightClick(Zeal::GameUI::InvSlot *inv_slot) {
  if (!tradeskill_alt_rightclick_enabled.get() || !inv_slot) {
    return false;
  }

  // Check if Alt key is pressed
  Zeal::GameUI::CXWndManager *wnd_mgr = Zeal::Game::get_wnd_manager();
  if (!wnd_mgr || !wnd_mgr->AltKeyState) {
    return false;
  }

  // Validate this is a valid tradeskill transfer
  if (!IsValidTradeskillTransfer(inv_slot)) {
    return false;
  }

  // Perform the transfer
  return TransferSingleItemToTradeskill(inv_slot);
}

bool Sigma::IsValidTradeskillTransfer(Zeal::GameUI::InvSlot *src_slot) {
  if (!src_slot || !src_slot->Item || !src_slot->invSlotWnd) {
    return false;
  }

  // Check if the item is in an inventory bag (slot IDs 250-329)
  int src_slot_id = src_slot->invSlotWnd->SlotID;
  if (src_slot_id < 250 || src_slot_id > 329) {
    return false;
  }

  // Check if there's an active tradeskill container
  Zeal::GameUI::ContainerWnd *tradeskill_container = FindActiveTradeskillContainer();
  if (!tradeskill_container) {
    return false;
  }

  // Check if the item can fit in the tradeskill container
  if (!tradeskill_container->pContainerInfo || 
      tradeskill_container->pContainerInfo->Container.SizeCapacity < src_slot->Item->Size) {
    return false;
  }

  return true;
}

bool Sigma::TransferSingleItemToTradeskill(Zeal::GameUI::InvSlot *src_slot) {
  Zeal::GameUI::ContainerWnd *tradeskill_container = FindActiveTradeskillContainer();
  if (!tradeskill_container) {
    return false;
  }

  // Find an empty slot in the tradeskill container
  const int num_slots = tradeskill_container->pContainerInfo->Container.Capacity;
  for (int i = 0; i < num_slots; ++i) {
    auto inv_slot_wnd = tradeskill_container->pSlotWnds[i];
    if (inv_slot_wnd && inv_slot_wnd->invSlot && !inv_slot_wnd->invSlot->Item) {
      // Found an empty slot, move a single item here
      return MoveSingleItemFromStack(src_slot, inv_slot_wnd->SlotID);
    }
  }

  // No empty slots available
  if (tradeskill_show_messages.get()) {
    Zeal::Game::print_chat("Sigma: No empty slots in tradeskill container");
  }
  return false;
}

Zeal::GameUI::ContainerWnd *Sigma::FindActiveTradeskillContainer() {
  // Based on the existing code in npc_give.cpp
  static int kFirstCombineType = 9;  // Types < 7 are bags

  auto container_mgr = Zeal::Game::Windows->ContainerMgr;
  if (!container_mgr) return nullptr;

  // Check for world container first (like forges, ovens, etc.)
  if (container_mgr->pWorldItems) {
    if (container_mgr->pWorldItems->Container.IsOpen &&
        container_mgr->pWorldItems->Container.Combine >= kFirstCombineType) {
      for (int i = 0; i < 0x11; ++i) {
        auto wnd = container_mgr->pPCContainers[i];
        if (wnd && (wnd->pContainerInfo == container_mgr->pWorldItems)) {
          return (wnd->IsVisible) ? wnd : nullptr;
        }
      }
    }
    return nullptr;
  }

  // Check for player-owned tradeskill containers
  for (int i = 0; i < 0x11; ++i) {
    auto wnd = container_mgr->pPCContainers[i];
    if (wnd && wnd->IsVisible && wnd->pContainerInfo &&
        wnd->pContainerInfo->Container.IsOpen &&
        wnd->pContainerInfo->Container.Combine >= kFirstCombineType) {
      return wnd;
    }
  }

  return nullptr;
}

bool Sigma::MoveSingleItemFromStack(Zeal::GameUI::InvSlot *src_slot, int dst_slot_id) {
  if (!src_slot || !src_slot->Item) {
    return false;
  }

  // Use the game's move_item function to transfer a single item
  // Parameters: src_slot_id, dst_slot_id, unknown1, quantity
  Zeal::Game::move_item(src_slot->invSlotWnd->SlotID, dst_slot_id, 0, 1);
  
  return true;
}
