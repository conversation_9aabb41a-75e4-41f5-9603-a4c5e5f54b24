P -194.0000, 639.0000, -172.0000, 255, 0, 0, 3, to_Echo_Caverns
P -1625.0000, 1100.0000, -77.0000, 255, 0, 0, 3, to_Echo_Caverns
P -732.0000, -293.0000, -103.0000, 255, 0, 0, 3, to_The_Bazaar
P -1781.0000, -355.0000, -58.0000, 255, 0, 0, 3, to_Echo_Caverns
P 76.0000, -114.0000, -166.0000, 255, 0, 0, 3, to_Paludal_Caverns
P -320.0000, 1065.0000, -31.0000, 255, 0, 0, 3, to_Paludal_Caverns
P -1511.0000, -168.0000, -60.0000, 255, 0, 0, 3, to_The_Nexus
P -190.0000, 982.0000, 0.0000, 255, 0, 0, 2, Succor
P -1625.0000, 349.0000, -59.0000, 128, 0, 128, 2, Oven
P -254.0000, 459.0000, -26.0000, 128, 0, 128, 2, <PERSON>ven
P -1414.0000, -271.0000, -58.0000, 128, 0, 128, 2, Loom
P -1421.0000, -413.0000, -58.0000, 128, 0, 128, 2, <PERSON>rew_<PERSON><PERSON>
P -1076.0000, -257.0000, -58.0000, 128, 0, 128, 2, Oven
P -61.0000, -126.0000, -29.0000, 128, 0, 128, 2, Loom
P -144.0000, -123.0000, -31.0000, 128, 0, 128, 2, Forge
P -313.0000, 18.0000, 0.0000, 128, 0, 128, 2, Brew_Barrel
P -404.0000, -71.0000, 0.0000, 128, 0, 128, 2, Brew_Barrel
P -1404.0000, 387.0000, -55.0000, 128, 0, 128, 2, Kiln
P -1363.0000, 402.0000, -55.0000, 128, 0, 128, 2, Pottery_Wheel
P -1310.0000, -26.0000, -58.0000, 128, 0, 128, 2, Forge
P -1456.0000, 333.0000, -36.0000, 128, 0, 128, 2, Forge
P 281.0000, -1206.0000, -49.0000, 240, 240, 240, 2, Talking_Scrolls
P 237.0000, -1176.0000, -49.0000, 240, 240, 240, 2, Izibat`s_Wonderous_Robes
P 100.0000, -421.0000, -190.0000, 240, 240, 0, 2, Underwater_Tunnel
P -1691.0000, -303.0000, -58.0000, 240, 240, 0, 2, Fake_Wall
P -1725.0000, -303.0000, -58.0000, 240, 240, 0, 2, Fake_Wall
P -34.0000, 380.0000, -39.0000, 240, 240, 240, 2, Baracks
P -1600.0000, 343.0000, -58.0000, 240, 240, 240, 2, The_Lost_Turnip
P -1250.0000, -191.0000, -112.0000, 240, 240, 0, 2, Teleport_to_Main_Level_(Click_Tree)
P -1339.0000, -195.0000, -60.0000, 240, 240, 0, 2, Teleport_to_Tree
P -892.0000, -50.0000, -58.0000, 240, 240, 240, 2, Shandling`s_Roost_Casino
P -1038.0000, -152.0000, -58.0000, 240, 240, 240, 2, Marchap`s_Micro_Metals
P -376.0000, 224.0000, -58.0000, 240, 240, 240, 2, Haven_Defenders
P 163.0000, -716.0000, -58.0000, 240, 240, 240, 2, House_of_Midst
P 121.0000, -104.0000, -31.0000, 240, 240, 240, 2, The_Fordel_Hold
P -1300.0000, -174.0000, 0.0000, 255, 255, 255, 2, Druid/Ranger/Beastlord_Guild
P 138.0000, -172.0000, -31.0000, 255, 210, 0, 2, Teridat_(Banker)
P 116.0000, -172.0000, -31.0000, 255, 210, 0, 2, Faloensar_(Banker)
P -132.0000, -1344.0000, -49.0000, 255, 210, 0, 2, Pansak_(Banker)
P -132.0000, -1325.0000, -49.0000, 255, 210, 0, 2, Heralow_(Banker)
P -1845.0000, 585.0000, -59.0000, 255, 210, 0, 2, Geologo_(Banker)
P -1845.0000, 596.0000, -59.0000, 255, 210, 0, 2, Raldilikis_(Banker)
P -1813.0000, -145.0000, -58.0000, 255, 210, 0, 2, Tarson_(Banker)
P -1637.0000, -236.0000, -58.0000, 0, 0, 0, 2, Nansin_(Soulbinder)
P -1573.0000, -267.0000, -58.0000, 0, 128, 0, 2, Steaon_Alarenier_(Alchemy)
P -1073.0000, -251.0000, -58.0000, 0, 128, 0, 2, Fantoal_(Baking)
P -1627.0000, 335.0000, -59.0000, 0, 128, 0, 2, Bolrodo_(Merchant)
P -628.0000, -189.0000, -47.0000, 0, 128, 0, 2, Oxon_Essencebinder_(Pottery)
P -628.0000, -172.0000, -47.0000, 0, 128, 0, 2, Kolburn_Inglewort_(Potions)
P -634.0000, -146.0000, -47.0000, 0, 128, 0, 2, Rowl_Tanner_(Tailor)
P -651.0000, -146.0000, -47.0000, 0, 128, 0, 2, Eastring_Fashing_(Tailor)
P -645.0000, 11.0000, -47.0000, 0, 128, 0, 2, Garn_Allonode_(Jewelcrafting)
P -629.0000, 12.0000, -47.0000, 0, 128, 0, 2, Adinson_Allonode_(Jewelcrafting)
P -755.0000, -31.0000, -72.0000, 0, 128, 0, 2, Talor_(Barkeep)
P -1432.0000, -308.0000, -58.0000, 0, 128, 0, 2, Nelm_the_Tiny_(Armor)
P -1203.0000, -391.0000, -42.0000, 0, 128, 0, 2, Jostan_(Merchant)
P -1191.0000, -444.0000, -58.0000, 0, 128, 0, 2, Eustace_Dakes_(Merchant)
P -1023.0000, -172.0000, -58.0000, 0, 128, 0, 2, Ralshalod_(Merchant)
P -1050.0000, -173.0000, -58.0000, 0, 128, 0, 2, Barotolen_(Merchant)
P -961.0000, -260.0000, -58.0000, 0, 128, 0, 2, Junun_(Fletching)
P -978.0000, -260.0000, -58.0000, 0, 128, 0, 2, Tabart_(Fletching)
P -907.0000, -195.0000, -58.0000, 0, 128, 0, 2, Bilkel_(Merchant)
P -845.0000, 23.0000, -79.0000, 0, 0, 0, 2, Therin_Asakith_(Casino)
P -330.0000, -157.0000, -31.0000, 0, 128, 0, 2, Lesia_Furthers_(Brewing)
P -327.0000, -167.0000, -31.0000, 0, 128, 0, 2, Lander_Furthers_(Brewing)
P -350.0000, -134.0000, -31.0000, 0, 128, 0, 2, Kytley_(Armor)
P -376.0000, -156.0000, -31.0000, 0, 128, 0, 2, Wawker_(Armor)
P -289.0000, -219.0000, -31.0000, 0, 128, 0, 2, Bargain_(General)
P 85.0000, -325.0000, -31.0000, 0, 128, 0, 2, Sedaitam_Cournsian_(General)
P 47.0000, -154.0000, -31.0000, 0, 128, 0, 2, Gelson_(Tailor)
P 47.0000, -163.0000, -31.0000, 0, 128, 0, 2, Eiles_(Baking)
P 19.0000, -184.0000, -31.0000, 0, 128, 0, 2, Bailes_(Weapons)
P 19.0000, -204.0000, -31.0000, 0, 128, 0, 2, Akeral_(Baking)
P 38.0000, -209.0000, -31.0000, 0, 128, 0, 2, Ferusal_(Poison)
P -100.0000, -151.0000, -29.0000, 0, 128, 0, 2, Nicoela_(Food)
P -178.0000, -137.0000, -31.0000, 0, 128, 0, 2, Jolan_(Smithing)
P -136.0000, -123.0000, -31.0000, 0, 128, 0, 2, Edcar_(Smithing)
P -181.0000, -166.0000, -31.0000, 0, 128, 0, 2, Kelly_(Smithing)
P -122.0000, -180.0000, -31.0000, 0, 128, 0, 2, Robert_(Smithing)
P -121.0000, -196.0000, -31.0000, 0, 128, 0, 2, Rusty_(Smithing)
P -10.0000, 66.0000, -27.0000, 0, 128, 0, 2, Sneathe_(General)
P -35.0000, 78.0000, -27.0000, 0, 128, 0, 2, Sanderson_(Instruments)
P -198.0000, 99.0000, -27.0000, 0, 128, 0, 2, Narsam_(Weapons)
P -164.0000, 99.0000, -27.0000, 0, 128, 0, 2, Meribeth_(Armor)
P -108.0000, 99.0000, -27.0000, 0, 128, 0, 2, Volan_(Armor)
P -128.0000, 99.0000, -27.0000, 0, 128, 0, 2, Talbard_(Boots)
P 290.0000, -50.0000, -23.0000, 0, 128, 0, 2, Galorano_Daldirick_(Spells)
P 350.0000, -2.0000, -23.0000, 0, 128, 0, 2, Adept_Tirisa_(Spells)
P 350.0000, -13.0000, -23.0000, 0, 128, 0, 2, Adept_Arnthus_(Spells)
P 350.0000, -23.0000, -23.0000, 0, 128, 0, 2, Adept_Paloney_(Spells)
P 316.0000, -31.0000, -23.0000, 0, 128, 0, 2, Galoran_Precon_(Smithing)
P 277.0000, -30.0000, -23.0000, 0, 128, 0, 2, Daekos_Ferinsalo_(Spells)
P 245.0000, -28.0000, -23.0000, 0, 128, 0, 2, Adept_Delf_(General)
P -343.0000, 24.0000, 0.0000, 0, 128, 0, 2, Bartender_Woolf_(Barkeep)
P -427.0000, -30.0000, 0.0000, 0, 128, 0, 2, Innkeep_Alviva_(General)
P -112.0000, -1366.0000, -101.0000, 0, 128, 0, 2, Horiel_(Merchant)
P -65.0000, -1491.0000, -101.0000, 0, 128, 0, 2, Galomas_(Merchant)
P -44.0000, -1175.0000, -49.0000, 0, 128, 0, 2, Eriska_Malosona_(Jewelcrafting)
P -44.0000, -1188.0000, -49.0000, 0, 128, 0, 2, Aeksola_Faerlonifer_(Jewelcrafting)
P 1.0000, -1213.0000, -49.0000, 0, 128, 0, 2, Healian_Saroelitan_(General)
P 569.0000, -1252.0000, -49.0000, 0, 128, 0, 2, Kenral_Siroisl_(Spells)
P 550.0000, -1376.0000, -49.0000, 0, 128, 0, 2, Goesl_Sealsonich_(Spells)
P 517.0000, -1438.0000, -49.0000, 0, 128, 0, 2, Desaliar_Ter`Fediskian_(Spells)
P 295.0000, -1187.0000, -49.0000, 0, 128, 0, 2, Mark_Blotter_(Spells)
P 294.0000, -1175.0000, -49.0000, 0, 128, 0, 2, Loensa_Esorlinal_(Apprentice)
P 238.0000, -1150.0000, -49.0000, 0, 128, 0, 2, Isibat_(Robes)
P 238.0000, -1213.0000, -49.0000, 0, 128, 0, 2, Fitonas_Galaofon_(General)
P -1791.0000, -237.0000, -58.0000, 0, 128, 0, 2, Henriala_Hobialon_(General)
P -1807.0000, -82.0000, -58.0000, 0, 128, 0, 2, Engie_Farworth_(Bags)
P -1805.0000, -57.0000, -58.0000, 0, 128, 0, 2, Bestive_Farworth_(Boxes)
P -1304.0000, -38.0000, -58.0000, 0, 128, 0, 2, Firehand_(Smithing)
P -1255.0000, -12.0000, -58.0000, 0, 128, 0, 2, Jiovfer_(Smithing)
P -1173.0000, -13.0000, -58.0000, 0, 128, 0, 2, Granger_(Merchant)
P -1015.0000, -113.0000, -58.0000, 0, 128, 0, 2, Marchap_(Tinkering)
P -1192.0000, -138.0000, -58.0000, 0, 128, 0, 2, Polson_(Alchemy)
P -1207.0000, -121.0000, -58.0000, 0, 128, 0, 2, Lemsen_(Alchemy)
P -1228.0000, -121.0000, -58.0000, 0, 128, 0, 2, Callowolf_(Alchemy)
P -1249.0000, -320.0000, -109.0000, 0, 128, 0, 2, Teriesl_(Spells)
P -1212.0000, -316.0000, -109.0000, 0, 128, 0, 2, Sarlos_(Spells)
P -1211.0000, -280.0000, -109.0000, 0, 128, 0, 2, Feirus_(Spells)
P -1250.0000, -279.0000, -109.0000, 0, 128, 0, 2, Ealerel_Tidebringer_(Spells)
P -1312.0000, -316.0000, -109.0000, 0, 128, 0, 2, Daveiro_(Spells)
P -1210.0000, -98.0000, -109.0000, 0, 128, 0, 2, Frewin_(Spells)
P -1250.0000, -100.0000, -109.0000, 0, 128, 0, 2, Jales_(Spells)
P -1248.0000, -62.0000, -109.0000, 0, 128, 0, 2, Horsal_(Spells)
P -1209.0000, -61.0000, -109.0000, 0, 128, 0, 2, Herol_(Spells)
P -1272.0000, -60.0000, -109.0000, 0, 128, 0, 2, Hejsua_(Spells)
P -1200.0000, -249.0000, -58.0000, 0, 128, 0, 2, Narmase_(Fletching)
P -1187.0000, -225.0000, -58.0000, 0, 128, 0, 2, Sroj_(Fletching)
P -1704.0000, -56.0000, -58.0000, 0, 128, 0, 2, Daliea_Saeiersa_(Food)
P -1684.0000, -55.0000, -58.0000, 0, 128, 0, 2, Tersie_Falsialoen_(General)
P -1441.0000, 333.0000, -36.0000, 0, 128, 0, 2, Bronklad_Stoneshaper_(Smithing)
P -1417.0000, 391.0000, -55.0000, 0, 128, 0, 2, Hobolor_Peltskinner_(Tailoring)
P -1382.0000, 420.0000, -55.0000, 0, 128, 0, 2, Wayke_Mudhands_(Pottery)
P -1393.0000, 431.0000, -55.0000, 0, 128, 0, 2, Dinoli_Fantalicus_(Barkeep)
P -1384.0000, 441.0000, -55.0000, 0, 128, 0, 2, Hintol_Balkortak_(General)
P -1373.0000, 430.0000, -55.0000, 0, 128, 0, 2, River_Mudhands_(Pottery)
P -1423.0000, 419.0000, -55.0000, 0, 128, 0, 2, Zimloro_Jalobottle_(Alchemy)
P -1623.0000, 386.0000, -59.0000, 0, 128, 0, 2, Falina_Silinilak_(General)
P -1679.0000, 363.0000, -59.0000, 0, 128, 0, 2, Toddor_Stalorok_(Armor)
P -1669.0000, 371.0000, -59.0000, 0, 128, 0, 2, Melina_Stalorok_(Armor)
P -1758.0000, 435.0000, -59.0000, 0, 128, 0, 2, Talida_Furrytoes_(General)
P -1806.0000, 539.0000, -59.0000, 0, 128, 0, 2, Ronlotar_Stumpshade_(Boots)
P -1783.0000, 667.0000, -58.0000, 0, 128, 0, 2, Tall_Joe_(Food)
P -1712.0000, 712.0000, -63.0000, 0, 128, 0, 2, Xerolop_Palostilad_(General)
P -1571.0000, 716.0000, -63.0000, 0, 128, 0, 2, Pilop_Stolarix_(Jewelcrafting)
P -1576.0000, 774.0000, -63.0000, 0, 128, 0, 2, Robbolo_Balashil_(Weapons)
P 424.0000, 76.0000, -23.0000, 128, 128, 128, 2, Priestess_Farinth_(GM_Paladin)
P 424.0000, 38.0000, -23.0000, 128, 128, 128, 2, Priest_Jarson_(GM_Cleric)
P 331.0000, 16.0000, -19.0000, 128, 128, 128, 2, Sesamin_Soulhealer_(GM_Cleric)
P 332.0000, 3.0000, -19.0000, 128, 128, 128, 2, Mandark_Soulhealer_(GM_Paladin)
P -1293.0000, -323.0000, -109.0000, 128, 128, 128, 2, Yargin_the_Traveler_(GM_Ranger)
P -1293.0000, -60.0000, -109.0000, 128, 128, 128, 2, Sarith_(GM_Beastlord)
P -1230.0000, -59.0000, -109.0000, 128, 128, 128, 2, Tarson_(GM_Shaman)
P -1230.0000, -323.0000, -109.0000, 128, 128, 128, 2, Plow_(GM_Druid)
P 102.0000, 355.0000, -31.0000, 128, 128, 128, 2, Garon_Battlehand_(GM_Warrior)
P 128.0000, -1000.0000, -93.0000, 128, 128, 128, 2, Halorik_(GM_Monk)
P 495.0000, -1376.0000, -49.0000, 128, 128, 128, 2, Palomidiar_Allakhaji_(GM_Enchanter)
P 125.0000, -1406.0000, -101.0000, 128, 128, 128, 2, Lamukas_(GM_Wizard)
P 319.0000, -1318.0000, -101.0000, 128, 128, 128, 2, Loewnsaz_(GM_Magician)
P -1704.0000, -278.0000, -58.0000, 128, 128, 128, 2, Frinzel_Heartstinger_(GM_Rogue)
P -760.0000, -137.0000, -67.0000, 0, 0, 0, 2, Hereia_Gealsina
P -693.0000, -54.0000, -72.0000, 0, 0, 0, 2, Slosean
P -740.0000, -45.0000, -72.0000, 0, 0, 0, 2, Rifkin
P -701.0000, -162.0000, -67.0000, 0, 0, 0, 2, Darosn_Sajson
P -544.0000, -170.0000, -32.0000, 0, 0, 0, 2, Tiplo_the_Gardener
P -391.0000, 291.0000, -26.0000, 0, 0, 0, 2, Trade_Commissioner_Henry
P -329.0000, 393.0000, -26.0000, 0, 0, 0, 2, Sarron
P -398.0000, 373.0000, -26.0000, 0, 127, 0, 2, Krizle_Geerlok_(Tinkering)
P -398.0000, 414.0000, -26.0000, 0, 0, 0, 2, Sir_Heaviside
P -385.0000, 434.0000, -26.0000, 0, 0, 0, 2, Sir_Jarsonic
P -304.0000, 452.0000, -26.0000, 0, 127, 0, 2, Barkeep_Veronica_(Barkeep)
P -286.0000, 455.0000, -26.0000, 0, 127, 0, 2, Barkeep_Prastice_(Barkeep)
P -308.0000, 428.0000, -26.0000, 0, 0, 0, 2, Sir_Oron
P -339.0000, 363.0000, -26.0000, 0, 0, 0, 2, Yaron
P -365.0000, 346.0000, -26.0000, 0, 0, 0, 2, Zaores
P -266.0000, 421.0000, 20.0000, 0, 0, 0, 2, Kardon
P 212.0000, -327.0000, -65.0000, 0, 0, 0, 2, Podslan_Xasiani
P 331.0000, -406.0000, -62.0000, 0, 0, 0, 2, Nolram_Gorlsan
P 376.0000, -376.0000, -62.0000, 0, 0, 0, 2, Tsueas_Eadajo
P 160.0000, -390.0000, -31.0000, 0, 0, 0, 2, Chadasl_Dalsoeam
P 24.0000, -331.0000, -31.0000, 0, 0, 0, 2, Aliane_Steelknuckle
P 13.0000, -399.0000, -31.0000, 0, 0, 0, 2, Peqi_Biato
P 28.0000, -205.0000, -31.0000, 0, 0, 0, 2, Lynesia_Eroaaf
P 51.0000, -236.0000, -31.0000, 0, 127, 0, 2, Maththias_(Poison)
P 50.0000, -245.0000, -31.0000, 0, 127, 0, 2, Jolana_(Poison)
P -159.0000, -47.0000, -31.0000, 0, 0, 0, 2, Record_Keeper_Ajar
P -230.0000, -12.0000, -31.0000, 0, 0, 0, 2, Daloran_Glimmerblade
P -226.0000, -25.0000, -31.0000, 0, 0, 0, 2, Mistala_Glimmerblade
P 301.0000, -55.0000, 16.0000, 0, 0, 0, 2, Mother_Ilisial_Fordel
P 158.0000, -254.0000, -31.0000, 0, 0, 0, 2, Nevetra_Utagse
P -427.0000, 39.0000, 0.0000, 0, 0, 0, 2, Hamon
P -336.0000, 18.0000, 0.0000, 0, 0, 0, 2, Barmaid_Adelina
P -303.0000, 35.0000, 0.0000, 0, 0, 0, 2, Lareso_Runsalark
P -303.0000, 53.0000, 0.0000, 0, 0, 0, 2, Eslof_Runsalark
P -306.0000, 74.0000, 0.0000, 0, 0, 0, 2, Milesent
P -340.0000, 73.0000, 0.0000, 0, 0, 0, 2, Lewena
P -310.0000, -59.0000, 0.0000, 0, 0, 0, 2, Sibyl
P -428.0000, -11.0000, 0.0000, 0, 0, 0, 2, Grimthor
P 5.0000, -1149.0000, -49.0000, 0, 127, 0, 2, Samotal_Sedmians_(Food)
P -85.0000, -1408.0000, -49.0000, 0, 0, 0, 2, Innkeep_Restoration
P -67.0000, -1467.0000, -49.0000, 0, 0, 0, 2, Pietro_Guiccini
P 427.0000, -1397.0000, -49.0000, 0, 0, 0, 2, Beriol_Talorakoir
P 461.0000, -1256.0000, -49.0000, 0, 0, 0, 2, Rianna_Birchard
P -1734.0000, -22.0000, -58.0000, 0, 127, 0, 2, Innkeep_Zara_(General)
P -1692.0000, 1.0000, -58.0000, 0, 0, 0, 2, Milasar_Husnalsn
P -1743.0000, 13.0000, -58.0000, 0, 0, 0, 2, Gealian_Husnalsn
P -1745.0000, 52.0000, -58.0000, 0, 0, 0, 2, Easen
P -1459.0000, 314.0000, -36.0000, 0, 127, 0, 2, Hardam_Stonegrinder_(Smithing)
P -1440.0000, 317.0000, -36.0000, 0, 0, 0, 2, Koren_Galund
P -1370.0000, 411.0000, -55.0000, 0, 0, 0, 2, Barlotal
P -1651.0000, 554.0000, -58.0000, 0, 0, 0, 2, Patriarch_Lardalon
P -1625.0000, 575.0000, -62.0000, 0, 0, 0, 2, Townsperson_Sarolix
P -1620.0000, 564.0000, -62.0000, 0, 0, 0, 2, Townsperson_Ranlanos
P -1634.0000, 529.0000, -62.0000, 0, 0, 0, 2, Townsperson_Berolon
P -1673.0000, 539.0000, -62.0000, 0, 0, 0, 2, Townsperson_Pirions
P -1676.0000, 553.0000, -62.0000, 0, 0, 0, 2, Townsperson_Erinol
P -1671.0000, 583.0000, -62.0000, 0, 0, 0, 2, Ambassador_Dordak
P -1663.0000, 585.0000, -62.0000, 0, 0, 0, 2, Ambassador_Balopombo
P -1655.0000, 588.0000, -62.0000, 0, 0, 0, 2, Ambassador_Jerlopodix
P -1813.0000, 505.0000, -59.0000, 0, 0, 0, 2, Skiliar_Hardbrick
P -1815.0000, 491.0000, -59.0000, 0, 0, 0, 2, Albarok
P -1814.0000, 435.0000, -59.0000, 0, 0, 0, 2, Nubert
P -1611.0000, 391.0000, -59.0000, 0, 0, 0, 2, General_Dalinastalarix
P -1328.0000, -443.0000, -58.0000, 0, 0, 0, 2, Dwedrog
P -1313.0000, -449.0000, -58.0000, 0, 127, 0, 2, Danlin_Hildreth_(Bar)
P -1334.0000, -463.0000, -58.0000, 0, 127, 0, 2, Belbie_Hildreth_(Bar)
P -1361.0000, -466.0000, -58.0000, 0, 0, 0, 2, Alexandra
P -1372.0000, -463.0000, -58.0000, 0, 0, 0, 2, Billien
P -1365.0000, -456.0000, -58.0000, 0, 0, 0, 2, Balrolok_Haldarano
P -1359.0000, -455.0000, -58.0000, 0, 0, 0, 1, Sinasi_Daleioa
P -1402.0000, -441.0000, -58.0000, 0, 0, 0, 2, Wiglapo
P -1403.0000, -434.0000, -58.0000, 0, 0, 0, 2, Sol
P -1409.0000, -434.0000, -58.0000, 0, 0, 0, 2, Drolan
P -1411.0000, -440.0000, -58.0000, 0, 0, 0, 2, Kassel
P -1409.0000, -404.0000, -58.0000, 0, 127, 0, 2, Skalopar_(Bar)
P -1164.0000, -371.0000, -58.0000, 0, 0, 0, 2, Daleoas_Brondaer
P -1204.0000, -372.0000, -58.0000, 0, 0, 0, 2, Reasearcher_Relreom
P -1255.0000, -337.0000, -57.0000, 0, 0, 0, 2, Driena_Whistleger
P -1244.0000, -124.0000, -58.0000, 0, 127, 0, 2, Lachlann_(Alchemy)
P -1258.0000, -126.0000, -58.0000, 0, 127, 0, 2, Donaidh_(Alchemy)
P -1268.0000, -125.0000, -58.0000, 0, 127, 0, 2, Torcuil_(Alchemy)
P -1284.0000, -128.0000, -58.0000, 0, 127, 0, 2, Fearchar_(Alchemy)
P -1298.0000, -126.0000, -58.0000, 0, 127, 0, 2, Eairdsigh_(Alchemy)
P -869.0000, -1.0000, -58.0000, 0, 0, 0, 2, Jacob_Thornblade
P -884.0000, 43.0000, -79.0000, 0, 0, 0, 2, Vuri_Pandar_(Casino)
P -866.0000, 45.0000, -79.0000, 0, 0, 0, 2, Tirac_Goldbender_(Casino)
P -846.0000, 35.0000, -79.0000, 0, 0, 0, 2, Gralin_Silvertooth_(Casino)
P -915.0000, 15.0000, -79.0000, 0, 127, 0, 2, Nayda_Winterwind_(Brewing)
P -928.0000, 41.0000, -79.0000, 255, 210, 0, 2, Yeren_Ironback_(Banker)
P -848.0000, -12.0000, -58.0000, 0, 127, 0, 2, Keiya_Oakwood_(Brewing)
P -761.0000, -16.0000, -72.0000, 0, 127, 0, 2, Verte_(Bar)
P -698.0000, -35.0000, -72.0000, 0, 0, 0, 2, Lolara
P -367.0000, 1006.0000, -31.0000, 0, 0, 0, 2, Tronso_Namistail
P -1252.0000, -337.0000, -58.0000, 0, 0, 0, 2, Xerisolap_Whistleger
P -1225.0000, -338.0000, -56.0000, 0, 0, 0, 2, Piersal_Whistleger
P 220.5254, 67.8296, -23.9979, 240, 240, 240, 2, House_of_Fordel
P -734.2481, -124.0068, -67.9979, 240, 240, 240, 2, Common_Grounds
