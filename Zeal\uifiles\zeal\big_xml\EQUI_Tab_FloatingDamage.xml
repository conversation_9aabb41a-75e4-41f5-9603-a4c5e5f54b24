<?xml version="1.0" encoding="us-ascii"?>
<XML ID="EQInterfaceDefinitionLanguage">
  <Schema xmlns="EverQuestData" xmlns:dt="EverQuestDataTypes" />

  <Button item="Zeal_FloatingDamage">
    <ScreenID>Zeal_FloatingDamage</ScreenID>
    <RelativePosition>true</RelativePosition>
    <Location>
      <X>20</X>
      <Y>48</Y>
    </Location>
    <Size>
      <CX>300</CX>
      <CY>40</CY>
    </Size>
    <Style_VScroll>false</Style_VScroll>
    <Style_HScroll>false</Style_HScroll>
    <Style_Transparent>false</Style_Transparent>
    <Style_Checkbox>true</Style_Checkbox>
    <TooltipReference>Toggles floating combat damage numbers</TooltipReference>
    <Text>Enabled</Text>
    <TextColor>
      <R>255</R>
      <G>255</G>
      <B>255</B>
    </TextColor>
    <ButtonDrawTemplate>
      <Normal>A_BtnNormal</Normal>
      <Pressed>A_BtnPressed</Pressed>
      <Flyby>A_BtnFlyby</Flyby>
      <Disabled>A_BtnDisabled</Disabled>
      <PressedFlyby>A_BtnPressedFlyby</PressedFlyby>
    </ButtonDrawTemplate>
  </Button>
  <Button item="Zeal_FloatingSelf">
    <ScreenID>Zeal_FloatingSelf</ScreenID>
    <RelativePosition>true</RelativePosition>
    <Location>
      <X>20</X>
      <Y>92</Y>
    </Location>
    <Size>
      <CX>300</CX>
      <CY>40</CY>
    </Size>
    <Style_VScroll>false</Style_VScroll>
    <Style_HScroll>false</Style_HScroll>
    <Style_Transparent>false</Style_Transparent>
    <Style_Checkbox>true</Style_Checkbox>
    <TooltipReference>Toggles display of damage from self</TooltipReference>
    <Text>Show from me</Text>
    <TextColor>
      <R>255</R>
      <G>255</G>
      <B>255</B>
    </TextColor>
    <ButtonDrawTemplate>
      <Normal>A_BtnNormal</Normal>
      <Pressed>A_BtnPressed</Pressed>
      <Flyby>A_BtnFlyby</Flyby>
      <Disabled>A_BtnDisabled</Disabled>
      <PressedFlyby>A_BtnPressedFlyby</PressedFlyby>
    </ButtonDrawTemplate>
  </Button>
  <Button item="Zeal_FloatingPets">
    <ScreenID>Zeal_FloatingPets</ScreenID>
    <RelativePosition>true</RelativePosition>
    <Location>
      <X>20</X>
      <Y>136</Y>
    </Location>
    <Size>
      <CX>300</CX>
      <CY>40</CY>
    </Size>
    <Style_VScroll>false</Style_VScroll>
    <Style_HScroll>false</Style_HScroll>
    <Style_Transparent>false</Style_Transparent>
    <Style_Checkbox>true</Style_Checkbox>
    <TooltipReference>Toggles display of damage from all pets</TooltipReference>
    <Text>Show from pets</Text>
    <TextColor>
      <R>255</R>
      <G>255</G>
      <B>255</B>
    </TextColor>
    <ButtonDrawTemplate>
      <Normal>A_BtnNormal</Normal>
      <Pressed>A_BtnPressed</Pressed>
      <Flyby>A_BtnFlyby</Flyby>
      <Disabled>A_BtnDisabled</Disabled>
      <PressedFlyby>A_BtnPressedFlyby</PressedFlyby>
    </ButtonDrawTemplate>
  </Button>
  <Button item="Zeal_FloatingOthers">
    <ScreenID>Zeal_FloatingOthers</ScreenID>
    <RelativePosition>true</RelativePosition>
    <Location>
      <X>20</X>
      <Y>180</Y>
    </Location>
    <Size>
      <CX>300</CX>
      <CY>40</CY>
    </Size>
    <Style_VScroll>false</Style_VScroll>
    <Style_HScroll>false</Style_HScroll>
    <Style_Transparent>false</Style_Transparent>
    <Style_Checkbox>true</Style_Checkbox>
    <TooltipReference>Toggles display of damage from other players</TooltipReference>
    <Text>Show from other PCs</Text>
    <TextColor>
      <R>255</R>
      <G>255</G>
      <B>255</B>
    </TextColor>
    <ButtonDrawTemplate>
      <Normal>A_BtnNormal</Normal>
      <Pressed>A_BtnPressed</Pressed>
      <Flyby>A_BtnFlyby</Flyby>
      <Disabled>A_BtnDisabled</Disabled>
      <PressedFlyby>A_BtnPressedFlyby</PressedFlyby>
    </ButtonDrawTemplate>
  </Button>
  <Button item="Zeal_FloatingNpcs">
    <ScreenID>Zeal_FloatingNpcs</ScreenID>
    <RelativePosition>true</RelativePosition>
    <Location>
      <X>20</X>
      <Y>224</Y>
    </Location>
    <Size>
      <CX>300</CX>
      <CY>40</CY>
    </Size>
    <Style_VScroll>false</Style_VScroll>
    <Style_HScroll>false</Style_HScroll>
    <Style_Transparent>false</Style_Transparent>
    <Style_Checkbox>true</Style_Checkbox>
    <TooltipReference>Toggles display of damage from NPCs</TooltipReference>
    <Text>Show from NPCs</Text>
    <TextColor>
      <R>255</R>
      <G>255</G>
      <B>255</B>
    </TextColor>
    <ButtonDrawTemplate>
      <Normal>A_BtnNormal</Normal>
      <Pressed>A_BtnPressed</Pressed>
      <Flyby>A_BtnFlyby</Flyby>
      <Disabled>A_BtnDisabled</Disabled>
      <PressedFlyby>A_BtnPressedFlyby</PressedFlyby>
    </ButtonDrawTemplate>
  </Button>
  <Button item="Zeal_FloatingHpUpdates">
    <ScreenID>Zeal_FloatingHpUpdates</ScreenID>
    <RelativePosition>true</RelativePosition>
    <Location>
      <X>20</X>
      <Y>268</Y>
    </Location>
    <Size>
      <CX>300</CX>
      <CY>40</CY>
    </Size>
    <Style_VScroll>false</Style_VScroll>
    <Style_HScroll>false</Style_HScroll>
    <Style_Transparent>false</Style_Transparent>
    <Style_Checkbox>true</Style_Checkbox>
    <TooltipReference>Toggles display of HP updates</TooltipReference>
    <Text>Show HP Updates</Text>
    <TextColor>
      <R>255</R>
      <G>255</G>
      <B>255</B>
    </TextColor>
    <ButtonDrawTemplate>
      <Normal>A_BtnNormal</Normal>
      <Pressed>A_BtnPressed</Pressed>
      <Flyby>A_BtnFlyby</Flyby>
      <Disabled>A_BtnDisabled</Disabled>
      <PressedFlyby>A_BtnPressedFlyby</PressedFlyby>
    </ButtonDrawTemplate>
  </Button>
  <Button item="Zeal_FloatingMelee">
    <ScreenID>Zeal_FloatingMelee</ScreenID>
    <RelativePosition>true</RelativePosition>
    <Location>
      <X>20</X>
      <Y>312</Y>
    </Location>
    <Size>
      <CX>300</CX>
      <CY>40</CY>
    </Size>
    <Style_VScroll>false</Style_VScroll>
    <Style_HScroll>false</Style_HScroll>
    <Style_Transparent>false</Style_Transparent>
    <Style_Checkbox>true</Style_Checkbox>
    <TooltipReference>Toggles display of melee damage from players</TooltipReference>
    <Text>Show from melee</Text>
    <TextColor>
      <R>255</R>
      <G>255</G>
      <B>255</B>
    </TextColor>
    <ButtonDrawTemplate>
      <Normal>A_BtnNormal</Normal>
      <Pressed>A_BtnPressed</Pressed>
      <Flyby>A_BtnFlyby</Flyby>
      <Disabled>A_BtnDisabled</Disabled>
      <PressedFlyby>A_BtnPressedFlyby</PressedFlyby>
    </ButtonDrawTemplate>
  </Button>
  <Button item="Zeal_FloatingSpells">
    <ScreenID>Zeal_FloatingSpells</ScreenID>
    <RelativePosition>true</RelativePosition>
    <Location>
      <X>20</X>
      <Y>356</Y>
    </Location>
    <Size>
      <CX>300</CX>
      <CY>40</CY>
    </Size>
    <Style_VScroll>false</Style_VScroll>
    <Style_HScroll>false</Style_HScroll>
    <Style_Transparent>false</Style_Transparent>
    <Style_Checkbox>true</Style_Checkbox>
    <TooltipReference>Toggles display of spell damage from players</TooltipReference>
    <Text>Show from spells</Text>
    <TextColor>
      <R>255</R>
      <G>255</G>
      <B>255</B>
    </TextColor>
    <ButtonDrawTemplate>
      <Normal>A_BtnNormal</Normal>
      <Pressed>A_BtnPressed</Pressed>
      <Flyby>A_BtnFlyby</Flyby>
      <Disabled>A_BtnDisabled</Disabled>
      <PressedFlyby>A_BtnPressedFlyby</PressedFlyby>
    </ButtonDrawTemplate>
  </Button>
    
  <Button item="Zeal_FloatingSpellIcons">
    <ScreenID>Zeal_FloatingSpellIcons</ScreenID>
    <RelativePosition>true</RelativePosition>
    <Location>
      <X>20</X>
      <Y>400</Y>
    </Location>
    <Size>
      <CX>300</CX>
      <CY>40</CY>
    </Size>
    <Style_VScroll>false</Style_VScroll>
    <Style_HScroll>false</Style_HScroll>
    <Style_Transparent>false</Style_Transparent>
    <Style_Checkbox>true</Style_Checkbox>
    <TooltipReference>Toggles floating spell icons</TooltipReference>
    <Text>Spell Icons</Text>
    <TextColor>
      <R>255</R>
      <G>255</G>
      <B>255</B>
    </TextColor>
    <ButtonDrawTemplate>
      <Normal>A_BtnNormal</Normal>
      <Pressed>A_BtnPressed</Pressed>
      <Flyby>A_BtnFlyby</Flyby>
      <Disabled>A_BtnDisabled</Disabled>
      <PressedFlyby>A_BtnPressedFlyby</PressedFlyby>
    </ButtonDrawTemplate>
  </Button>
  <Button item="Zeal_FloatingHideWithGui">
    <ScreenID>Zeal_FloatingHideWithGui</ScreenID>
    <RelativePosition>true</RelativePosition>
    <Location>
      <X>20</X>
      <Y>444</Y>
    </Location>
    <Size>
      <CX>300</CX>
      <CY>40</CY>
    </Size>
    <Style_VScroll>false</Style_VScroll>
    <Style_HScroll>false</Style_HScroll>
    <Style_Transparent>false</Style_Transparent>
    <Style_Checkbox>true</Style_Checkbox>
    <TooltipReference>Hides FCD when GUI is hidden (Default fonts are always hidden)</TooltipReference>
    <Text>Hide with GUI</Text>
    <TextColor>
      <R>255</R>
      <G>255</G>
      <B>255</B>
    </TextColor>
    <ButtonDrawTemplate>
      <Normal>A_BtnNormal</Normal>
      <Pressed>A_BtnPressed</Pressed>
      <Flyby>A_BtnFlyby</Flyby>
      <Disabled>A_BtnDisabled</Disabled>
      <PressedFlyby>A_BtnPressedFlyby</PressedFlyby>
    </ButtonDrawTemplate>
  </Button>

  
  <Label item="Zeal_FloatingFont_Label">
    <ScreenID>Zeal_FloatingFont_Label</ScreenID>
    <RelativePosition>true</RelativePosition>
    <Location>
      <X>400</X>
      <Y>48</Y>
    </Location>
    <Size>
      <CX>300</CX>
      <CY>28</CY>
    </Size>
    <Text>Font</Text>
    <TextColor>
      <R>255</R>
      <G>255</G>
      <B>255</B>
    </TextColor>
    <NoWrap>true</NoWrap>
    <AlignCenter>false</AlignCenter>
    <AlignRight>false</AlignRight>
  </Label>
  <Combobox item="Zeal_FloatingFont_Combobox">
    <ScreenID>Zeal_FloatingFont_Combobox</ScreenID>
    <DrawTemplate>WDT_Inner</DrawTemplate>
    <Location>
      <X>400</X>
      <Y>88</Y>
    </Location>
    <Size>
      <CX>300</CX>
      <CY>48</CY>
    </Size>
    <TextColor>
      <R>255</R>
      <G>255</G>
      <B>255</B>
    </TextColor>
    <ListHeight>70</ListHeight>
    <Button>BDT_Combo</Button>
    <Style_Border>true</Style_Border>
    <Choices>default</Choices>
  </Combobox>
  
  <Label item="Zeal_FloatingBigHit_Label">
    <ScreenID>Zeal_FloatingBigHit_Label</ScreenID>
    <RelativePosition>true</RelativePosition>
    <Location>
      <X>400</X>
      <Y>140</Y>
    </Location>
    <Size>
      <CX>300</CX>
      <CY>28</CY>
    </Size>
    <Text>Big hit threshold value</Text>
    <TextColor>
      <R>255</R>
      <G>255</G>
      <B>255</B>
    </TextColor>
    <NoWrap>true</NoWrap>
    <AlignCenter>false</AlignCenter>
    <AlignRight>false</AlignRight>
  </Label>
  <Slider item="Zeal_FloatingBigHit_Slider">
    <ScreenID>Zeal_FloatingBigHit_Slider</ScreenID>
    <RelativePosition>true</RelativePosition>
    <Location>
      <X>394</X>
      <Y>180</Y>
    </Location>
    <Size>
      <CX>200</CX>
      <CY>32</CY>
    </Size>
    <SliderArt>SDT_DefSlider</SliderArt>
  </Slider>
  <Label item="Zeal_FloatingBigHit_Value">
    <ScreenID>Zeal_FloatingBigHit_Value</ScreenID>
    <RelativePosition>true</RelativePosition>
    <Location>
      <X>594</X>
      <Y>180</Y>
    </Location>
    <Size>
      <CX>100</CX>
      <CY>32</CY>
    </Size>
    <Text>0</Text>
    <TextColor>
      <R>255</R>
      <G>255</G>
      <B>255</B>
    </TextColor>
    <NoWrap>true</NoWrap>
    <AlignCenter>false</AlignCenter>
    <AlignRight>true</AlignRight>
  </Label>
  <Page item="Tab_FloatingDamage">
    <ScreenID>Tab_FloatingDamage</ScreenID>
    <RelativePosition>true</RelativePosition>
    
    <Style_VScroll>true</Style_VScroll>
    <Style_HScroll>false</Style_HScroll>
    <Style_Transparent>false</Style_Transparent>
    <DrawTemplate>WDT_Def</DrawTemplate>
    <TabText>Floating Combat</TabText>
    <TabTextColor>
      <R>255</R>
      <G>255</G>
      <B>255</B>
    </TabTextColor>
    <TabTextActiveColor>
      <R>255</R>
      <G>255</G>
      <B>0</B>
    </TabTextActiveColor>
    <Style_Sizable>true</Style_Sizable>
    <Pieces>Zeal_FloatingDamage</Pieces>
    <Pieces>Zeal_FloatingSelf</Pieces>
    <Pieces>Zeal_FloatingPets</Pieces>
    <Pieces>Zeal_FloatingOthers</Pieces>
    <Pieces>Zeal_FloatingNpcs</Pieces>
    <Pieces>Zeal_FloatingHpUpdates</Pieces>
    <Pieces>Zeal_FloatingMelee</Pieces>
    <Pieces>Zeal_FloatingSpells</Pieces>
    <Pieces>Zeal_FloatingSpellIcons</Pieces>
    <Pieces>Zeal_FloatingHideWithGui</Pieces>
    <Pieces>Zeal_FloatingBigHit_Label</Pieces>
    <Pieces>Zeal_FloatingBigHit_Slider</Pieces>
    <Pieces>Zeal_FloatingBigHit_Value</Pieces>
    <Pieces>Zeal_FloatingFont_Label</Pieces>
    <Pieces>Zeal_FloatingFont_Combobox</Pieces>
    <Location>
      <X>0</X>
      <Y>44</Y>
    </Location>
    <Size>
      <CX>760</CX>
      <CY>678</CY>
    </Size>
  </Page>
  </XML>