P 4516.0000, -1784.0000, 82.1213, 255, 0, 0, 3, to_<PERSON><PERSON><PERSON><PERSON>`s_<PERSON><PERSON>
P 2407.0000, 4169.0000, 76.5080, 255, 0, 0, 3, to_<PERSON><PERSON><PERSON>_Vie
P -1137.0000, -5223.0000, 154.3259, 255, 0, 0, 3, to_The_Field_of_Bone
P -2651.0000, 4301.0000, -4.8518, 255, 0, 0, 3, to_<PERSON><PERSON>a_Vie
P -3135.0000, -3031.0000, 0.1259, 255, 0, 0, 3, to_<PERSON><PERSON>lis_East
P -2945.0000, -2761.0000, 0.0000, 255, 0, 0, 2, Succor
P -2947.0000, -2421.0000, 40.3446, 0, 128, 0, 2, <PERSON><PERSON><PERSON>_(<PERSON>)
P -2907.0000, -2461.0000, 40.3446, 0, 128, 0, 2, Mi<PERSON>_(<PERSON>)
P -2723.0000, -2641.0000, 39.0009, 0, 128, 0, 2, Go<PERSON><PERSON>_(Armor)
P -2812.0000, -2706.0000, 40.3134, 0, 128, 0, 2, By<PERSON>_(Weapons)
P 1500.0000, 1250.0000, 0.0000, 255, 255, 255, 2, <PERSON><PERSON>,_The_Froglok_City
P 1369.1247, -1399.3107, -7.1891, 0, 0, 0, 2, Grik_the_Ex<PERSON>
P -1107.0000, -413.0000, -158.0000, 127, 64, 0, 2, Black<PERSON>_(<PERSON>,<PERSON><PERSON>)
P -2856.0000, 2170.0000, 91.0000, 127, 64, 0, 2, <PERSON><PERSON>_(<PERSON>,<PERSON><PERSON>)
P 1903.0000, -703.0000, -19.0000, 127, 64, 0, 2, Blackwing_(Hunter,Roam)
P 1837.0000, -3216.0000, 181.0000, 127, 64, 0, 2, Blackwing_(Hunter,Roam)
P 1168.0000, -3472.0000, 0.0000, 127, 64, 0, 2, Bleeder_(Hunter,Roam)
P 2037.0000, 1503.0000, -217.0000, 127, 64, 0, 2, Bloodgorge_(Hunter,Roam)
P -2322.0000, 1955.0000, -118.0000, 127, 64, 0, 2, Bloodgorge_(Hunter,Roam)
P 1249.0000, -2941.0000, 17.0000, 127, 64, 0, 2, Bloodskull_(Hunter,Roam)
P -1029.0000, 2663.0000, -161.0000, 127, 64, 0, 2, Bloodvein_(Hunter,Roam)
P -1809.0000, -1174.0000, 60.0000, 127, 64, 0, 2, Bloodvein_(Hunter,Roam)
P 2793.0000, -12.0000, -16.0000, 127, 64, 0, 2, Bulsgor_(Hunter,Roam)
P 1751.0000, -3670.0000, 132.0000, 127, 64, 0, 2, Bulsgor_(Hunter,Roam)
P -1091.0000, 649.0000, 122.0000, 127, 64, 0, 2, Crackclaw_(Hunter,Roam)
P 1778.0000, -1716.0000, -155.0000, 127, 64, 0, 2, Crackclaw_(Hunter,Roam)
P 2032.0000, -1217.0000, 96.0000, 127, 64, 0, 2, Crookspine_(Hunter,Roam)
P -831.0000, 3308.0000, -127.0000, 127, 64, 0, 2, Deadeye_(Hunter,Roam)
P -1420.0000, 744.0000, 0.0000, 127, 64, 0, 2, Deadeye_(Hunter,Roam)
P 1589.0000, -2108.0000, 82.0000, 127, 64, 0, 2, Dred_(Hunter,Roam)
P 2183.0000, 791.0000, -160.0000, 127, 64, 0, 2, Dreesix_Ghoultounge_(Hunter,Roam)
P -1903.0000, -865.0000, 199.0000, 127, 64, 0, 2, Dreesix_Ghoultounge_(Hunter,Roam)
P -694.0000, 1068.0000, 48.0000, 127, 64, 0, 2, Ebon_Bloodrose_(Hunter,Roam)
P -1747.0000, 3287.0000, -196.0000, 127, 64, 0, 2, Ebon_Bloodrose_(Hunter,Roam)
P -1447.0000, -978.0000, -278.0000, 127, 64, 0, 2, Fakraa_the_Foresaken_(Hunter,Roam)
P -3256.0000, 2388.0000, 265.0000, 127, 64, 0, 2, Fakraa_the_Foresaken_(Hunter,Roam)
P -1420.0000, -661.0000, -109.0000, 127, 64, 0, 2, Fangor_(Hunter,Roam)
P -2985.0000, 2575.0000, 125.0000, 127, 64, 0, 2, Fangor_(Hunter,Roam)
P 2029.0000, 1276.0000, -228.0000, 127, 64, 0, 2, Farik_the_Vile_(Hunter,Roam)
P -1442.0000, 2863.0000, 0.0000, 127, 64, 0, 2, Farik_the_Vile_(Hunter,Roam)
P 2270.0000, -3523.0000, 0.0000, 127, 64, 0, 2, Fisherman_Grik_(Hunter,Roam)
P -2584.0000, 1665.0000, 131.0000, 127, 64, 0, 2, Frayk_(Hunter,Roam)
P 2937.0000, 990.0000, -83.0000, 127, 64, 0, 2, Frayk_(Hunter,Roam)
P -1920.0000, 118.0000, 313.0000, 127, 64, 0, 2, Froszik_the_Impaler_(Hunter,Roam)
P 1852.0000, -2526.0000, 307.0000, 127, 64, 0, 2, Galeech_(Hunter,Roam)
P 2062.0000, -134.0000, -20.0000, 127, 64, 0, 2, Galeech_(Hunter,Roam)
P 2697.0000, -2062.0000, 0.0000, 127, 64, 0, 2, Gluttik_(Hunter,Roam)
P 1667.0000, -958.0000, 123.0000, 127, 64, 0, 2, Gorge_(Hunter,Roam)
P -1483.0000, 423.0000, -82.0000, 127, 64, 0, 2, Grimewurm_(Hunter,Roam)
P -1454.0000, 3510.0000, 90.0000, 127, 64, 0, 2, Grimewurm_(Hunter,Roam)
P -1126.0000, 897.0000, 116.0000, 127, 64, 0, 2, Grizshnok_(Hunter,Roam)
P 2133.0000, 1084.0000, -182.0000, 127, 64, 0, 2, Grizshnok_(Hunter,Roam)
P 1962.0000, 1774.0000, -236.0000, 127, 64, 0, 2, Heartblood_Fern_(Hunter,Roam)
P -1453.0000, 1683.0000, 183.0000, 127, 64, 0, 2, Heartblood_Fern_(Hunter,Roam)
P 1937.0000, -351.0000, 171.0000, 127, 64, 0, 2, Horkak_the_Dead_(Hunter,Roam)
P 1630.0000, -1481.0000, -166.0000, 127, 64, 0, 2, Ichorspike_(Hunter,Roam)
P 1665.0000, -1866.0000, -171.0000, 127, 64, 0, 2, Old_Hangman_(Hunter,Roam)
P -2651.0000, 932.0000, 86.0000, 127, 64, 0, 2, Old_Hangman_(Hunter,Roam)
P 2231.0000, -1616.0000, 45.0000, 127, 64, 0, 2, Scalek_(Hunter,Roam)
P -2101.0000, -705.0000, 77.0000, 127, 64, 0, 2, Soblohg_(Hunter,Roam)
P -2908.0000, 1990.0000, -178.0000, 127, 64, 0, 2, Soblohg_(Hunter,Roam)
P 1495.0000, -1940.0000, -181.0000, 127, 64, 0, 2, Thirgus_(Hunter,Roam)
P 2763.0000, -1018.0000, 122.0000, 127, 64, 0, 2, Torgis_(Hunter,Roam)
P -1056.0000, 1462.0000, 47.0000, 127, 64, 0, 2, Two_Tails_(Hunter,Roam)
P 2909.0000, 638.0000, 238.0000, 127, 64, 0, 2, Two_Tails_(Hunter,Roam)
P -1292.0000, 3460.0000, -118.0000, 127, 64, 0, 2, Venomwing_(Hunter,Roam)
P -2070.0000, 1235.0000, 0.0000, 127, 64, 0, 2, Venomwing_(Hunter,Roam)
P 2673.0000, -1849.0000, 312.0000, 127, 64, 0, 2, Vissix_(Hunter,Roam)
P -2104.0000, 599.0000, -85.0000, 127, 64, 0, 2, Weeping_Mantrap_(Hunter,Roam)
P 2658.0000, 1159.0000, 257.0000, 127, 64, 0, 2, Weeping_Mantrap_(Hunter,Roam)
P 2682.0000, -1295.0000, 150.0000, 127, 64, 0, 2, Woggir_(Hunter,Roam)
P -3433.0000, 1695.0000, 0.0000, 127, 64, 0, 2, Zagran_the_Mad_(Hunter,Roam)
P -909.3291, -4252.9184, 62.5574, 0, 0, 0, 2, Calgerene_Mythmoor
P -1594.5767, -3801.4423, 70.2936, 0, 127, 127, 2, Ssessthrass_(Task_Master)
P 3582.8078, -1153.2327, 180.6192, 0, 0, 0, 2, Zaiban_Zellenshar
P 1755.4168, 110.4288, -16.3008, 0, 0, 0, 2, Ulump_Pujluk
P 1780.1143, 255.0930, -9.2085, 0, 0, 0, 2, Dugroz
P 1254.0922, 780.3460, 25.9027, 0, 0, 0, 2, Kaggy_Krup
P 1411.9622, 1261.5352, 26.0758, 0, 0, 240, 1, GS:_Earthenware_Bowl
P 1779.4971, 1479.9353, 25.5226, 0, 0, 0, 2, Lokin_the_Wise
P 2048.8959, 1551.4031, -13.2109, 0, 0, 0, 2, Muirkiaz_Selishshen
P 2548.7177, 3286.9750, 18.8161, 0, 0, 0, 2, Gkoroza_Scaletine
P -2586.8295, -1820.1754, -8.1364, 0, 0, 0, 2, Kellboa_Skyspeed
P -3015.0000, 570.0000, -16.0000, 0, 0, 240, 1, GS:_Piece_of_a_Medallion
P -2326.0000, 3658.0000, 37.0000, 0, 0, 0, 2, Scout_Eyru
