<?xml version="1.0" encoding="us-ascii"?>
<XML ID="EQInterfaceDefinitionLanguage">
  <Schema xmlns="EverQuestData" xmlns:dt="EverQuestDataTypes" />

  <Button item="Zeal_TargetRing">
    <ScreenID>Zeal_TargetRing</ScreenID>
    <RelativePosition>true</RelativePosition>
    <Location>
      <X>11</X>
      <Y>10</Y>
    </Location>
    <Size>
      <CX>150</CX>
      <CY>20</CY>
    </Size>
    <Style_VScroll>false</Style_VScroll>
    <Style_HScroll>false</Style_HScroll>
    <Style_Transparent>false</Style_Transparent>
    <Style_Checkbox>true</Style_Checkbox>
    <TooltipReference>Toggles Target Rings</TooltipReference>
    <Text>Enabled</Text>
    <TextColor>
      <R>255</R>
      <G>255</G>
      <B>255</B>
    </TextColor>
    <ButtonDrawTemplate>
      <Normal>A_BtnNormal</Normal>
      <Pressed>A_BtnPressed</Pressed>
      <Flyby>A_BtnFlyby</Flyby>
      <Disabled>A_BtnDisabled</Disabled>
      <PressedFlyby>A_BtnPressedFlyby</PressedFlyby>
    </ButtonDrawTemplate>
  </Button>
  <Button item="Zeal_TargetRingDisableForSelf">
    <ScreenID>Zeal_TargetRingDisableForSelf</ScreenID>
    <RelativePosition>true</RelativePosition>
    <Location>
      <X>11</X>
      <Y>32</Y>
    </Location>
    <Size>
      <CX>150</CX>
      <CY>20</CY>
    </Size>
    <Style_VScroll>false</Style_VScroll>
    <Style_HScroll>false</Style_HScroll>
    <Style_Transparent>false</Style_Transparent>
    <Style_Checkbox>true</Style_Checkbox>
    <TooltipReference>Disables target ring when self-targeting</TooltipReference>
    <Text>Disable for self</Text>
    <TextColor>
      <R>255</R>
      <G>255</G>
      <B>255</B>
    </TextColor>
    <ButtonDrawTemplate>
      <Normal>A_BtnNormal</Normal>
      <Pressed>A_BtnPressed</Pressed>
      <Flyby>A_BtnFlyby</Flyby>
      <Disabled>A_BtnDisabled</Disabled>
      <PressedFlyby>A_BtnPressedFlyby</PressedFlyby>
    </ButtonDrawTemplate>
  </Button>
  <Button item="Zeal_TargetRingAttackIndicator">
    <ScreenID>Zeal_TargetRingAttackIndicator</ScreenID>
    <RelativePosition>true</RelativePosition>
    <Location>
      <X>11</X>
      <Y>54</Y>
    </Location>
    <Size>
      <CX>150</CX>
      <CY>20</CY>
    </Size>
    <Style_VScroll>false</Style_VScroll>
    <Style_HScroll>false</Style_HScroll>
    <Style_Transparent>false</Style_Transparent>
    <Style_Checkbox>true</Style_Checkbox>
    <TooltipReference>Toggles Target Rings Attack Indicator</TooltipReference>
    <Text>Attack Indicator</Text>
    <TextColor>
      <R>255</R>
      <G>255</G>
      <B>255</B>
    </TextColor>
    <ButtonDrawTemplate>
      <Normal>A_BtnNormal</Normal>
      <Pressed>A_BtnPressed</Pressed>
      <Flyby>A_BtnFlyby</Flyby>
      <Disabled>A_BtnDisabled</Disabled>
      <PressedFlyby>A_BtnPressedFlyby</PressedFlyby>
    </ButtonDrawTemplate>
  </Button>
  <Button item="Zeal_TargetRingForward">
    <ScreenID>Zeal_TargetRingForward</ScreenID>
    <RelativePosition>true</RelativePosition>
    <Location>
      <X>11</X>
      <Y>76</Y>
    </Location>
    <Size>
      <CX>150</CX>
      <CY>20</CY>
    </Size>
    <Style_VScroll>false</Style_VScroll>
    <Style_HScroll>false</Style_HScroll>
    <Style_Transparent>false</Style_Transparent>
    <Style_Checkbox>true</Style_Checkbox>
    <TooltipReference>Always rotate the target ring the same direction as the targets heading</TooltipReference>
    <Text>Follow Heading</Text>
    <TextColor>
      <R>255</R>
      <G>255</G>
      <B>255</B>
    </TextColor>
    <ButtonDrawTemplate>
      <Normal>A_BtnNormal</Normal>
      <Pressed>A_BtnPressed</Pressed>
      <Flyby>A_BtnFlyby</Flyby>
      <Disabled>A_BtnDisabled</Disabled>
      <PressedFlyby>A_BtnPressedFlyby</PressedFlyby>
    </ButtonDrawTemplate>
  </Button>
  <Button item="Zeal_TargetRingCone">
    <ScreenID>Zeal_TargetRingCone</ScreenID>
    <RelativePosition>true</RelativePosition>
    <Location>
      <X>11</X>
      <Y>98</Y>
    </Location>
    <Size>
      <CX>150</CX>
      <CY>20</CY>
    </Size>
    <Style_VScroll>false</Style_VScroll>
    <Style_HScroll>false</Style_HScroll>
    <Style_Transparent>false</Style_Transparent>
    <Style_Checkbox>true</Style_Checkbox>
    <TooltipReference>Renders the ring as a cone</TooltipReference>
    <Text>Cone</Text>
    <TextColor>
      <R>255</R>
      <G>255</G>
      <B>255</B>
    </TextColor>
    <ButtonDrawTemplate>
      <Normal>A_BtnNormal</Normal>
      <Pressed>A_BtnPressed</Pressed>
      <Flyby>A_BtnFlyby</Flyby>
      <Disabled>A_BtnDisabled</Disabled>
      <PressedFlyby>A_BtnPressedFlyby</PressedFlyby>
    </ButtonDrawTemplate>
  </Button>
  <Button item="Zeal_TargetRingColor">
    <ScreenID>Zeal_TargetRingColor</ScreenID>
    <RelativePosition>true</RelativePosition>
    <Location>
      <X>11</X>
      <Y>120</Y>
    </Location>
    <Size>
      <CX>150</CX>
      <CY>20</CY>
    </Size>
    <Style_VScroll>false</Style_VScroll>
    <Style_HScroll>false</Style_HScroll>
    <Style_Transparent>false</Style_Transparent>
    <Style_Checkbox>true</Style_Checkbox>
    <TooltipReference>Renders Target ring color using Target Color</TooltipReference>
    <Text>Target Color</Text>
    <TextColor>
      <R>255</R>
      <G>255</G>
      <B>255</B>
    </TextColor>
    <ButtonDrawTemplate>
      <Normal>A_BtnNormal</Normal>
      <Pressed>A_BtnPressed</Pressed>
      <Flyby>A_BtnFlyby</Flyby>
      <Disabled>A_BtnDisabled</Disabled>
      <PressedFlyby>A_BtnPressedFlyby</PressedFlyby>
    </ButtonDrawTemplate>
  </Button>
  <Button item="Zeal_TargetRingHideWithGui">
    <ScreenID>Zeal_TargetRingHideWithGui</ScreenID>
    <RelativePosition>true</RelativePosition>
    <Location>
      <X>11</X>
      <Y>142</Y>
    </Location>
    <Size>
      <CX>150</CX>
      <CY>20</CY>
    </Size>
    <Style_VScroll>false</Style_VScroll>
    <Style_HScroll>false</Style_HScroll>
    <Style_Transparent>false</Style_Transparent>
    <Style_Checkbox>true</Style_Checkbox>
    <TooltipReference>Hides target ring when GUI is hidden</TooltipReference>
    <Text>Hide with Gui</Text>
    <TextColor>
      <R>255</R>
      <G>255</G>
      <B>255</B>
    </TextColor>
    <ButtonDrawTemplate>
      <Normal>A_BtnNormal</Normal>
      <Pressed>A_BtnPressed</Pressed>
      <Flyby>A_BtnFlyby</Flyby>
      <Disabled>A_BtnDisabled</Disabled>
      <PressedFlyby>A_BtnPressedFlyby</PressedFlyby>
    </ButtonDrawTemplate>
  </Button>
  <!-- Zeal Target Ring Fill slider -->
  <Label item="Zeal_TargetRingFill_Label">
    <ScreenID>Zeal_TargetRingFill_Label</ScreenID>
    <RelativePosition>true</RelativePosition>
    <Location>
      <X>213</X>
      <Y>9</Y>
    </Location>
    <Size>
      <CX>150</CX>
      <CY>14</CY>
    </Size>
    <Text>Inner Fill</Text>
    <TextColor>
      <R>255</R>
      <G>255</G>
      <B>255</B>
    </TextColor>
    <NoWrap>true</NoWrap>
    <AlignCenter>false</AlignCenter>
    <AlignRight>false</AlignRight>
  </Label>
  <Slider item="Zeal_TargetRingFill_Slider">
    <ScreenID>Zeal_TargetRingFill_Slider</ScreenID>
    <RelativePosition>true</RelativePosition>
    <Location>
      <X>213</X>
      <Y>29</Y>
    </Location>
    <Size>
      <CX>100</CX>
      <CY>16</CY>
    </Size>
    <SliderArt>SDT_DefSlider</SliderArt>
  </Slider>
  <Label item="Zeal_TargetRingFill_Value">
    <ScreenID>Zeal_TargetRingFill_Value</ScreenID>
    <RelativePosition>true</RelativePosition>
    <Location>
      <X>313</X>
      <Y>29</Y>
    </Location>
    <Size>
      <CX>50</CX>
      <CY>16</CY>
    </Size>
    <Text>0.0</Text>
    <TextColor>
      <R>255</R>
      <G>255</G>
      <B>255</B>
    </TextColor>
    <NoWrap>true</NoWrap>
    <AlignCenter>false</AlignCenter>
    <AlignRight>true</AlignRight>
  </Label>
  
  <Label item="Zeal_TargetRingSize_Label">
    <ScreenID>Zeal_TargetRingSize_Label</ScreenID>
    <RelativePosition>true</RelativePosition>
    <Location>
      <X>213</X>
      <Y>49</Y>
    </Location>
    <Size>
      <CX>150</CX>
      <CY>14</CY>
    </Size>
    <Text>Outter Size</Text>
    <TextColor>
      <R>255</R>
      <G>255</G>
      <B>255</B>
    </TextColor>
    <NoWrap>true</NoWrap>
    <AlignCenter>false</AlignCenter>
    <AlignRight>false</AlignRight>
  </Label>
  <Slider item="Zeal_TargetRingSize_Slider">
    <ScreenID>Zeal_TargetRingSize_Slider</ScreenID>
    <RelativePosition>true</RelativePosition>
    <Location>
      <X>213</X>
      <Y>69</Y>
    </Location>
    <Size>
      <CX>100</CX>
      <CY>16</CY>
    </Size>
    <SliderArt>SDT_DefSlider</SliderArt>
  </Slider>
  <Label item="Zeal_TargetRingSize_Value">
    <ScreenID>Zeal_TargetRingSize_Value</ScreenID>
    <RelativePosition>true</RelativePosition>
    <Location>
      <X>313</X>
      <Y>69</Y>
    </Location>
    <Size>
      <CX>50</CX>
      <CY>16</CY>
    </Size>
    <Text>0.0</Text>
    <TextColor>
      <R>255</R>
      <G>255</G>
      <B>255</B>
    </TextColor>
    <NoWrap>true</NoWrap>
    <AlignCenter>false</AlignCenter>
    <AlignRight>true</AlignRight>
  </Label>
  <Label item="Zeal_TargetRingRotation_Label">
    <ScreenID>Zeal_TargetRingRotation_Label</ScreenID>
    <RelativePosition>true</RelativePosition>
    <Location>
      <X>213</X>
      <Y>89</Y>
    </Location>
    <Size>
      <CX>150</CX>
      <CY>14</CY>
    </Size>
    <Text>Rotation</Text>
    <TextColor>
      <R>255</R>
      <G>255</G>
      <B>255</B>
    </TextColor>
    <NoWrap>true</NoWrap>
    <AlignCenter>false</AlignCenter>
    <AlignRight>false</AlignRight>
  </Label>
  <Slider item="Zeal_TargetRingRotation_Slider">
    <ScreenID>Zeal_TargetRingRotation_Slider</ScreenID>
    <RelativePosition>true</RelativePosition>
    <Location>
      <X>213</X>
      <Y>109</Y>
    </Location>
    <Size>
      <CX>100</CX>
      <CY>16</CY>
    </Size>
    <SliderArt>SDT_DefSlider</SliderArt>
  </Slider>
  <Label item="Zeal_TargetRingRotation_Value">
    <ScreenID>Zeal_TargetRingRotation_Value</ScreenID>
    <RelativePosition>true</RelativePosition>
    <Location>
      <X>313</X>
      <Y>109</Y>
    </Location>
    <Size>
      <CX>50</CX>
      <CY>16</CY>
    </Size>
    <Text>0.0</Text>
    <TextColor>
      <R>255</R>
      <G>255</G>
      <B>255</B>
    </TextColor>
    <NoWrap>true</NoWrap>
    <AlignCenter>false</AlignCenter>
    <AlignRight>true</AlignRight>
  </Label>
  
  <Label item="Zeal_TargetRingSegments_Label">
    <ScreenID>Zeal_TargetRingSegments_Label</ScreenID>
    <RelativePosition>true</RelativePosition>
    <Location>
      <X>213</X>
      <Y>129</Y>
    </Location>
    <Size>
      <CX>150</CX>
      <CY>14</CY>
    </Size>
    <Text>Segments</Text>
    <TextColor>
      <R>255</R>
      <G>255</G>
      <B>255</B>
    </TextColor>
    <NoWrap>true</NoWrap>
    <AlignCenter>false</AlignCenter>
    <AlignRight>false</AlignRight>
  </Label>
  <Slider item="Zeal_TargetRingSegments_Slider">
    <ScreenID>Zeal_TargetRingSegments_Slider</ScreenID>
    <RelativePosition>true</RelativePosition>
    <Location>
      <X>213</X>
      <Y>149</Y>
    </Location>
    <Size>
      <CX>100</CX>
      <CY>16</CY>
    </Size>
    <SliderArt>SDT_DefSlider</SliderArt>
  </Slider>
  <Label item="Zeal_TargetRingSegments_Value">
    <ScreenID>Zeal_TargetRingSegments_Value</ScreenID>
    <RelativePosition>true</RelativePosition>
    <Location>
      <X>313</X>
      <Y>149</Y>
    </Location>
    <Size>
      <CX>50</CX>
      <CY>16</CY>
    </Size>
    <Text>0.0</Text>
    <TextColor>
      <R>255</R>
      <G>255</G>
      <B>255</B>
    </TextColor>
    <NoWrap>true</NoWrap>
    <AlignCenter>false</AlignCenter>
    <AlignRight>true</AlignRight>
  </Label>
  
  <Label item="Zeal_TargetRingFlash_Label">
    <ScreenID>Zeal_TargetRingFlash_Label</ScreenID>
    <RelativePosition>true</RelativePosition>
    <Location>
      <X>213</X>
      <Y>169</Y>
    </Location>
    <Size>
      <CX>150</CX>
      <CY>14</CY>
    </Size>
    <Text>Indicator Flash Delay</Text>
    <TextColor>
      <R>255</R>
      <G>255</G>
      <B>255</B>
    </TextColor>
    <NoWrap>true</NoWrap>
    <AlignCenter>false</AlignCenter>
    <AlignRight>false</AlignRight>
  </Label>
  <Slider item="Zeal_TargetRingFlash_Slider">
    <ScreenID>Zeal_TargetRingFlash_Slider</ScreenID>
    <RelativePosition>true</RelativePosition>
    <Location>
      <X>213</X>
      <Y>189</Y>
    </Location>
    <Size>
      <CX>100</CX>
      <CY>16</CY>
    </Size>
    <SliderArt>SDT_DefSlider</SliderArt>
  </Slider>
  <Label item="Zeal_TargetRingFlash_Value">
    <ScreenID>Zeal_TargetRingFlash_Value</ScreenID>
    <RelativePosition>true</RelativePosition>
    <Location>
      <X>313</X>
      <Y>189</Y>
    </Location>
    <Size>
      <CX>50</CX>
      <CY>16</CY>
    </Size>
    <Text>0.0</Text>
    <TextColor>
      <R>255</R>
      <G>255</G>
      <B>255</B>
    </TextColor>
    <NoWrap>true</NoWrap>
    <AlignCenter>false</AlignCenter>
    <AlignRight>true</AlignRight>
  </Label>
  
   <!-- Zeal Target Ring Transparency slider -->
  <Label item="Zeal_TargetRingTransparency_Label">
    <ScreenID>Zeal_TargetRingTransparency_Label</ScreenID>
    <RelativePosition>true</RelativePosition>
    <Location>
      <X>213</X>
      <Y>209</Y>
    </Location>
    <Size>
      <CX>150</CX>
      <CY>14</CY>
    </Size>
    <Text>Transparency</Text>
    <TextColor>
      <R>255</R>
      <G>255</G>
      <B>255</B>
    </TextColor>
    <NoWrap>true</NoWrap>
    <AlignCenter>false</AlignCenter>
    <AlignRight>false</AlignRight>
  </Label>
  <Slider item="Zeal_TargetRingTransparency_Slider">
    <ScreenID>Zeal_TargetRingTransparency_Slider</ScreenID>
    <RelativePosition>true</RelativePosition>
    <Location>
      <X>213</X>
      <Y>229</Y>
    </Location>
    <Size>
      <CX>100</CX>
      <CY>16</CY>
    </Size>
    <SliderArt>SDT_DefSlider</SliderArt>
  </Slider>
  <Label item="Zeal_TargetRingTransparency_Value">
    <ScreenID>Zeal_TargetRingTransparency_Value</ScreenID>
    <RelativePosition>true</RelativePosition>
    <Location>
      <X>313</X>
      <Y>229</Y>
    </Location>
    <Size>
      <CX>50</CX>
      <CY>16</CY>
    </Size>
    <Text>0.0</Text>
    <TextColor>
      <R>255</R>
      <G>255</G>
      <B>255</B>
    </TextColor>
    <NoWrap>true</NoWrap>
    <AlignCenter>false</AlignCenter>
    <AlignRight>true</AlignRight>
  </Label>
  
  <Label item="Zeal_TargetRingTexture_Label">
    <ScreenID>Zeal_TargetRingTexture_Label</ScreenID>
    <RelativePosition>true</RelativePosition>
    <Location>
      <X>211</X>
      <Y>249</Y>
    </Location>
    <Size>
      <CX>150</CX>
      <CY>14</CY>
    </Size>
    <Text>Texture</Text>
    <TextColor>
      <R>255</R>
      <G>255</G>
      <B>255</B>
    </TextColor>
    <NoWrap>true</NoWrap>
    <AlignCenter>false</AlignCenter>
    <AlignRight>false</AlignRight>
  </Label>
  <Combobox item="Zeal_TargetRingTexture_Combobox">
    <ScreenID>Zeal_TargetRingTexture_Combobox</ScreenID>
    <DrawTemplate>WDT_Inner</DrawTemplate>
    <Location>
      <X>211</X>
      <Y>269</Y>
    </Location>
    <Size>
      <CX>150</CX>
      <CY>24</CY>
    </Size>
    <TextColor>
      <R>255</R>
      <G>255</G>
      <B>255</B>
    </TextColor>
    <ListHeight>70</ListHeight>
    <Button>BDT_Combo</Button>
    <Style_Border>true</Style_Border>
    <Choices>None</Choices>
  </Combobox>
    <Page item="Tab_TargetRing">
    <ScreenID>Tab_TargetRing</ScreenID>
    <RelativePosition>true</RelativePosition>
    <!-- Pages are sized and positioned by their parent tab -->
    <Style_VScroll>true</Style_VScroll>
    <Style_HScroll>false</Style_HScroll>
    <Style_Transparent>false</Style_Transparent>
    <DrawTemplate>WDT_Def</DrawTemplate>
    <TabText>Target Ring</TabText>
    <TabTextColor>
      <R>255</R>
      <G>255</G>
      <B>255</B>
    </TabTextColor>
    <TabTextActiveColor>
      <R>255</R>
      <G>255</G>
      <B>0</B>
    </TabTextActiveColor>
    <Style_Sizable>true</Style_Sizable>
    <Pieces>Zeal_TargetRing</Pieces>
    <Pieces>Zeal_TargetRingDisableForSelf</Pieces>
    <Pieces>Zeal_TargetRingFill_Label</Pieces>
    <Pieces>Zeal_TargetRingFill_Slider</Pieces>
    <Pieces>Zeal_TargetRingFill_Value</Pieces>
    <Pieces>Zeal_TargetRingSize_Label</Pieces>
    <Pieces>Zeal_TargetRingSize_Slider</Pieces>
    <Pieces>Zeal_TargetRingSize_Value</Pieces>
    <Pieces>Zeal_TargetRingRotation_Label</Pieces>
    <Pieces>Zeal_TargetRingRotation_Slider</Pieces>
    <Pieces>Zeal_TargetRingRotation_Value</Pieces>
    <Pieces>Zeal_TargetRingSegments_Label</Pieces>
    <Pieces>Zeal_TargetRingSegments_Slider</Pieces>
    <Pieces>Zeal_TargetRingSegments_Value</Pieces>
    <Pieces>Zeal_TargetRingFlash_Label</Pieces>
    <Pieces>Zeal_TargetRingFlash_Slider</Pieces>
    <Pieces>Zeal_TargetRingFlash_Value</Pieces>
    <Pieces>Zeal_TargetRingTransparency_Label</Pieces>
    <Pieces>Zeal_TargetRingTransparency_Slider</Pieces>
    <Pieces>Zeal_TargetRingTransparency_Value</Pieces>
    <Pieces>Zeal_TargetRingTexture_Label</Pieces>
    <Pieces>Zeal_TargetRingAttackIndicator</Pieces>
    <Pieces>Zeal_TargetRingForward</Pieces>
    <Pieces>Zeal_TargetRingTexture_Combobox</Pieces>
    <Pieces>Zeal_TargetRingCone</Pieces>
    <Pieces>Zeal_TargetRingColor</Pieces>
    <Pieces>Zeal_TargetRingHideWithGui</Pieces>
    <Location>
      <X>0</X>
      <Y>22</Y>
    </Location>
    <Size>
      <CX>380</CX>
      <CY>339</CY>
    </Size>
  </Page>
  </XML>
