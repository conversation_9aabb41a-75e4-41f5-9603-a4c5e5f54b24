<?xml version="1.0" encoding="us-ascii"?>
<XML ID="EQInterfaceDefinitionLanguage">
  <Schema xmlns="EverQuestData" xmlns:dt="EverQuestDataTypes" />
  
  <!-- Sigma Tab Header -->
  <Label item="Sigma_HeaderLabel">
    <ScreenID>Sigma_HeaderLabel</ScreenID>
    <RelativePosition>true</RelativePosition>
    <Location>
      <X>10</X>
      <Y>1</Y>
    </Location>
    <Size>
      <CX>500</CX>
      <CY>16</CY>
    </Size>
    <Text>Sigma - Custom Zeal Modifications</Text>
    <TextColor>
      <R>255</R>
      <G>255</G>
      <B>0</B>
      <Alpha>255</Alpha>
    </TextColor>
    <NoWrap>true</NoWrap>
    <AlignCenter>false</AlignCenter>
    <AlignRight>false</AlignRight>
  </Label>

  <!-- Tradeskill Section -->
  <Label item="Sigma_TradeskillSectionLabel">
    <ScreenID>Sigma_TradeskillSectionLabel</ScreenID>
    <RelativePosition>true</RelativePosition>
    <Location>
      <X>10</X>
      <Y>25</Y>
    </Location>
    <Size>
      <CX>200</CX>
      <CY>16</CY>
    </Size>
    <Text>Tradeskill Enhancements:</Text>
    <TextColor>
      <R>200</R>
      <G>200</G>
      <B>255</B>
      <Alpha>255</Alpha>
    </TextColor>
    <NoWrap>true</NoWrap>
    <AlignCenter>false</AlignCenter>
    <AlignRight>false</AlignRight>
  </Label>

  <Button item="Sigma_TradeskillAltRightClick">
    <ScreenID>Sigma_TradeskillAltRightClick</ScreenID>
    <RelativePosition>true</RelativePosition>
    <Location>
      <X>10</X>
      <Y>45</Y>
    </Location>
    <Size>
      <CX>200</CX>
      <CY>20</CY>
    </Size>
    <Style_VScroll>false</Style_VScroll>
    <Style_HScroll>false</Style_HScroll>
    <Style_Transparent>false</Style_Transparent>
    <Style_Checkbox>true</Style_Checkbox>
    <TooltipReference>Alt+Right-click on inventory items to move one item from stack to tradeskill container</TooltipReference>
    <Text>Alt+Right-click Tradeskill Transfer</Text>
    <TextColor>
      <R>255</R>
      <G>255</G>
      <B>255</B>
    </TextColor>
    <ButtonDrawTemplate>
      <Normal>A_BtnNormal</Normal>
      <Pressed>A_BtnPressed</Pressed>
      <Flyby>A_BtnFlyby</Flyby>
      <Disabled>A_BtnDisabled</Disabled>
      <PressedFlyby>A_BtnPressedFlyby</PressedFlyby>
    </ButtonDrawTemplate>
  </Button>

  <Button item="Sigma_TradeskillShowMessages">
    <ScreenID>Sigma_TradeskillShowMessages</ScreenID>
    <RelativePosition>true</RelativePosition>
    <Location>
      <X>10</X>
      <Y>70</Y>
    </Location>
    <Size>
      <CX>200</CX>
      <CY>20</CY>
    </Size>
    <Style_VScroll>false</Style_VScroll>
    <Style_HScroll>false</Style_HScroll>
    <Style_Transparent>false</Style_Transparent>
    <Style_Checkbox>true</Style_Checkbox>
    <TooltipReference>Show Sigma tradeskill messages in chat window</TooltipReference>
    <Text>Show Tradeskill Messages</Text>
    <TextColor>
      <R>255</R>
      <G>255</G>
      <B>255</B>
    </TextColor>
    <ButtonDrawTemplate>
      <Normal>A_BtnNormal</Normal>
      <Pressed>A_BtnPressed</Pressed>
      <Flyby>A_BtnFlyby</Flyby>
      <Disabled>A_BtnDisabled</Disabled>
      <PressedFlyby>A_BtnPressedFlyby</PressedFlyby>
    </ButtonDrawTemplate>
  </Button>

  <!-- Future Features Section -->
  <Label item="Sigma_FutureFeaturesLabel">
    <ScreenID>Sigma_FutureFeaturesLabel</ScreenID>
    <RelativePosition>true</RelativePosition>
    <Location>
      <X>10</X>
      <Y>110</Y>
    </Location>
    <Size>
      <CX>200</CX>
      <CY>16</CY>
    </Size>
    <Text>Future Features:</Text>
    <TextColor>
      <R>200</R>
      <G>200</G>
      <B>255</B>
      <Alpha>255</Alpha>
    </TextColor>
    <NoWrap>true</NoWrap>
    <AlignCenter>false</AlignCenter>
    <AlignRight>false</AlignRight>
  </Label>

  <Label item="Sigma_PlaceholderLabel">
    <ScreenID>Sigma_PlaceholderLabel</ScreenID>
    <RelativePosition>true</RelativePosition>
    <Location>
      <X>10</X>
      <Y>130</Y>
    </Location>
    <Size>
      <CX>400</CX>
      <CY>16</CY>
    </Size>
    <Text>Additional custom features will be added here in future updates.</Text>
    <TextColor>
      <R>150</R>
      <G>150</G>
      <B>150</B>
      <Alpha>255</Alpha>
    </TextColor>
    <NoWrap>true</NoWrap>
    <AlignCenter>false</AlignCenter>
    <AlignRight>false</AlignRight>
  </Label>

  <!-- Sigma Tab Page Definition -->
  <Page item="Tab_Sigma">
    <ScreenID>Tab_Sigma</ScreenID>
    <RelativePosition>true</RelativePosition>
    <!-- Pages are sized and positioned by their parent tab -->
    <Style_VScroll>true</Style_VScroll>
    <Style_HScroll>true</Style_HScroll>
    <Style_Transparent>false</Style_Transparent>
    <DrawTemplate>WDT_Def</DrawTemplate>
    <TabText>Sigma</TabText>
    <TabTextColor>
      <R>255</R>
      <G>255</G>
      <B>255</B>
    </TabTextColor>
    <TabTextActiveColor>
      <R>255</R>
      <G>255</G>
      <B>0</B>
    </TabTextActiveColor>
    <Style_Sizable>true</Style_Sizable>
    <Pieces>Sigma_HeaderLabel</Pieces>
    <Pieces>Sigma_TradeskillSectionLabel</Pieces>
    <Pieces>Sigma_TradeskillAltRightClick</Pieces>
    <Pieces>Sigma_TradeskillShowMessages</Pieces>
    <Pieces>Sigma_FutureFeaturesLabel</Pieces>
    <Pieces>Sigma_PlaceholderLabel</Pieces>
    <Location>
      <X>0</X>
      <Y>22</Y>
    </Location>
    <Size>
      <CX>380</CX>
      <CY>339</CY>
    </Size>
  </Page>
</XML>
