<?xml version="1.0" encoding="us-ascii"?>
<XML ID="EQInterfaceDefinitionLanguage">
  <Schema xmlns="EverQuestData" xmlns:dt="EverQuestDataTypes" />

  <Button item="Zeal_Cam">
    <ScreenID>Zeal_Cam</ScreenID>
    <RelativePosition>true</RelativePosition>
    <Location>
      <X>20</X>
      <Y>28</Y>
    </Location>
    <Size>
      <CX>300</CX>
      <CY>40</CY>
    </Size>
    <Style_VScroll>false</Style_VScroll>
    <Style_HScroll>false</Style_HScroll>
    <Style_Transparent>false</Style_Transparent>
    <Style_Checkbox>true</Style_Checkbox>
    <TooltipReference>Toggles Zeal's mouse look smoothing</TooltipReference>
    <Text>Enabled</Text>
    <TextColor>
      <R>255</R>
      <G>255</G>
      <B>255</B>
    </TextColor>
    <ButtonDrawTemplate>
      <Normal>A_BtnNormal</Normal>
      <Pressed>A_BtnPressed</Pressed>
      <Flyby>A_BtnFlyby</Flyby>
      <Disabled>A_BtnDisabled</Disabled>
      <PressedFlyby>A_BtnPressedFlyby</PressedFlyby>
    </ButtonDrawTemplate>
  </Button>
   
  <Label item="Zeal_PanDelayLabel">
    <ScreenID>Zeal_PanDelayLabel</ScreenID>
    <RelativePosition>true</RelativePosition>
    <Location>
      <X>394</X>
      <Y>20</Y>
    </Location>
    <Size>
      <CX>300</CX>
      <CY>28</CY>
    </Size>
    <Text>Pan Delay</Text>
    <TextColor>
      <R>255</R>
      <G>255</G>
      <B>255</B>
    </TextColor>
    <NoWrap>true</NoWrap>
    <AlignCenter>false</AlignCenter>
    <AlignRight>false</AlignRight>
  </Label>
  <Slider item="Zeal_PanDelaySlider">
    <ScreenID>Zeal_PanDelaySlider</ScreenID>
    <RelativePosition>true</RelativePosition>
    <Location>
      <X>394</X>
      <Y>60</Y>
    </Location>
    <Size>
      <CX>200</CX>
      <CY>32</CY>
    </Size>
    <SliderArt>SDT_DefSlider</SliderArt>
  </Slider>
  <Label item="Zeal_PanDelayValueLabel">
    <ScreenID>Zeal_PanDelayValueLabel</ScreenID>
    <RelativePosition>true</RelativePosition>
    <Location>
      <X>594</X>
      <Y>60</Y>
    </Location>
    <Size>
      <CX>100</CX>
      <CY>32</CY>
    </Size>
    <Text>0.0</Text>
    <TextColor>
      <R>255</R>
      <G>255</G>
      <B>255</B>
    </TextColor>
    <NoWrap>true</NoWrap>
    <AlignCenter>false</AlignCenter>
    <AlignRight>true</AlignRight>
  </Label>
  
  <Label item="Zeal_CamSensitivityLabel">
    <ScreenID>Zeal_CamSensitivityLabel</ScreenID>
    <RelativePosition>true</RelativePosition>
    <Location>
      <X>400</X>
      <Y>202</Y>
    </Location>
    <Size>
      <CX>300</CX>
      <CY>28</CY>
    </Size>
    <Text>Zeal Cam Sensitivity</Text>
    <TextColor>
      <R>255</R>
      <G>255</G>
      <B>255</B>
    </TextColor>
    <NoWrap>true</NoWrap>
    <AlignCenter>false</AlignCenter>
    <AlignRight>false</AlignRight>
  </Label>
  
  <Label item="Zeal_FirstPersonXLabel">
    <ScreenID>Zeal_FirstPersonXLabel</ScreenID>
    <RelativePosition>true</RelativePosition>
    <Location>
      <X>440</X>
      <Y>250</Y>
    </Location>
    <Size>
      <CX>300</CX>
      <CY>28</CY>
    </Size>
    <Text>First Person X</Text>
    <TextColor>
      <R>255</R>
      <G>255</G>
      <B>255</B>
    </TextColor>
    <NoWrap>true</NoWrap>
    <AlignCenter>false</AlignCenter>
    <AlignRight>false</AlignRight>
  </Label>
  <Slider item="Zeal_FirstPersonSlider_X">
    <ScreenID>Zeal_FirstPersonSlider_X</ScreenID>
    <RelativePosition>true</RelativePosition>
    <Location>
      <X>440</X>
      <Y>290</Y>
    </Location>
    <Size>
      <CX>200</CX>
      <CY>32</CY>
    </Size>
    <SliderArt>SDT_DefSlider</SliderArt>
  </Slider>
  <Label item="Zeal_FirstPersonLabel_X">
    <ScreenID>Zeal_FirstPersonLabel_X</ScreenID>
    <RelativePosition>true</RelativePosition>
    <Location>
      <X>620</X>
      <Y>288</Y>
    </Location>
    <Size>
      <CX>100</CX>
      <CY>32</CY>
    </Size>
    <Text>0.0</Text>
    <TextColor>
      <R>255</R>
      <G>255</G>
      <B>255</B>
    </TextColor>
    <NoWrap>true</NoWrap>
    <AlignCenter>false</AlignCenter>
    <AlignRight>true</AlignRight>
  </Label>
  
  <Label item="Zeal_FirstPersonYLabel">
    <ScreenID>Zeal_FirstPersonYLabel</ScreenID>
    <RelativePosition>true</RelativePosition>
    <Location>
      <X>440</X>
      <Y>326</Y>
    </Location>
    <Size>
      <CX>300</CX>
      <CY>28</CY>
    </Size>
    <Text>First Person Y</Text>
    <TextColor>
      <R>255</R>
      <G>255</G>
      <B>255</B>
    </TextColor>
    <NoWrap>true</NoWrap>
    <AlignCenter>false</AlignCenter>
    <AlignRight>false</AlignRight>
  </Label>
  <Slider item="Zeal_FirstPersonSlider_Y">
    <ScreenID>Zeal_FirstPersonSlider_Y</ScreenID>
    <RelativePosition>true</RelativePosition>
    <Location>
      <X>440</X>
      <Y>366</Y>
    </Location>
    <Size>
      <CX>200</CX>
      <CY>32</CY>
    </Size>
    <SliderArt>SDT_DefSlider</SliderArt>
  </Slider>
  <Label item="Zeal_FirstPersonLabel_Y">
    <ScreenID>Zeal_FirstPersonLabel_Y</ScreenID>
    <RelativePosition>true</RelativePosition>
    <Location>
      <X>620</X>
      <Y>364</Y>
    </Location>
    <Size>
      <CX>100</CX>
      <CY>32</CY>
    </Size>
    <Text>0.0</Text>
    <TextColor>
      <R>255</R>
      <G>255</G>
      <B>255</B>
    </TextColor>
    <NoWrap>true</NoWrap>
    <AlignCenter>false</AlignCenter>
    <AlignRight>true</AlignRight>
  </Label>
  
  <Label item="Zeal_ThirdPersonXLabel">
    <ScreenID>Zeal_ThirdPersonXLabel</ScreenID>
    <RelativePosition>true</RelativePosition>
    <Location>
      <X>440</X>
      <Y>408</Y>
    </Location>
    <Size>
      <CX>300</CX>
      <CY>28</CY>
    </Size>
    <Text>Third Person X</Text>
    <TextColor>
      <R>255</R>
      <G>255</G>
      <B>255</B>
    </TextColor>
    <NoWrap>true</NoWrap>
    <AlignCenter>false</AlignCenter>
    <AlignRight>false</AlignRight>
  </Label>
  <Slider item="Zeal_ThirdPersonSlider_X">
    <ScreenID>Zeal_ThirdPersonSlider_X</ScreenID>
    <RelativePosition>true</RelativePosition>
    <Location>
      <X>440</X>
      <Y>448</Y>
    </Location>
    <Size>
      <CX>200</CX>
      <CY>32</CY>
    </Size>
    <SliderArt>SDT_DefSlider</SliderArt>
  </Slider>
  <Label item="Zeal_ThirdPersonLabel_X">
    <ScreenID>Zeal_ThirdPersonLabel_X</ScreenID>
    <RelativePosition>true</RelativePosition>
    <Location>
      <X>620</X>
      <Y>446</Y>
    </Location>
    <Size>
      <CX>100</CX>
      <CY>32</CY>
    </Size>
    <Text>0.0</Text>
    <TextColor>
      <R>255</R>
      <G>255</G>
      <B>255</B>
    </TextColor>
    <NoWrap>true</NoWrap>
    <AlignCenter>false</AlignCenter>
    <AlignRight>true</AlignRight>
  </Label>
  
  <Label item="Zeal_ThirdPersonYLabel">
    <ScreenID>Zeal_ThirdPersonYLabel</ScreenID>
    <RelativePosition>true</RelativePosition>
    <Location>
      <X>440</X>
      <Y>484</Y>
    </Location>
    <Size>
      <CX>300</CX>
      <CY>28</CY>
    </Size>
    <Text>Third Person Y</Text>
    <TextColor>
      <R>255</R>
      <G>255</G>
      <B>255</B>
    </TextColor>
    <NoWrap>true</NoWrap>
    <AlignCenter>false</AlignCenter>
    <AlignRight>false</AlignRight>
  </Label>
  <Slider item="Zeal_ThirdPersonSlider_Y">
    <ScreenID>Zeal_ThirdPersonSlider_Y</ScreenID>
    <RelativePosition>true</RelativePosition>
    <Location>
      <X>440</X>
      <Y>524</Y>
    </Location>
    <Size>
      <CX>200</CX>
      <CY>32</CY>
    </Size>
    <SliderArt>SDT_DefSlider</SliderArt>
  </Slider>
  <Label item="Zeal_ThirdPersonLabel_Y">
    <ScreenID>Zeal_ThirdPersonLabel_Y</ScreenID>
    <RelativePosition>true</RelativePosition>
    <Location>
      <X>620</X>
      <Y>522</Y>
    </Location>
    <Size>
      <CX>100</CX>
      <CY>32</CY>
    </Size>
    <Text>0.0</Text>
    <TextColor>
      <R>255</R>
      <G>255</G>
      <B>255</B>
    </TextColor>
    <NoWrap>true</NoWrap>
    <AlignCenter>false</AlignCenter>
    <AlignRight>true</AlignRight>
  </Label>
  <Label item="Zeal_FovLabel_">
    <ScreenID>Zeal_FovLabel_</ScreenID>
    <RelativePosition>true</RelativePosition>
    <Location>
      <X>394</X>
      <Y>104</Y>
    </Location>
    <Size>
      <CX>300</CX>
      <CY>28</CY>
    </Size>
    <Text>Field of View</Text>
    <TextColor>
      <R>255</R>
      <G>255</G>
      <B>255</B>
    </TextColor>
    <NoWrap>true</NoWrap>
    <AlignCenter>false</AlignCenter>
    <AlignRight>false</AlignRight>
  </Label>
  <Slider item="Zeal_Fov">
    <ScreenID>Zeal_FoVSlider</ScreenID>
    <RelativePosition>true</RelativePosition>
    <Location>
      <X>394</X>
      <Y>144</Y>
    </Location>
    <Size>
      <CX>200</CX>
      <CY>32</CY>
    </Size>
    <SliderArt>SDT_DefSlider</SliderArt>
  </Slider>
  <Label item="Zeal_FovLabel">
    <ScreenID>Zeal_FoVValueLabel</ScreenID>
    <RelativePosition>true</RelativePosition>
    <Location>
      <X>594</X>
      <Y>144</Y>
    </Location>
    <Size>
      <CX>100</CX>
      <CY>32</CY>
    </Size>
    <Text>0</Text>
    <TextColor>
      <R>255</R>
      <G>255</G>
      <B>255</B>
    </TextColor>
    <NoWrap>true</NoWrap>
    <AlignCenter>false</AlignCenter>
    <AlignRight>true</AlignRight>
  </Label>
  <Button item="Zeal_UseOldSens">
    <ScreenID>Zeal_UseOldSens</ScreenID>
    <RelativePosition>true</RelativePosition>
    <Location>
      <X>20</X>
      <Y>72</Y>
    </Location>
    <Size>
      <CX>300</CX>
      <CY>40</CY>
    </Size>
    <Style_VScroll>false</Style_VScroll>
    <Style_HScroll>false</Style_HScroll>
    <Style_Transparent>false</Style_Transparent>
    <Style_Checkbox>true</Style_Checkbox>
    <TooltipReference>Uses the previous versions sensitivity adjustments for mouse look</TooltipReference>
    <Text>Old Sensitivity Math</Text>
    <TextColor>
      <R>255</R>
      <G>255</G>
      <B>255</B>
    </TextColor>
    <ButtonDrawTemplate>
      <Normal>A_BtnNormal</Normal>
      <Pressed>A_BtnPressed</Pressed>
      <Flyby>A_BtnFlyby</Flyby>
      <Disabled>A_BtnDisabled</Disabled>
      <PressedFlyby>A_BtnPressedFlyby</PressedFlyby>
    </ButtonDrawTemplate>
  </Button>
   <Button item="Zeal_Cam_TurnLocked">
    <ScreenID>Zeal_Cam_TurnLocked</ScreenID>
    <RelativePosition>true</RelativePosition>
    <Location>
      <X>20</X>
      <Y>116</Y>
    </Location>
    <Size>
      <CX>300</CX>
      <CY>40</CY>
    </Size>
    <Style_VScroll>false</Style_VScroll>
    <Style_HScroll>false</Style_HScroll>
    <Style_Transparent>false</Style_Transparent>
    <Style_Checkbox>true</Style_Checkbox>
    <TooltipReference>Turns your camera in third person with turn keys</TooltipReference>
    <Text>Key Turn-&gt;Camera Turn</Text>
    <TextColor>
      <R>255</R>
      <G>255</G>
      <B>255</B>
    </TextColor>
    <ButtonDrawTemplate>
      <Normal>A_BtnNormal</Normal>
      <Pressed>A_BtnPressed</Pressed>
      <Flyby>A_BtnFlyby</Flyby>
      <Disabled>A_BtnDisabled</Disabled>
      <PressedFlyby>A_BtnPressedFlyby</PressedFlyby>
    </ButtonDrawTemplate>
  </Button>
  <Button item="Zeal_Cam_ToggleOverheadView">
    <ScreenID>Zeal_Cam_ToggleOverheadView</ScreenID>
    <RelativePosition>true</RelativePosition>
    <Location>
      <X>20</X>
      <Y>160</Y>
    </Location>
    <Size>
      <CX>300</CX>
      <CY>40</CY>
    </Size>
    <Style_VScroll>false</Style_VScroll>
    <Style_HScroll>false</Style_HScroll>
    <Style_Transparent>false</Style_Transparent>
    <Style_Checkbox>true</Style_Checkbox>
    <TooltipReference>Includes overhead view in Toggle Camera cycle</TooltipReference>
    <Text>Cycle Overhead View</Text>
    <TextColor>
      <R>255</R>
      <G>255</G>
      <B>255</B>
    </TextColor>
    <ButtonDrawTemplate>
      <Normal>A_BtnNormal</Normal>
      <Pressed>A_BtnPressed</Pressed>
      <Flyby>A_BtnFlyby</Flyby>
      <Disabled>A_BtnDisabled</Disabled>
      <PressedFlyby>A_BtnPressedFlyby</PressedFlyby>
    </ButtonDrawTemplate>
  </Button>
  <Button item="Zeal_Cam_ToggleZealView">
    <ScreenID>Zeal_Cam_ToggleZealView</ScreenID>
    <RelativePosition>true</RelativePosition>
    <Location>
      <X>20</X>
      <Y>204</Y>
    </Location>
    <Size>
      <CX>300</CX>
      <CY>40</CY>
    </Size>
    <Style_VScroll>false</Style_VScroll>
    <Style_HScroll>false</Style_HScroll>
    <Style_Transparent>false</Style_Transparent>
    <Style_Checkbox>true</Style_Checkbox>
    <TooltipReference>Includes Zeal (chase) camera view in Toggle Camera cycle</TooltipReference>
    <Text>Cycle Zeal View</Text>
    <TextColor>
      <R>255</R>
      <G>255</G>
      <B>255</B>
    </TextColor>
    <ButtonDrawTemplate>
      <Normal>A_BtnNormal</Normal>
      <Pressed>A_BtnPressed</Pressed>
      <Flyby>A_BtnFlyby</Flyby>
      <Disabled>A_BtnDisabled</Disabled>
      <PressedFlyby>A_BtnPressedFlyby</PressedFlyby>
    </ButtonDrawTemplate>
  </Button>
  <Button item="Zeal_Cam_ToggleFree1View">
    <ScreenID>Zeal_Cam_ToggleFree1View</ScreenID>
    <RelativePosition>true</RelativePosition>
    <Location>
      <X>20</X>
      <Y>248</Y>
    </Location>
    <Size>
      <CX>300</CX>
      <CY>40</CY>
    </Size>
    <Style_VScroll>false</Style_VScroll>
    <Style_HScroll>false</Style_HScroll>
    <Style_Transparent>false</Style_Transparent>
    <Style_Checkbox>true</Style_Checkbox>
    <TooltipReference>Includes Free 1 camera view in Toggle Camera cycle</TooltipReference>
    <Text>Cycle Free 1 View</Text>
    <TextColor>
      <R>255</R>
      <G>255</G>
      <B>255</B>
    </TextColor>
    <ButtonDrawTemplate>
      <Normal>A_BtnNormal</Normal>
      <Pressed>A_BtnPressed</Pressed>
      <Flyby>A_BtnFlyby</Flyby>
      <Disabled>A_BtnDisabled</Disabled>
      <PressedFlyby>A_BtnPressedFlyby</PressedFlyby>
    </ButtonDrawTemplate>
  </Button>
  <Button item="Zeal_Cam_ToggleFree2View">
    <ScreenID>Zeal_Cam_ToggleFree2View</ScreenID>
    <RelativePosition>true</RelativePosition>
    <Location>
      <X>20</X>
      <Y>292</Y>
    </Location>
    <Size>
      <CX>300</CX>
      <CY>40</CY>
    </Size>
    <Style_VScroll>false</Style_VScroll>
    <Style_HScroll>false</Style_HScroll>
    <Style_Transparent>false</Style_Transparent>
    <Style_Checkbox>true</Style_Checkbox>
    <TooltipReference>Includes Free 2 camera view in Toggle Camera cycle</TooltipReference>
    <Text>Cycle Free 2 View</Text>
    <TextColor>
      <R>255</R>
      <G>255</G>
      <B>255</B>
    </TextColor>
    <ButtonDrawTemplate>
      <Normal>A_BtnNormal</Normal>
      <Pressed>A_BtnPressed</Pressed>
      <Flyby>A_BtnFlyby</Flyby>
      <Disabled>A_BtnDisabled</Disabled>
      <PressedFlyby>A_BtnPressedFlyby</PressedFlyby>
    </ButtonDrawTemplate>
  </Button>
  <Page item="Tab_Camera">
    <ScreenID>Tab_Camera</ScreenID>
    <RelativePosition>true</RelativePosition>
    
    <Style_VScroll>true</Style_VScroll>
    <Style_HScroll>false</Style_HScroll>
    <Style_Transparent>false</Style_Transparent>
    <DrawTemplate>WDT_Def</DrawTemplate>
    <TabText>Cam</TabText>
    <TabTextColor>
      <R>255</R>
      <G>255</G>
      <B>255</B>
    </TabTextColor>
    <TabTextActiveColor>
      <R>255</R>
      <G>255</G>
      <B>0</B>
    </TabTextActiveColor>
    <Style_Sizable>true</Style_Sizable>
    <Pieces>Zeal_Cam</Pieces>
    <Pieces>Zeal_UseOldSens</Pieces>
    <Pieces>Zeal_Cam_ToggleOverheadView</Pieces>
    <Pieces>Zeal_Cam_ToggleZealView</Pieces>
    <Pieces>Zeal_Cam_ToggleFree1View</Pieces>
    <Pieces>Zeal_Cam_ToggleFree2View</Pieces>
    <Pieces>Zeal_CamSensitivityLabel</Pieces>
    <Pieces>Zeal_FirstPersonXLabel</Pieces>
    <Pieces>Zeal_FirstPersonSlider_X</Pieces>
    <Pieces>Zeal_FirstPersonLabel_X</Pieces>
    <Pieces>Zeal_FirstPersonYLabel</Pieces>
    <Pieces>Zeal_FirstPersonSlider_Y</Pieces>
    <Pieces>Zeal_FirstPersonLabel_Y</Pieces>
    <Pieces>Zeal_ThirdPersonXLabel</Pieces>
    <Pieces>Zeal_ThirdPersonSlider_X</Pieces>
    <Pieces>Zeal_ThirdPersonLabel_X</Pieces>
    <Pieces>Zeal_ThirdPersonYLabel</Pieces>
    <Pieces>Zeal_ThirdPersonSlider_Y</Pieces>
    <Pieces>Zeal_ThirdPersonLabel_Y</Pieces>
    <Pieces>Zeal_PanDelayLabel</Pieces>
    <Pieces>Zeal_PanDelaySlider</Pieces>
    <Pieces>Zeal_PanDelayValueLabel</Pieces>
    <Pieces>Zeal_Fov</Pieces>
    <Pieces>Zeal_FovLabel</Pieces>
    <Pieces>Zeal_FovLabel_</Pieces>
	<Pieces>Zeal_Cam_TurnLocked</Pieces>
    <Location>
      <X>0</X>
      <Y>44</Y>
    </Location>
    <Size>
      <CX>760</CX>
      <CY>678</CY>
    </Size>
  </Page>
  </XML>