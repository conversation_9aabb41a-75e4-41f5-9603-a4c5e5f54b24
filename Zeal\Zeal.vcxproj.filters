﻿<?xml version="1.0" encoding="utf-8"?>
<Project ToolsVersion="4.0" xmlns="http://schemas.microsoft.com/developer/msbuild/2003">
  <ItemGroup>
    <Filter Include="Source Files">
      <UniqueIdentifier>{4FC737F1-C7A5-4376-A066-2A32D752A2FF}</UniqueIdentifier>
      <Extensions>cpp;c;cc;cxx;c++;cppm;ixx;def;odl;idl;hpj;bat;asm;asmx</Extensions>
    </Filter>
    <Filter Include="Header Files">
      <UniqueIdentifier>{93995380-89BD-4b04-88EB-625FBE52EBFB}</UniqueIdentifier>
      <Extensions>h;hh;hpp;hxx;h++;hm;inl;inc;ipp;xsd</Extensions>
    </Filter>
    <Filter Include="Resource Files">
      <UniqueIdentifier>{67DA6AB6-F800-4c08-8B7A-83BB121AAD01}</UniqueIdentifier>
      <Extensions>rc;ico;cur;bmp;dlg;rc2;rct;bin;rgs;gif;jpg;jpeg;jpe;resx;tiff;tif;png;wav;mfcribbon-ms</Extensions>
    </Filter>
    <Filter Include="Header Files\memory">
      <UniqueIdentifier>{f0dd8c82-da84-4ebd-a05c-77d393b74285}</UniqueIdentifier>
    </Filter>
    <Filter Include="Source Files\memory">
      <UniqueIdentifier>{30efbb41-0e4c-432c-b755-ba8536e5f5e6}</UniqueIdentifier>
    </Filter>
    <Filter Include="Source Files\helpers">
      <UniqueIdentifier>{62f5607d-ae22-4c41-a59c-1ab9e2dd53fa}</UniqueIdentifier>
    </Filter>
    <Filter Include="Header Files\hooks">
      <UniqueIdentifier>{2a875da1-9bd1-4ad1-b132-3b7111729135}</UniqueIdentifier>
    </Filter>
    <Filter Include="Source Files\hooks">
      <UniqueIdentifier>{0c1bd18a-139b-47e6-8e00-c3d3228346a9}</UniqueIdentifier>
    </Filter>
    <Filter Include="Header Files\other">
      <UniqueIdentifier>{ea0b750a-50aa-4748-b13c-f4ea87ac3bd1}</UniqueIdentifier>
    </Filter>
    <Filter Include="Header Files\client">
      <UniqueIdentifier>{e5b39775-cb23-4102-bd7f-bbe1b3043de2}</UniqueIdentifier>
    </Filter>
    <Filter Include="Source Files\ui">
      <UniqueIdentifier>{7b003c9c-b705-4895-b5a7-5254e7fe2dcf}</UniqueIdentifier>
    </Filter>
    <Filter Include="Header Files\ui">
      <UniqueIdentifier>{e6269d11-8017-478b-a57d-95243aa4ca06}</UniqueIdentifier>
    </Filter>
    <Filter Include="Source Files\other">
      <UniqueIdentifier>{49dbe278-85aa-4b42-9831-fd53aef2022c}</UniqueIdentifier>
    </Filter>
    <Filter Include="Header Files\helpers">
      <UniqueIdentifier>{527e20ee-c27c-458f-94eb-b5dffd5cee70}</UniqueIdentifier>
    </Filter>
    <Filter Include="UI Files">
      <UniqueIdentifier>{5ac2a0c8-3a18-4026-bc6e-1cf0e43c27cf}</UniqueIdentifier>
    </Filter>
  </ItemGroup>
  <ItemGroup>
    <ClInclude Include="memory.h">
      <Filter>Header Files\memory</Filter>
    </ClInclude>
    <ClInclude Include="hook_wrapper.h">
      <Filter>Header Files\memory</Filter>
    </ClInclude>
    <ClInclude Include="vectors.h">
      <Filter>Header Files\helpers</Filter>
    </ClInclude>
    <ClInclude Include="camera_mods.h">
      <Filter>Header Files\hooks</Filter>
    </ClInclude>
    <ClInclude Include="find_pattern.h">
      <Filter>Header Files\memory</Filter>
    </ClInclude>
    <ClInclude Include="looting.h">
      <Filter>Header Files\hooks</Filter>
    </ClInclude>
    <ClInclude Include="labels.h">
      <Filter>Header Files\hooks</Filter>
    </ClInclude>
    <ClInclude Include="binds.h">
      <Filter>Header Files\hooks</Filter>
    </ClInclude>
    <ClInclude Include="commands.h">
      <Filter>Header Files\hooks</Filter>
    </ClInclude>
    <ClInclude Include="callbacks.h">
      <Filter>Header Files\hooks</Filter>
    </ClInclude>
    <ClInclude Include="experience.h">
      <Filter>Header Files\other</Filter>
    </ClInclude>
    <ClInclude Include="cycle_target.h">
      <Filter>Header Files\other</Filter>
    </ClInclude>
    <ClInclude Include="resource.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="raid.h">
      <Filter>Header Files\hooks</Filter>
    </ClInclude>
    <ClInclude Include="game_str.h">
      <Filter>Header Files\hooks</Filter>
    </ClInclude>
    <ClInclude Include="chat.h">
      <Filter>Header Files\hooks</Filter>
    </ClInclude>
    <ClInclude Include="outputfile.h">
      <Filter>Header Files\other</Filter>
    </ClInclude>
    <ClInclude Include="buff_timers.h">
      <Filter>Header Files\other</Filter>
    </ClInclude>
    <ClInclude Include="game_addresses.h">
      <Filter>Header Files\client</Filter>
    </ClInclude>
    <ClInclude Include="game_functions.h">
      <Filter>Header Files\client</Filter>
    </ClInclude>
    <ClInclude Include="game_structures.h">
      <Filter>Header Files\client</Filter>
    </ClInclude>
    <ClInclude Include="game_ui.h">
      <Filter>Header Files\client</Filter>
    </ClInclude>
    <ClInclude Include="spell_categories.h">
      <Filter>Header Files\client</Filter>
    </ClInclude>
    <ClInclude Include="alarm.h">
      <Filter>Header Files\other</Filter>
    </ClInclude>
    <ClInclude Include="player_movement.h">
      <Filter>Header Files\other</Filter>
    </ClInclude>
    <ClInclude Include="netstat.h">
      <Filter>Header Files\other</Filter>
    </ClInclude>
    <ClInclude Include="item_display.h">
      <Filter>Header Files\hooks</Filter>
    </ClInclude>
    <ClInclude Include="spellsets.h">
      <Filter>Header Files\hooks</Filter>
    </ClInclude>
    <ClInclude Include="melody.h">
      <Filter>Header Files\hooks</Filter>
    </ClInclude>
    <ClInclude Include="camera_math.h">
      <Filter>Header Files\helpers</Filter>
    </ClInclude>
    <ClInclude Include="ui_options.h">
      <Filter>Header Files\ui</Filter>
    </ClInclude>
    <ClInclude Include="ui_bank.h">
      <Filter>Header Files\ui</Filter>
    </ClInclude>
    <ClInclude Include="ui_manager.h">
      <Filter>Header Files\ui</Filter>
    </ClInclude>
    <ClInclude Include="ui_loot.h">
      <Filter>Header Files\ui</Filter>
    </ClInclude>
    <ClInclude Include="autofire.h">
      <Filter>Header Files\hooks</Filter>
    </ClInclude>
    <ClInclude Include="string_util.h">
      <Filter>Header Files\helpers</Filter>
    </ClInclude>
    <ClInclude Include="tooltip.h">
      <Filter>Header Files\hooks</Filter>
    </ClInclude>
    <ClInclude Include="ui_raid.h">
      <Filter>Header Files\ui</Filter>
    </ClInclude>
    <ClInclude Include="ui_guild.h">
      <Filter>Header Files\ui</Filter>
    </ClInclude>
    <ClInclude Include="named_pipe.h">
      <Filter>Header Files\other</Filter>
    </ClInclude>
    <ClInclude Include="instruction_length.h">
      <Filter>Header Files\memory</Filter>
    </ClInclude>
    <ClInclude Include="io_ini.h">
      <Filter>Header Files\other</Filter>
    </ClInclude>
    <ClInclude Include="physics.h">
      <Filter>Header Files\hooks</Filter>
    </ClInclude>
    <ClInclude Include="ui_hotbutton.h">
      <Filter>Header Files\ui</Filter>
    </ClInclude>
    <ClInclude Include="floating_damage.h">
      <Filter>Header Files\hooks</Filter>
    </ClInclude>
    <ClInclude Include="target_ring.h">
      <Filter>Header Files\other</Filter>
    </ClInclude>
    <ClInclude Include="crash_handler.h">
      <Filter>Header Files\other</Filter>
    </ClInclude>
    <ClInclude Include="patches.h">
      <Filter>Header Files\hooks</Filter>
    </ClInclude>
    <ClInclude Include="directx.h">
      <Filter>Header Files\hooks</Filter>
    </ClInclude>
    <ClInclude Include="nameplate.h">
      <Filter>Header Files\hooks</Filter>
    </ClInclude>
    <ClInclude Include="tellwindows.h">
      <Filter>Header Files\hooks</Filter>
    </ClInclude>
    <ClInclude Include="zone_map.h">
      <Filter>Header Files\other</Filter>
    </ClInclude>
    <ClInclude Include="zone_map_data.h">
      <Filter>Header Files\other</Filter>
    </ClInclude>
    <ClInclude Include="ui_group.h">
      <Filter>Header Files\ui</Filter>
    </ClInclude>
    <ClInclude Include="zeal.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="entity_manager.h">
      <Filter>Header Files\other</Filter>
    </ClInclude>
    <ClInclude Include="chatfilter.h">
      <Filter>Header Files\hooks</Filter>
    </ClInclude>
    <ClInclude Include="ui_inputdialog.h">
      <Filter>Header Files\ui</Filter>
    </ClInclude>
    <ClInclude Include="json.hpp">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="game_packets.h">
      <Filter>Header Files\client</Filter>
    </ClInclude>
    <ClInclude Include="helm_manager.h">
      <Filter>Header Files\hooks</Filter>
    </ClInclude>
    <ClInclude Include="music.h">
      <Filter>Header Files\hooks</Filter>
    </ClInclude>
    <ClInclude Include="ui_zoneselect.h">
      <Filter>Header Files\ui</Filter>
    </ClInclude>
    <ClInclude Include="tick.h">
      <Filter>Header Files\hooks</Filter>
    </ClInclude>
    <ClInclude Include="npc_give.h">
      <Filter>Header Files\hooks</Filter>
    </ClInclude>
    <ClInclude Include="ui_buff.h">
      <Filter>Header Files\ui</Filter>
    </ClInclude>
    <ClInclude Include="character_select.h">
      <Filter>Header Files\hooks</Filter>
    </ClInclude>
    <ClInclude Include="survey.h">
      <Filter>Header Files\hooks</Filter>
    </ClInclude>
    <ClInclude Include="equip_item.h">
      <Filter>Header Files\hooks</Filter>
    </ClInclude>
    <ClInclude Include="default_spritefont.h">
      <Filter>Header Files\other</Filter>
    </ClInclude>
    <ClInclude Include="bitmap_font.h">
      <Filter>Header Files\other</Filter>
    </ClInclude>
    <ClInclude Include="assist.h">
      <Filter>Header Files\other</Filter>
    </ClInclude>
    <ClInclude Include="miniz.h">
      <Filter>Header Files\other</Filter>
    </ClInclude>
    <ClInclude Include="zeal_settings.h">
      <Filter>Header Files\other</Filter>
    </ClInclude>
    <ClInclude Include="items.h">
      <Filter>Header Files\other</Filter>
    </ClInclude>
    <ClInclude Include="ui_inspect.h">
      <Filter>Header Files\ui</Filter>
    </ClInclude>
    <ClInclude Include="operator_overloads.h">
      <Filter>Header Files\helpers</Filter>
    </ClInclude>
    <ClInclude Include="ui_skin.h">
      <Filter>Header Files</Filter>
    </ClInclude>
  </ItemGroup>
  <ItemGroup>
    <ClCompile Include="dllmain.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="hook_wrapper.cpp">
      <Filter>Source Files\memory</Filter>
    </ClCompile>
    <ClCompile Include="memory.cpp">
      <Filter>Source Files\memory</Filter>
    </ClCompile>
    <ClCompile Include="vectors.cpp">
      <Filter>Source Files\helpers</Filter>
    </ClCompile>
    <ClCompile Include="camera_mods.cpp">
      <Filter>Source Files\hooks</Filter>
    </ClCompile>
    <ClCompile Include="find_pattern.cpp">
      <Filter>Source Files\memory</Filter>
    </ClCompile>
    <ClCompile Include="zeal.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="looting.cpp">
      <Filter>Source Files\hooks</Filter>
    </ClCompile>
    <ClCompile Include="labels.cpp">
      <Filter>Source Files\hooks</Filter>
    </ClCompile>
    <ClCompile Include="binds.cpp">
      <Filter>Source Files\hooks</Filter>
    </ClCompile>
    <ClCompile Include="commands.cpp">
      <Filter>Source Files\hooks</Filter>
    </ClCompile>
    <ClCompile Include="callbacks.cpp">
      <Filter>Source Files\hooks</Filter>
    </ClCompile>
    <ClCompile Include="experience.cpp">
      <Filter>Source Files\other</Filter>
    </ClCompile>
    <ClCompile Include="cycle_target.cpp">
      <Filter>Source Files\other</Filter>
    </ClCompile>
    <ClCompile Include="raid.cpp">
      <Filter>Source Files\hooks</Filter>
    </ClCompile>
    <ClCompile Include="game_str.cpp">
      <Filter>Source Files\hooks</Filter>
    </ClCompile>
    <ClCompile Include="chat.cpp">
      <Filter>Source Files\hooks</Filter>
    </ClCompile>
    <ClCompile Include="outputfile.cpp">
      <Filter>Source Files\other</Filter>
    </ClCompile>
    <ClCompile Include="buff_timers.cpp">
      <Filter>Source Files\other</Filter>
    </ClCompile>
    <ClCompile Include="spellsets.cpp">
      <Filter>Source Files\hooks</Filter>
    </ClCompile>
    <ClCompile Include="camera_math.cpp">
      <Filter>Source Files\helpers</Filter>
    </ClCompile>
    <ClCompile Include="alarm.cpp">
      <Filter>Source Files\other</Filter>
    </ClCompile>
    <ClCompile Include="player_movement.cpp">
      <Filter>Source Files\other</Filter>
    </ClCompile>
    <ClCompile Include="netstat.cpp">
      <Filter>Source Files\other</Filter>
    </ClCompile>
    <ClCompile Include="item_display.cpp">
      <Filter>Source Files\hooks</Filter>
    </ClCompile>
    <ClCompile Include="melody.cpp">
      <Filter>Source Files\hooks</Filter>
    </ClCompile>
    <ClCompile Include="string_util.cpp">
      <Filter>Source Files\helpers</Filter>
    </ClCompile>
    <ClCompile Include="ui_options.cpp">
      <Filter>Source Files\ui</Filter>
    </ClCompile>
    <ClCompile Include="ui_bank.cpp">
      <Filter>Source Files\ui</Filter>
    </ClCompile>
    <ClCompile Include="ui_manager.cpp">
      <Filter>Source Files\ui</Filter>
    </ClCompile>
    <ClCompile Include="ui_loot.cpp">
      <Filter>Source Files\ui</Filter>
    </ClCompile>
    <ClCompile Include="autofire.cpp">
      <Filter>Source Files\hooks</Filter>
    </ClCompile>
    <ClCompile Include="ui_raid.cpp">
      <Filter>Source Files\ui</Filter>
    </ClCompile>
    <ClCompile Include="ui_guild.cpp">
      <Filter>Source Files\ui</Filter>
    </ClCompile>
    <ClCompile Include="named_pipe.cpp">
      <Filter>Source Files\other</Filter>
    </ClCompile>
    <ClCompile Include="physics.cpp">
      <Filter>Source Files\hooks</Filter>
    </ClCompile>
    <ClCompile Include="tooltip.cpp">
      <Filter>Source Files\memory</Filter>
    </ClCompile>
    <ClCompile Include="ui_hotbutton.cpp">
      <Filter>Source Files\ui</Filter>
    </ClCompile>
    <ClCompile Include="floating_damage.cpp">
      <Filter>Source Files\hooks</Filter>
    </ClCompile>
    <ClCompile Include="target_ring.cpp">
      <Filter>Source Files\other</Filter>
    </ClCompile>
    <ClCompile Include="crash_handler.cpp">
      <Filter>Source Files\other</Filter>
    </ClCompile>
    <ClCompile Include="directx.cpp">
      <Filter>Source Files\hooks</Filter>
    </ClCompile>
    <ClCompile Include="miniz.c">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="patches.cpp">
      <Filter>Source Files\hooks</Filter>
    </ClCompile>
    <ClCompile Include="npc_give.cpp">
      <Filter>Source Files\hooks</Filter>
    </ClCompile>
    <ClCompile Include="nameplate.cpp">
      <Filter>Source Files\hooks</Filter>
    </ClCompile>
    <ClCompile Include="tellwindows.cpp">
      <Filter>Source Files\hooks</Filter>
    </ClCompile>
    <ClCompile Include="zone_map.cpp">
      <Filter>Source Files\other</Filter>
    </ClCompile>
    <ClCompile Include="zone_map_data.cpp">
      <Filter>Source Files\other</Filter>
    </ClCompile>
    <ClCompile Include="ui_group.cpp">
      <Filter>Source Files\ui</Filter>
    </ClCompile>
    <ClCompile Include="entity_manager.cpp">
      <Filter>Source Files\other</Filter>
    </ClCompile>
    <ClCompile Include="chatfilter.cpp">
      <Filter>Source Files\hooks</Filter>
    </ClCompile>
    <ClCompile Include="ui_inputdialog.cpp">
      <Filter>Source Files\ui</Filter>
    </ClCompile>
    <ClCompile Include="helm_manager.cpp">
      <Filter>Source Files\hooks</Filter>
    </ClCompile>
    <ClCompile Include="ui_buff.cpp">
      <Filter>Source Files\ui</Filter>
    </ClCompile>
    <ClCompile Include="music.cpp">
      <Filter>Source Files\hooks</Filter>
    </ClCompile>
    <ClCompile Include="ui_zoneselect.cpp">
      <Filter>Source Files\ui</Filter>
    </ClCompile>
    <ClCompile Include="character_select.cpp">
      <Filter>Source Files\hooks</Filter>
    </ClCompile>
    <ClCompile Include="bitmap_font.cpp">
      <Filter>Source Files\other</Filter>
    </ClCompile>
    <ClCompile Include="assist.cpp">
      <Filter>Source Files\other</Filter>
    </ClCompile>
    <ClCompile Include="game_functions.cpp">
      <Filter>Source Files\other</Filter>
    </ClCompile>
    <ClCompile Include="survey.cpp">
      <Filter>Source Files\hooks</Filter>
    </ClCompile>
    <ClCompile Include="tick.cpp">
      <Filter>Source Files\other</Filter>
    </ClCompile>
    <ClCompile Include="equip_item.cpp">
      <Filter>Source Files\hooks</Filter>
    </ClCompile>
    <ClCompile Include="items.cpp">
      <Filter>Source Files\other</Filter>
    </ClCompile>
    <ClCompile Include="ui_inspect.cpp">
      <Filter>Source Files\ui</Filter>
    </ClCompile>
    <ClCompile Include="ui_skin.cpp">
      <Filter>Source Files\ui</Filter>
    </ClCompile>
    <ClCompile Include="default_spritefont.cpp">
      <Filter>Source Files\other</Filter>
    </ClCompile>
  </ItemGroup>
  <ItemGroup>
    <ResourceCompile Include="Zeal.rc">
      <Filter>Resource Files</Filter>
    </ResourceCompile>
  </ItemGroup>
  <ItemGroup>
    <None Include="..\README.md" />
    <None Include="..\CHANGELOG.md" />
    <None Include="uifiles\zeal\spell_info\fetch_spell_info.py">
      <Filter>UI Files</Filter>
    </None>
    <None Include="uifiles\zeal\spell_info\README.md">
      <Filter>UI Files</Filter>
    </None>
    <None Include="uifiles\zeal\optional\README.md">
      <Filter>UI Files</Filter>
    </None>
    <None Include="uifiles\zeal\big_xml\README.md">
      <Filter>UI Files</Filter>
    </None>
    <None Include="uifiles\zeal\big_xml\generate_big_xml.py">
      <Filter>UI Files</Filter>
    </None>
  </ItemGroup>
  <ItemGroup>
    <Xml Include="uifiles\zeal\optional\EQUI_CharacterSelect.xml">
      <Filter>UI Files</Filter>
    </Xml>
    <Xml Include="uifiles\zeal\EQUI_Tab_Cam.xml">
      <Filter>UI Files</Filter>
    </Xml>
    <Xml Include="uifiles\zeal\EQUI_Tab_Colors.xml">
      <Filter>UI Files</Filter>
    </Xml>
    <Xml Include="uifiles\zeal\EQUI_Tab_FloatingDamage.xml">
      <Filter>UI Files</Filter>
    </Xml>
    <Xml Include="uifiles\zeal\EQUI_Tab_General.xml">
      <Filter>UI Files</Filter>
    </Xml>
    <Xml Include="uifiles\zeal\EQUI_Tab_Map.xml">
      <Filter>UI Files</Filter>
    </Xml>
    <Xml Include="uifiles\zeal\EQUI_Tab_Nameplate.xml">
      <Filter>UI Files</Filter>
    </Xml>
    <Xml Include="uifiles\zeal\EQUI_Tab_TargetRing.xml">
      <Filter>UI Files</Filter>
    </Xml>
    <Xml Include="uifiles\zeal\EQUI_ZealButtonWnd.xml">
      <Filter>UI Files</Filter>
    </Xml>
    <Xml Include="uifiles\zeal\EQUI_ZealInputDialog.xml">
      <Filter>UI Files</Filter>
    </Xml>
    <Xml Include="uifiles\zeal\EQUI_ZealMap.xml">
      <Filter>UI Files</Filter>
    </Xml>
    <Xml Include="uifiles\zeal\EQUI_ZealOptions.xml">
      <Filter>UI Files</Filter>
    </Xml>
    <Xml Include="uifiles\zeal\EQUI_ZoneSelect.xml">
      <Filter>UI Files</Filter>
    </Xml>
    <Xml Include="uifiles\zeal\EQUI_OptionsWindow.xml">
      <Filter>UI Files</Filter>
    </Xml>
  </ItemGroup>
  <ItemGroup>
    <Text Include="uifiles\zeal\spell_info\spell_info.txt">
      <Filter>UI Files</Filter>
    </Text>
  </ItemGroup>
</Project>