//------------------------------------------------------------------------------
// File: MMStream.idl
//
// Desc: MultiMedia streaming interface IDL file.
//
// Copyright (c) 1998 - 2000, Microsoft Corporation.  All rights reserved.
//------------------------------------------------------------------------------


import "unknwn.idl";

cpp_quote("#define MS_ERROR_CODE(x) MAKE_HRESULT(1, FACILITY_ITF, (x) + 0x400)")
cpp_quote("#define MS_SUCCESS_CODE(x) MAKE_HRESULT(0, FACILITY_ITF, x)")
cpp_quote("#define MS_S_PENDING		             MS_SUCCESS_CODE(1)")
cpp_quote("#define MS_S_NOUPDATE                     MS_SUCCESS_CODE(2)")
cpp_quote("#define MS_S_ENDOFSTREAM	             MS_SUCCESS_CODE(3)")

cpp_quote("#define MS_E_SAMPLEALLOC                  MS_ERROR_CODE(1)")
cpp_quote("#define MS_E_PURPOSEID	             MS_ERROR_CODE(2)")
cpp_quote("#define MS_E_NOSTREAM	             MS_ERROR_CODE(3)")
cpp_quote("#define MS_E_NOSEEKING	             MS_ERROR_CODE(4)")
cpp_quote("#define MS_E_INCOMPATIBLE                 MS_ERROR_CODE(5)")
cpp_quote("#define MS_E_BUSY		             MS_ERROR_CODE(6)")
cpp_quote("#define MS_E_NOTINIT		             MS_ERROR_CODE(7)")
cpp_quote("#define MS_E_SOURCEALREADYDEFINED         MS_ERROR_CODE(8)")
cpp_quote("#define MS_E_INVALIDSTREAMTYPE            MS_ERROR_CODE(9)")
cpp_quote("#define MS_E_NOTRUNNING                   MS_ERROR_CODE(10)")

cpp_quote("// {A35FF56A-9FDA-11d0-8FDF-00C04FD9189D}")
cpp_quote("DEFINE_GUID(MSPID_PrimaryVideo, ")
cpp_quote("0xa35ff56a, 0x9fda, 0x11d0, 0x8f, 0xdf, 0x0, 0xc0, 0x4f, 0xd9, 0x18, 0x9d);")

cpp_quote("// {A35FF56B-9FDA-11d0-8FDF-00C04FD9189D}")
cpp_quote("DEFINE_GUID(MSPID_PrimaryAudio,")
cpp_quote("0xa35ff56b, 0x9fda, 0x11d0, 0x8f, 0xdf, 0x0, 0xc0, 0x4f, 0xd9, 0x18, 0x9d);")

cpp_quote("#if(_WIN32_WINNT < 0x0400)")
typedef void (__stdcall * PAPCFUNC)(DWORD_PTR dwParam);
cpp_quote("#endif")

typedef LONGLONG STREAM_TIME;

typedef GUID MSPID;
typedef REFGUID REFMSPID;

typedef enum {
	STREAMTYPE_READ         = 0,
	STREAMTYPE_WRITE        = 1,
	STREAMTYPE_TRANSFORM= 2
} STREAM_TYPE;

typedef enum {
	STREAMSTATE_STOP        = 0,
	STREAMSTATE_RUN         = 1
} STREAM_STATE;


typedef enum {
	COMPSTAT_NOUPDATEOK                     = 0x00000001,
	COMPSTAT_WAIT                                   = 0x00000002,
	COMPSTAT_ABORT                  = 0x00000004
} COMPLETION_STATUS_FLAGS;

//  Flags for GetInformation
enum {
	MMSSF_HASCLOCK                                  = 0x00000001,
	MMSSF_SUPPORTSEEK                               = 0x00000002,
	MMSSF_ASYNCHRONOUS                              = 0x00000004
};

//  Flags for StreamSample::Update
enum {
		SSUPDATE_ASYNC              = 0x00000001,
		SSUPDATE_CONTINUOUS                     = 0x00000002
};

interface IMultiMediaStream;
interface IMediaStream;
interface IStreamSample;

// IMultiMediaStream interface
[
object,
local,
uuid(B502D1BC-9A57-11d0-8FDE-00C04FD9189D),
pointer_default(unique)
]
interface IMultiMediaStream : IUnknown {

	HRESULT GetInformation(
		[out]  DWORD *pdwFlags,
		[out]  STREAM_TYPE *pStreamType);

	HRESULT GetMediaStream(
		[in] REFMSPID idPurpose,
		[out] IMediaStream **ppMediaStream);

	HRESULT EnumMediaStreams(
		[in] long Index,
		[out] IMediaStream **ppMediaStream);

	HRESULT GetState(
		[out] STREAM_STATE *pCurrentState);

	HRESULT SetState(
		[in] STREAM_STATE NewState);

	HRESULT GetTime(
		[out] STREAM_TIME *pCurrentTime);

	HRESULT GetDuration(
		[out] STREAM_TIME *pDuration);

	HRESULT Seek(
		[in] STREAM_TIME SeekTime);

	HRESULT GetEndOfStreamEventHandle(
		[out] HANDLE *phEOS);
};


// IMediaStream interface
[
object,
uuid(B502D1BD-9A57-11d0-8FDE-00C04FD9189D),
pointer_default(unique)
]
interface IMediaStream : IUnknown {
	
	HRESULT GetMultiMediaStream(
		[out] IMultiMediaStream **ppMultiMediaStream);

	HRESULT GetInformation(
		[out]  MSPID *pPurposeId,
		[out]  STREAM_TYPE *pType);

	HRESULT SetSameFormat(
		[in] IMediaStream *pStreamThatHasDesiredFormat,
		[in] DWORD dwFlags);

	HRESULT AllocateSample(
		[in]  DWORD dwFlags,
		[out] IStreamSample **ppSample);
	

	HRESULT CreateSharedSample(
		[in]  IStreamSample *pExistingSample,
		[in]  DWORD dwFlags,
		[out] IStreamSample **ppNewSample);

	HRESULT SendEndOfStream(DWORD dwFlags);
};


// IStreamSample interface
[
object,
local,
uuid(B502D1BE-9A57-11d0-8FDE-00C04FD9189D),
pointer_default(unique)
]
interface IStreamSample : IUnknown {
	
	HRESULT GetMediaStream(
		[in] IMediaStream **ppMediaStream);

	HRESULT GetSampleTimes(
		[out]  STREAM_TIME * pStartTime,
		[out]  STREAM_TIME * pEndTime,
		[out]  STREAM_TIME * pCurrentTime);

	HRESULT SetSampleTimes(
		[in]  const STREAM_TIME *pStartTime,
		[in]  const STREAM_TIME *pEndTime);

	HRESULT Update(
		[in]            DWORD dwFlags,
		[in]  HANDLE hEvent,
		[in]  PAPCFUNC pfnAPC,
		[in]  DWORD_PTR dwAPCData);

	HRESULT CompletionStatus(
		[in] DWORD dwFlags,
		[in] DWORD dwMilliseconds);
};




