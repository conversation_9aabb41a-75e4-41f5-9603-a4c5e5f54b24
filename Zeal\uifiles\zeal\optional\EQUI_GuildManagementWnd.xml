<?xml version="1.0" encoding="us-ascii"?>
<XML ID="EQInterfaceDefinitionLanguage">
  <Schema xmlns="EverQuestData" xmlns:dt="EverQuestDataTypes" />
  <Label item="GT_GuildNameLabel">
    <ScreenID>GuildName</ScreenID>
    <RelativePosition>true</RelativePosition>
    <AutoStretch>true</AutoStretch>
    <LeftAnchorOffset>4</LeftAnchorOffset>
    <TopAnchorOffset>4</TopAnchorOffset>
    <RightAnchorOffset>4</RightAnchorOffset>
    <BottomAnchorOffset>24</BottomAnchorOffset>
    <TopAnchorToTop>true</TopAnchorToTop>
    <BottomAnchorToTop>true</BottomAnchorToTop>
    <LeftAnchorToLeft>true</LeftAnchorToLeft>
    <RightAnchorToLeft>false</RightAnchorToLeft>
    <Text>Guildname of the Guildname</Text>
    <TextColor>
      <R>255</R>
      <G>255</G>
      <B>255</B>
    </TextColor>
    <NoWrap>true</NoWrap>
    <AlignCenter>true</AlignCenter>
    <AlignRight>false</AlignRight>
  </Label>
  <Button item="GT_DumpButton">
    <ScreenID>DumpButton</ScreenID>
    <RelativePosition>true</RelativePosition>
    <AutoStretch>true</AutoStretch>
    <LeftAnchorOffset>74</LeftAnchorOffset>
    <TopAnchorOffset>4</TopAnchorOffset>
    <RightAnchorOffset>4</RightAnchorOffset>
    <BottomAnchorOffset>24</BottomAnchorOffset>
    <TopAnchorToTop>true</TopAnchorToTop>
    <BottomAnchorToTop>true</BottomAnchorToTop>
    <LeftAnchorToLeft>false</LeftAnchorToLeft>
    <RightAnchorToLeft>false</RightAnchorToLeft>
    <Style_Transparent>false</Style_Transparent>
    <Style_Checkbox>false</Style_Checkbox>
    <TooltipReference>Dump your guild info to a file.</TooltipReference>
    <Text>Dump</Text>
    <TextColor>
      <R>255</R>
      <G>255</G>
      <B>255</B>
    </TextColor>
    <ButtonDrawTemplate>
      <Normal>A_SmallBtnNormal</Normal>
      <Pressed>A_SmallBtnPressed</Pressed>
      <Flyby>A_SmallBtnFlyby</Flyby>
      <Disabled>A_SmallBtnDisabled</Disabled>
      <PressedFlyby>A_SmallBtnPressedFlyby</PressedFlyby>
    </ButtonDrawTemplate>
  </Button>
  <Listbox item="GT_MemberList">
    <ScreenID>MemberList</ScreenID>
    <DrawTemplate>WDT_Inner</DrawTemplate>
    <TooltipReference>List of Guild members.</TooltipReference>
    <AutoStretch>true</AutoStretch>
    <LeftAnchorOffset>4</LeftAnchorOffset>
    <TopAnchorOffset>4</TopAnchorOffset>
    <RightAnchorOffset>4</RightAnchorOffset>
    <BottomAnchorOffset>84</BottomAnchorOffset>
    <TopAnchorToTop>true</TopAnchorToTop>
    <BottomAnchorToTop>false</BottomAnchorToTop>
    <LeftAnchorToLeft>true</LeftAnchorToLeft>
    <RightAnchorToLeft>false</RightAnchorToLeft>
    <TextColor>
      <R>255</R>
      <G>255</G>
      <B>255</B>
    </TextColor>
    <Style_Border>true</Style_Border>
    <Style_VScroll>true</Style_VScroll>
    <Columns>
      <Width>100</Width>
      <Heading>Name</Heading>
    </Columns>
    <Columns>
      <Width>45</Width>
      <Heading>Lvl</Heading>
    </Columns>
    <Columns>
      <Width>60</Width>
      <Heading>Class</Heading>
    </Columns>
    <Columns>
      <Width>45</Width>
      <Heading>Rank</Heading>
    </Columns>
    <Columns>
      <Width>80</Width>
      <Heading>Last On</Heading>
    </Columns>
    <Columns>
      <Width>100</Width>
      <Heading>Zone</Heading>
    </Columns>
    <Columns>
      <Width>120</Width>
      <Heading>Public Note</Heading>
    </Columns>
  </Listbox>
  <Label item="GT_MOTDLabel">
    <ScreenID />
    <RelativePosition>true</RelativePosition>
    <AutoStretch>true</AutoStretch>
    <LeftAnchorOffset>4</LeftAnchorOffset>
    <TopAnchorOffset>80</TopAnchorOffset>
    <RightAnchorOffset>90</RightAnchorOffset>
    <BottomAnchorOffset>62</BottomAnchorOffset>
    <TopAnchorToTop>false</TopAnchorToTop>
    <BottomAnchorToTop>false</BottomAnchorToTop>
    <LeftAnchorToLeft>true</LeftAnchorToLeft>
    <RightAnchorToLeft>true</RightAnchorToLeft>
    <Text>MOTD:</Text>
    <TextColor>
      <R>255</R>
      <G>255</G>
      <B>255</B>
    </TextColor>
    <NoWrap>true</NoWrap>
    <AlignCenter>false</AlignCenter>
    <AlignRight>false</AlignRight>
  </Label>
  <Label item="GT_MOTDAuthorLabel">
    <ScreenID>MOTDAuthorLabel</ScreenID>
    <RelativePosition>true</RelativePosition>
    <AutoStretch>true</AutoStretch>
    <LeftAnchorOffset>4</LeftAnchorOffset>
    <TopAnchorOffset>50</TopAnchorOffset>
    <RightAnchorOffset>90</RightAnchorOffset>
    <BottomAnchorOffset>32</BottomAnchorOffset>
    <TopAnchorToTop>false</TopAnchorToTop>
    <BottomAnchorToTop>false</BottomAnchorToTop>
    <LeftAnchorToLeft>true</LeftAnchorToLeft>
    <RightAnchorToLeft>true</RightAnchorToLeft>
    <Text />
    <TextColor>
      <R>255</R>
      <G>255</G>
      <B>255</B>
    </TextColor>
    <NoWrap>true</NoWrap>
    <AlignCenter>false</AlignCenter>
    <AlignRight>false</AlignRight>
  </Label>
  <Editbox item="GT_MOTDInput">
    <ScreenID>MOTDInput</ScreenID>
    <DrawTemplate>WDT_Inner</DrawTemplate>
    <Style_VScroll>true</Style_VScroll>
    <RelativePosition>true</RelativePosition>
    <TooltipReference>This is your Guild's Message of the Day.</TooltipReference>
    <Style_Border>true</Style_Border>
    <AutoStretch>true</AutoStretch>
    <LeftAnchorOffset>98</LeftAnchorOffset>
    <TopAnchorOffset>80</TopAnchorOffset>
    <RightAnchorOffset>68</RightAnchorOffset>
    <BottomAnchorOffset>32</BottomAnchorOffset>
    <TopAnchorToTop>false</TopAnchorToTop>
    <BottomAnchorToTop>false</BottomAnchorToTop>
    <LeftAnchorToLeft>true</LeftAnchorToLeft>
    <RightAnchorToLeft>false</RightAnchorToLeft>
    <TextColor>
      <R>255</R>
      <G>255</G>
      <B>255</B>
    </TextColor>
    <Style_Multiline>true</Style_Multiline>
  </Editbox>
  <Button item="GT_UpdateMOTDButton">
    <ScreenID>UpdateMOTDButton</ScreenID>
    <RelativePosition>true</RelativePosition>
    <AutoStretch>true</AutoStretch>
    <LeftAnchorOffset>64</LeftAnchorOffset>
    <TopAnchorOffset>68</TopAnchorOffset>
    <RightAnchorOffset>4</RightAnchorOffset>
    <BottomAnchorOffset>45</BottomAnchorOffset>
    <TopAnchorToTop>false</TopAnchorToTop>
    <BottomAnchorToTop>false</BottomAnchorToTop>
    <LeftAnchorToLeft>false</LeftAnchorToLeft>
    <RightAnchorToLeft>false</RightAnchorToLeft>
    <Style_Transparent>false</Style_Transparent>
    <Style_Checkbox>false</Style_Checkbox>
    <TooltipReference>Update the MOTD for your guild.</TooltipReference>
    <Text>Update</Text>
    <TextColor>
      <R>255</R>
      <G>255</G>
      <B>255</B>
    </TextColor>
    <ButtonDrawTemplate>
      <Normal>A_SmallBtnNormal</Normal>
      <Pressed>A_SmallBtnPressed</Pressed>
      <Flyby>A_SmallBtnFlyby</Flyby>
      <Disabled>A_SmallBtnDisabled</Disabled>
      <PressedFlyby>A_SmallBtnPressedFlyby</PressedFlyby>
    </ButtonDrawTemplate>
  </Button>
  <STMLbox item="GT_MOTDViewer">
    <ScreenID>MOTDViewer</ScreenID>
    <DrawTemplate>WDT_Inner</DrawTemplate>
    <RelativePosition>true</RelativePosition>
    <Style_VScroll>true</Style_VScroll>
    <TooltipReference>This is your Guild's Message of the Day.</TooltipReference>
    <AutoStretch>true</AutoStretch>
    <LeftAnchorOffset>98</LeftAnchorOffset>
    <TopAnchorOffset>80</TopAnchorOffset>
    <RightAnchorOffset>4</RightAnchorOffset>
    <BottomAnchorOffset>32</BottomAnchorOffset>
    <TopAnchorToTop>false</TopAnchorToTop>
    <BottomAnchorToTop>false</BottomAnchorToTop>
    <LeftAnchorToLeft>true</LeftAnchorToLeft>
    <RightAnchorToLeft>false</RightAnchorToLeft>
    <Style_Border>true</Style_Border>
  </STMLbox>
  <Button item="GT_MakeLeaderButton">
    <ScreenID>MakeLeaderButton</ScreenID>
    <RelativePosition>true</RelativePosition>
    <AutoStretch>true</AutoStretch>
    <LeftAnchorOffset>4</LeftAnchorOffset>
    <TopAnchorOffset>28</TopAnchorOffset>
    <RightAnchorOffset>104</RightAnchorOffset>
    <BottomAnchorOffset>4</BottomAnchorOffset>
    <TopAnchorToTop>false</TopAnchorToTop>
    <BottomAnchorToTop>false</BottomAnchorToTop>
    <LeftAnchorToLeft>true</LeftAnchorToLeft>
    <RightAnchorToLeft>true</RightAnchorToLeft>
    <Style_Transparent>false</Style_Transparent>
    <Style_Checkbox>false</Style_Checkbox>
    <TooltipReference>Make the selected Player the new Guild Leader (Guild Leader only).</TooltipReference>
    <Text>Make Leader</Text>
    <TextColor>
      <R>255</R>
      <G>255</G>
      <B>255</B>
    </TextColor>
    <ButtonDrawTemplate>
      <Normal>A_SmallBtnNormal</Normal>
      <Pressed>A_SmallBtnPressed</Pressed>
      <Flyby>A_SmallBtnFlyby</Flyby>
      <Disabled>A_SmallBtnDisabled</Disabled>
      <PressedFlyby>A_SmallBtnPressedFlyby</PressedFlyby>
    </ButtonDrawTemplate>
  </Button>
  <Button item="GT_PromoteButton">
    <ScreenID>PromoteButton</ScreenID>
    <RelativePosition>true</RelativePosition>
    <AutoStretch>true</AutoStretch>
    <LeftAnchorOffset>108</LeftAnchorOffset>
    <TopAnchorOffset>28</TopAnchorOffset>
    <RightAnchorOffset>188</RightAnchorOffset>
    <BottomAnchorOffset>4</BottomAnchorOffset>
    <TopAnchorToTop>false</TopAnchorToTop>
    <BottomAnchorToTop>false</BottomAnchorToTop>
    <LeftAnchorToLeft>true</LeftAnchorToLeft>
    <RightAnchorToLeft>true</RightAnchorToLeft>
    <Style_Transparent>false</Style_Transparent>
    <Style_Checkbox>false</Style_Checkbox>
    <TooltipReference>Promote the selected player to Officer (GuildLeader only), player must be targetted.</TooltipReference>
    <Text>Promote</Text>
    <TextColor>
      <R>255</R>
      <G>255</G>
      <B>255</B>
    </TextColor>
    <ButtonDrawTemplate>
      <Normal>A_SmallBtnNormal</Normal>
      <Pressed>A_SmallBtnPressed</Pressed>
      <Flyby>A_SmallBtnFlyby</Flyby>
      <Disabled>A_SmallBtnDisabled</Disabled>
      <PressedFlyby>A_SmallBtnPressedFlyby</PressedFlyby>
    </ButtonDrawTemplate>
  </Button>
  <Button item="GT_DemoteButton">
    <ScreenID>DemoteButton</ScreenID>
    <RelativePosition>true</RelativePosition>
    <AutoStretch>true</AutoStretch>
    <LeftAnchorOffset>192</LeftAnchorOffset>
    <TopAnchorOffset>28</TopAnchorOffset>
    <RightAnchorOffset>272</RightAnchorOffset>
    <BottomAnchorOffset>4</BottomAnchorOffset>
    <TopAnchorToTop>false</TopAnchorToTop>
    <BottomAnchorToTop>false</BottomAnchorToTop>
    <LeftAnchorToLeft>true</LeftAnchorToLeft>
    <RightAnchorToLeft>true</RightAnchorToLeft>
    <Style_Transparent>false</Style_Transparent>
    <Style_Checkbox>false</Style_Checkbox>
    <TooltipReference>Demote the selected player from Officer to Member (GuildLeader or self only).</TooltipReference>
    <Text>Demote</Text>
    <TextColor>
      <R>255</R>
      <G>255</G>
      <B>255</B>
    </TextColor>
    <ButtonDrawTemplate>
      <Normal>A_SmallBtnNormal</Normal>
      <Pressed>A_SmallBtnPressed</Pressed>
      <Flyby>A_SmallBtnFlyby</Flyby>
      <Disabled>A_SmallBtnDisabled</Disabled>
      <PressedFlyby>A_SmallBtnPressedFlyby</PressedFlyby>
    </ButtonDrawTemplate>
  </Button>
  <Button item="GT_RemoveButton">
    <ScreenID>RemoveButton</ScreenID>
    <RelativePosition>true</RelativePosition>
    <AutoStretch>true</AutoStretch>
    <LeftAnchorOffset>277</LeftAnchorOffset>
    <TopAnchorOffset>28</TopAnchorOffset>
    <RightAnchorOffset>357</RightAnchorOffset>
    <BottomAnchorOffset>4</BottomAnchorOffset>
    <TopAnchorToTop>false</TopAnchorToTop>
    <BottomAnchorToTop>false</BottomAnchorToTop>
    <LeftAnchorToLeft>true</LeftAnchorToLeft>
    <RightAnchorToLeft>true</RightAnchorToLeft>
    <Style_Transparent>false</Style_Transparent>
    <Style_Checkbox>false</Style_Checkbox>
    <TooltipReference>Remove the player from the guild  (Higher rank or self only).</TooltipReference>
    <Text>Remove</Text>
    <TextColor>
      <R>255</R>
      <G>255</G>
      <B>255</B>
    </TextColor>
    <ButtonDrawTemplate>
      <Normal>A_SmallBtnNormal</Normal>
      <Pressed>A_SmallBtnPressed</Pressed>
      <Flyby>A_SmallBtnFlyby</Flyby>
      <Disabled>A_SmallBtnDisabled</Disabled>
      <PressedFlyby>A_SmallBtnPressedFlyby</PressedFlyby>
    </ButtonDrawTemplate>
  </Button>
  <Button item="GT_BankerFlagButton">
    <ScreenID>BankerFlagButton</ScreenID>
    <RelativePosition>true</RelativePosition>
    <AutoStretch>true</AutoStretch>
    <LeftAnchorOffset>380</LeftAnchorOffset>
    <TopAnchorOffset>24</TopAnchorOffset>
    <RightAnchorOffset>396</RightAnchorOffset>
    <BottomAnchorOffset>8</BottomAnchorOffset>
    <TopAnchorToTop>false</TopAnchorToTop>
    <BottomAnchorToTop>false</BottomAnchorToTop>
    <LeftAnchorToLeft>true</LeftAnchorToLeft>
    <RightAnchorToLeft>true</RightAnchorToLeft>
    <Style_Transparent>false</Style_Transparent>
    <Style_Checkbox>true</Style_Checkbox>
    <Text />
    <TextColor>
      <R>255</R>
      <G>255</G>
      <B>255</B>
    </TextColor>
    <ButtonDrawTemplate>
      <Normal>A_CheckBoxNormal</Normal>
      <Pressed>A_CheckBoxPressed</Pressed>
    </ButtonDrawTemplate>
  </Button>
  <Label item="GT_BankerFlagLabel">
    <ScreenID />
    <RelativePosition>true</RelativePosition>
    <AutoStretch>true</AutoStretch>
    <LeftAnchorOffset>400</LeftAnchorOffset>
    <TopAnchorOffset>24</TopAnchorOffset>
    <RightAnchorOffset>440</RightAnchorOffset>
    <BottomAnchorOffset>4</BottomAnchorOffset>
    <TopAnchorToTop>false</TopAnchorToTop>
    <BottomAnchorToTop>false</BottomAnchorToTop>
    <LeftAnchorToLeft>true</LeftAnchorToLeft>
    <RightAnchorToLeft>true</RightAnchorToLeft>
    <Text>Banker</Text>
    <TextColor>
      <R>255</R>
      <G>255</G>
      <B>255</B>
    </TextColor>
    <NoWrap>true</NoWrap>
    <AlignCenter>false</AlignCenter>
    <AlignRight>false</AlignRight>
  </Label>
  <Page item="GT_MemberPage">
    <ScreenID>MemberPage</ScreenID>
    <RelativePosition>false</RelativePosition>
    <DrawTemplate>WDT_Def</DrawTemplate>
    <TabText>Roster</TabText>
    <TabTextColor>
      <R>255</R>
      <G>255</G>
      <B>255</B>
    </TabTextColor>
    <TabTextActiveColor>
      <R>255</R>
      <G>255</G>
      <B>0</B>
    </TabTextActiveColor>
    <Pieces>GT_MemberList</Pieces>
    <Pieces>GT_MOTDLabel</Pieces>
    <Pieces>GT_MOTDInput</Pieces>
    <Pieces>GT_UpdateMOTDButton</Pieces>
    <Pieces>GT_MOTDViewer</Pieces>
    <Pieces>GT_MakeLeaderButton</Pieces>
    <Pieces>GT_PromoteButton</Pieces>
    <Pieces>GT_DemoteButton</Pieces>
    <Pieces>GT_RemoveButton</Pieces>
    <Pieces>GT_BankerFlagButton</Pieces>
    <Pieces>GT_BankerFlagLabel</Pieces>
    <Pieces>GT_MOTDAuthorLabel</Pieces>
    <Style_Sizable>true</Style_Sizable>
  </Page>
  <Listbox item="GT_NoteList">
    <ScreenID>NoteList</ScreenID>
    <DrawTemplate>WDT_Inner</DrawTemplate>
    <TooltipReference>List of Guild members.</TooltipReference>
    <AutoStretch>true</AutoStretch>
    <LeftAnchorOffset>4</LeftAnchorOffset>
    <TopAnchorOffset>4</TopAnchorOffset>
    <RightAnchorOffset>4</RightAnchorOffset>
    <BottomAnchorOffset>60</BottomAnchorOffset>
    <TopAnchorToTop>true</TopAnchorToTop>
    <BottomAnchorToTop>false</BottomAnchorToTop>
    <LeftAnchorToLeft>true</LeftAnchorToLeft>
    <RightAnchorToLeft>false</RightAnchorToLeft>
    <TextColor>
      <R>255</R>
      <G>255</G>
      <B>255</B>
    </TextColor>
    <Style_Border>true</Style_Border>
    <Style_VScroll>true</Style_VScroll>
    <Columns>
      <Width>100</Width>
      <Heading>Name</Heading>
    </Columns>
    <Columns>
      <Width>240</Width>
      <Heading>Public Note</Heading>
    </Columns>
    <Columns>
      <Width>200</Width>
      <Heading>Personal Note</Heading>
    </Columns>
  </Listbox>
  <Label item="GT_PublicLabel">
    <ScreenID />
    <RelativePosition>true</RelativePosition>
    <AutoStretch>true</AutoStretch>
    <LeftAnchorOffset>4</LeftAnchorOffset>
    <TopAnchorOffset>56</TopAnchorOffset>
    <RightAnchorOffset>90</RightAnchorOffset>
    <BottomAnchorOffset>32</BottomAnchorOffset>
    <TopAnchorToTop>false</TopAnchorToTop>
    <BottomAnchorToTop>false</BottomAnchorToTop>
    <LeftAnchorToLeft>true</LeftAnchorToLeft>
    <RightAnchorToLeft>true</RightAnchorToLeft>
    <Text>Public Note:</Text>
    <TextColor>
      <R>255</R>
      <G>255</G>
      <B>255</B>
    </TextColor>
    <NoWrap>true</NoWrap>
    <AlignCenter>false</AlignCenter>
    <AlignRight>false</AlignRight>
  </Label>
  <Editbox item="GT_PublicInput">
    <ScreenID>PublicInput</ScreenID>
    <DrawTemplate>WDT_Inner</DrawTemplate>
    <RelativePosition>true</RelativePosition>
    <TooltipReference>This note is viewable by the entire guild.</TooltipReference>
    <Style_Border>true</Style_Border>
    <AutoStretch>true</AutoStretch>
    <LeftAnchorOffset>98</LeftAnchorOffset>
    <TopAnchorOffset>58</TopAnchorOffset>
    <RightAnchorOffset>68</RightAnchorOffset>
    <BottomAnchorOffset>32</BottomAnchorOffset>
    <TopAnchorToTop>false</TopAnchorToTop>
    <BottomAnchorToTop>false</BottomAnchorToTop>
    <LeftAnchorToLeft>true</LeftAnchorToLeft>
    <RightAnchorToLeft>false</RightAnchorToLeft>
    <TextColor>
      <R>255</R>
      <G>255</G>
      <B>255</B>
    </TextColor>
  </Editbox>
  <Button item="GT_UpdatePublicButton">
    <ScreenID>UpdatePublicButton</ScreenID>
    <RelativePosition>true</RelativePosition>
    <AutoStretch>true</AutoStretch>
    <LeftAnchorOffset>64</LeftAnchorOffset>
    <TopAnchorOffset>58</TopAnchorOffset>
    <RightAnchorOffset>4</RightAnchorOffset>
    <BottomAnchorOffset>32</BottomAnchorOffset>
    <TopAnchorToTop>false</TopAnchorToTop>
    <BottomAnchorToTop>false</BottomAnchorToTop>
    <LeftAnchorToLeft>false</LeftAnchorToLeft>
    <RightAnchorToLeft>false</RightAnchorToLeft>
    <Style_Transparent>false</Style_Transparent>
    <Style_Checkbox>false</Style_Checkbox>
    <TooltipReference>Change the public note to the text input (Officer only).</TooltipReference>
    <Text>Update</Text>
    <TextColor>
      <R>255</R>
      <G>255</G>
      <B>255</B>
    </TextColor>
    <ButtonDrawTemplate>
      <Normal>A_SmallBtnNormal</Normal>
      <Pressed>A_SmallBtnPressed</Pressed>
      <Flyby>A_SmallBtnFlyby</Flyby>
      <Disabled>A_SmallBtnDisabled</Disabled>
      <PressedFlyby>A_SmallBtnPressedFlyby</PressedFlyby>
    </ButtonDrawTemplate>
  </Button>
  <Label item="GT_PersonalLabel">
    <ScreenID />
    <RelativePosition>true</RelativePosition>
    <AutoStretch>true</AutoStretch>
    <LeftAnchorOffset>4</LeftAnchorOffset>
    <TopAnchorOffset>26</TopAnchorOffset>
    <RightAnchorOffset>90</RightAnchorOffset>
    <BottomAnchorOffset>2</BottomAnchorOffset>
    <TopAnchorToTop>false</TopAnchorToTop>
    <BottomAnchorToTop>false</BottomAnchorToTop>
    <LeftAnchorToLeft>true</LeftAnchorToLeft>
    <RightAnchorToLeft>true</RightAnchorToLeft>
    <Text>Personal Note:</Text>
    <TextColor>
      <R>255</R>
      <G>255</G>
      <B>255</B>
    </TextColor>
    <NoWrap>true</NoWrap>
    <AlignCenter>false</AlignCenter>
    <AlignRight>false</AlignRight>
  </Label>
  <Editbox item="GT_PersonalInput">
    <ScreenID>PersonalInput</ScreenID>
    <DrawTemplate>WDT_Inner</DrawTemplate>
    <RelativePosition>true</RelativePosition>
    <TooltipReference>This note is viewable only by you.</TooltipReference>
    <Style_Border>true</Style_Border>
    <AutoStretch>true</AutoStretch>
    <LeftAnchorOffset>98</LeftAnchorOffset>
    <TopAnchorOffset>28</TopAnchorOffset>
    <RightAnchorOffset>68</RightAnchorOffset>
    <BottomAnchorOffset>2</BottomAnchorOffset>
    <TopAnchorToTop>false</TopAnchorToTop>
    <BottomAnchorToTop>false</BottomAnchorToTop>
    <LeftAnchorToLeft>true</LeftAnchorToLeft>
    <RightAnchorToLeft>false</RightAnchorToLeft>
    <TextColor>
      <R>255</R>
      <G>255</G>
      <B>255</B>
    </TextColor>
  </Editbox>
  <Button item="GT_UpdatePersonalButton">
    <ScreenID>UpdatePersonalButton</ScreenID>
    <RelativePosition>true</RelativePosition>
    <AutoStretch>true</AutoStretch>
    <LeftAnchorOffset>64</LeftAnchorOffset>
    <TopAnchorOffset>28</TopAnchorOffset>
    <RightAnchorOffset>4</RightAnchorOffset>
    <BottomAnchorOffset>2</BottomAnchorOffset>
    <TopAnchorToTop>false</TopAnchorToTop>
    <BottomAnchorToTop>false</BottomAnchorToTop>
    <LeftAnchorToLeft>false</LeftAnchorToLeft>
    <RightAnchorToLeft>false</RightAnchorToLeft>
    <Style_Transparent>false</Style_Transparent>
    <Style_Checkbox>false</Style_Checkbox>
    <TooltipReference>Change the personal note to the text input.</TooltipReference>
    <Text>Update</Text>
    <TextColor>
      <R>255</R>
      <G>255</G>
      <B>255</B>
    </TextColor>
    <ButtonDrawTemplate>
      <Normal>A_SmallBtnNormal</Normal>
      <Pressed>A_SmallBtnPressed</Pressed>
      <Flyby>A_SmallBtnFlyby</Flyby>
      <Disabled>A_SmallBtnDisabled</Disabled>
      <PressedFlyby>A_SmallBtnPressedFlyby</PressedFlyby>
    </ButtonDrawTemplate>
  </Button>
  <Page item="GT_NotePage">
    <ScreenID>NotePage</ScreenID>
    <RelativePosition>false</RelativePosition>
    <DrawTemplate>WDT_Def</DrawTemplate>
    <TabText>Notes</TabText>
    <TabTextColor>
      <R>255</R>
      <G>255</G>
      <B>255</B>
    </TabTextColor>
    <TabTextActiveColor>
      <R>255</R>
      <G>255</G>
      <B>0</B>
    </TabTextActiveColor>
    <Pieces>GT_NoteList</Pieces>
    <Pieces>GT_PublicLabel</Pieces>
    <Pieces>GT_PublicInput</Pieces>
    <Pieces>GT_UpdatePublicButton</Pieces>
    <Pieces>GT_PersonalLabel</Pieces>
    <Pieces>GT_PersonalInput</Pieces>
    <Pieces>GT_UpdatePersonalButton</Pieces>
    <Style_Sizable>true</Style_Sizable>
  </Page>
  <Listbox item="GT_TributeList">
    <ScreenID>TributeList</ScreenID>
    <DrawTemplate>WDT_Inner</DrawTemplate>
    <TooltipReference>List of Guild members.</TooltipReference>
    <AutoStretch>true</AutoStretch>
    <LeftAnchorOffset>4</LeftAnchorOffset>
    <TopAnchorOffset>4</TopAnchorOffset>
    <RightAnchorOffset>4</RightAnchorOffset>
    <BottomAnchorOffset>28</BottomAnchorOffset>
    <TopAnchorToTop>true</TopAnchorToTop>
    <BottomAnchorToTop>false</BottomAnchorToTop>
    <LeftAnchorToLeft>true</LeftAnchorToLeft>
    <RightAnchorToLeft>false</RightAnchorToLeft>
    <TextColor>
      <R>255</R>
      <G>255</G>
      <B>255</B>
    </TextColor>
    <Style_Border>true</Style_Border>
    <Style_VScroll>true</Style_VScroll>
    <Columns>
      <Width>100</Width>
      <Heading>Name</Heading>
    </Columns>
    <Columns>
      <Width>45</Width>
      <Heading>Status</Heading>
    </Columns>
    <Columns>
      <Width>70</Width>
      <Heading>Donations</Heading>
    </Columns>
    <Columns>
      <Width>130</Width>
      <Heading>Last Donation</Heading>
    </Columns>
  </Listbox>
  <Button item="GT_OptOutButton">
    <ScreenID>OptOutButton</ScreenID>
    <RelativePosition>true</RelativePosition>
    <AutoStretch>true</AutoStretch>
    <LeftAnchorOffset>4</LeftAnchorOffset>
    <TopAnchorOffset>24</TopAnchorOffset>
    <RightAnchorOffset>64</RightAnchorOffset>
    <BottomAnchorOffset>2</BottomAnchorOffset>
    <TopAnchorToTop>false</TopAnchorToTop>
    <BottomAnchorToTop>false</BottomAnchorToTop>
    <LeftAnchorToLeft>true</LeftAnchorToLeft>
    <RightAnchorToLeft>true</RightAnchorToLeft>
    <Style_Transparent>false</Style_Transparent>
    <Style_Checkbox>false</Style_Checkbox>
    <Text>Opt Out</Text>
    <TooltipReference>Press this button to opt out of Guild Tribute benefits.</TooltipReference>
    <TextColor>
      <R>255</R>
      <G>255</G>
      <B>255</B>
    </TextColor>
    <ButtonDrawTemplate>
      <Normal>A_SmallBtnNormal</Normal>
      <Pressed>A_SmallBtnPressed</Pressed>
      <Flyby>A_SmallBtnFlyby</Flyby>
      <Disabled>A_SmallBtnDisabled</Disabled>
      <PressedFlyby>A_SmallBtnPressedFlyby</PressedFlyby>
    </ButtonDrawTemplate>
  </Button>
  <Label item="GT_TributePoolLabel">
    <ScreenID />
    <RelativePosition>true</RelativePosition>
    <AutoStretch>true</AutoStretch>
    <LeftAnchorOffset>160</LeftAnchorOffset>
    <TopAnchorOffset>22</TopAnchorOffset>
    <RightAnchorOffset>74</RightAnchorOffset>
    <BottomAnchorOffset>2</BottomAnchorOffset>
    <TopAnchorToTop>false</TopAnchorToTop>
    <BottomAnchorToTop>false</BottomAnchorToTop>
    <LeftAnchorToLeft>false</LeftAnchorToLeft>
    <RightAnchorToLeft>false</RightAnchorToLeft>
    <Text>Tribute Points:</Text>
    <TextColor>
      <R>255</R>
      <G>255</G>
      <B>255</B>
    </TextColor>
    <NoWrap>true</NoWrap>
    <AlignCenter>false</AlignCenter>
    <AlignRight>true</AlignRight>
  </Label>
  <Label item="GT_TributePool">
    <ScreenID>TributePool</ScreenID>&gt;
		<RelativePosition>true</RelativePosition><AutoStretch>true</AutoStretch><LeftAnchorOffset>70</LeftAnchorOffset><TopAnchorOffset>22</TopAnchorOffset><RightAnchorOffset>4</RightAnchorOffset><BottomAnchorOffset>2</BottomAnchorOffset><TopAnchorToTop>false</TopAnchorToTop><BottomAnchorToTop>false</BottomAnchorToTop><LeftAnchorToLeft>false</LeftAnchorToLeft><RightAnchorToLeft>false</RightAnchorToLeft><EQType>122</EQType><TextColor><R>255</R><G>255</G><B>255</B></TextColor><NoWrap>true</NoWrap><AlignCenter>true</AlignCenter><AlignRight>false</AlignRight></Label>
  <Page item="GT_TributePage">
    <ScreenID>TributePage</ScreenID>
    <RelativePosition>false</RelativePosition>
    <DrawTemplate>WDT_Def</DrawTemplate>
    <TabText>Tribute</TabText>
    <TabTextColor>
      <R>255</R>
      <G>255</G>
      <B>255</B>
    </TabTextColor>
    <TabTextActiveColor>
      <R>255</R>
      <G>255</G>
      <B>0</B>
    </TabTextActiveColor>
    <Pieces>GT_TributeList</Pieces>
    <Pieces>GT_OptOutButton</Pieces>
    <Pieces>GT_TributePoolLabel</Pieces>
    <Pieces>GT_TributePool</Pieces>
    <Style_Sizable>true</Style_Sizable>
  </Page>
  <TabBox item="GT_Subwindows">
    <ScreenID>Subwindows</ScreenID>
    <RelativePosition>true</RelativePosition>
    <AutoStretch>true</AutoStretch>
    <LeftAnchorOffset>2</LeftAnchorOffset>
    <TopAnchorOffset>28</TopAnchorOffset>
    <RightAnchorOffset>2</RightAnchorOffset>
    <BottomAnchorOffset>28</BottomAnchorOffset>
    <TopAnchorToTop>true</TopAnchorToTop>
    <BottomAnchorToTop>false</BottomAnchorToTop>
    <LeftAnchorToLeft>true</LeftAnchorToLeft>
    <RightAnchorToLeft>false</RightAnchorToLeft>
    <TabBorderTemplate>FT_DefTabBorder</TabBorderTemplate>
    <PageBorderTemplate>FT_DefPageBorder</PageBorderTemplate>
    <Pages>GT_MemberPage</Pages>
    <Pages>GT_NotePage</Pages>
    <Pages>GT_TributePage</Pages>
  </TabBox>
  <Button item="GT_ShowOfflineButton">
    <ScreenID>ShowOfflineButton</ScreenID>
    <RelativePosition>true</RelativePosition>
    <AutoStretch>true</AutoStretch>
    <LeftAnchorOffset>10</LeftAnchorOffset>
    <TopAnchorOffset>24</TopAnchorOffset>
    <RightAnchorOffset>26</RightAnchorOffset>
    <BottomAnchorOffset>8</BottomAnchorOffset>
    <TopAnchorToTop>false</TopAnchorToTop>
    <BottomAnchorToTop>false</BottomAnchorToTop>
    <LeftAnchorToLeft>true</LeftAnchorToLeft>
    <RightAnchorToLeft>true</RightAnchorToLeft>
    <Style_Transparent>false</Style_Transparent>
    <Style_Checkbox>true</Style_Checkbox>
    <Text />
    <TextColor>
      <R>255</R>
      <G>255</G>
      <B>255</B>
    </TextColor>
    <ButtonDrawTemplate>
      <Normal>A_CheckBoxNormal</Normal>
      <Pressed>A_CheckBoxPressed</Pressed>
    </ButtonDrawTemplate>
  </Button>
  <Label item="GT_ShowOfflineLabel">
    <ScreenID />
    <RelativePosition>true</RelativePosition>
    <AutoStretch>true</AutoStretch>
    <LeftAnchorOffset>30</LeftAnchorOffset>
    <TopAnchorOffset>24</TopAnchorOffset>
    <RightAnchorOffset>128</RightAnchorOffset>
    <BottomAnchorOffset>4</BottomAnchorOffset>
    <TopAnchorToTop>false</TopAnchorToTop>
    <BottomAnchorToTop>false</BottomAnchorToTop>
    <LeftAnchorToLeft>true</LeftAnchorToLeft>
    <RightAnchorToLeft>true</RightAnchorToLeft>
    <Text>Show Offline</Text>
    <TextColor>
      <R>255</R>
      <G>255</G>
      <B>255</B>
    </TextColor>
    <NoWrap>true</NoWrap>
    <AlignCenter>false</AlignCenter>
    <AlignRight>false</AlignRight>
  </Label>
  <Label item="GT_PlayerCountLabelText">
    <ScreenID />
    <RelativePosition>true</RelativePosition>
    <AutoStretch>true</AutoStretch>
    <LeftAnchorOffset>130</LeftAnchorOffset>
    <TopAnchorOffset>25</TopAnchorOffset>
    <RightAnchorOffset>30</RightAnchorOffset>
    <BottomAnchorOffset>5</BottomAnchorOffset>
    <TopAnchorToTop>false</TopAnchorToTop>
    <BottomAnchorToTop>false</BottomAnchorToTop>
    <LeftAnchorToLeft>false</LeftAnchorToLeft>
    <RightAnchorToLeft>false</RightAnchorToLeft>
    <Text>Member Count:</Text>
    <TextColor>
      <R>255</R>
      <G>255</G>
      <B>255</B>
    </TextColor>
    <NoWrap>true</NoWrap>
    <AlignCenter>false</AlignCenter>
    <AlignRight>false</AlignRight>
  </Label>
  <Label item="GT_PlayerCountLabel">
    <ScreenID>PlayerCount</ScreenID>
    <RelativePosition>true</RelativePosition>
    <AutoStretch>true</AutoStretch>
    <LeftAnchorOffset>28</LeftAnchorOffset>
    <TopAnchorOffset>25</TopAnchorOffset>
    <RightAnchorOffset>3</RightAnchorOffset>
    <BottomAnchorOffset>5</BottomAnchorOffset>
    <TopAnchorToTop>false</TopAnchorToTop>
    <BottomAnchorToTop>false</BottomAnchorToTop>
    <LeftAnchorToLeft>false</LeftAnchorToLeft>
    <RightAnchorToLeft>false</RightAnchorToLeft>
    <Text />
    <TextColor>
      <R>255</R>
      <G>255</G>
      <B>255</B>
    </TextColor>
    <NoWrap>true</NoWrap>
    <AlignCenter>false</AlignCenter>
    <AlignRight>false</AlignRight>
  </Label>
  <Screen item="GuildManagementWnd">
    <ScreenID />
    <RelativePosition>false</RelativePosition>
    <Location>
      <X>50</X>
      <Y>100</Y>
    </Location>
    <Size>
      <CX>600</CX>
      <CY>410</CY>
    </Size>
    <Text>Guild Management</Text>
    <Style_VScroll>false</Style_VScroll>
    <Style_HScroll>false</Style_HScroll>
    <Style_Transparent>false</Style_Transparent>
    <TooltipReference>This is the Guild Management Window.</TooltipReference>
    <DrawTemplate>WDT_Rounded</DrawTemplate>
    <Style_Titlebar>true</Style_Titlebar>
    <Style_Closebox>true</Style_Closebox>
    <Style_Minimizebox>true</Style_Minimizebox>
    <Style_Border>true</Style_Border>
    <Style_Sizable>true</Style_Sizable>
    <Pieces>GT_GuildNameLabel</Pieces>
    <Pieces>GT_DumpButton</Pieces>
    <Pieces>GT_Subwindows</Pieces>
    <Pieces>GT_PlayerCountLabelText</Pieces>
    <Pieces>GT_PlayerCountLabel</Pieces>
    <Pieces>GT_ShowOfflineButton</Pieces>
    <Pieces>GT_ShowOfflineLabel</Pieces>
  </Screen>
</XML>